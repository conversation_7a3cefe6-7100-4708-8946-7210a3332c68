#!/usr/bin/env python3
"""
RoxyBrowser + DrissionPage 集成测试
测试两个平台的完美结合
"""

import sys
import time
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from config import DrissionPageConfig
from drissionpage_automation import DrissionPageAutomation

def test_traditional_mode():
    """测试传统 DrissionPage 模式"""
    print("[CONFIG] 测试传统 DrissionPage 模式...")
    
    automation = DrissionPageAutomation()
    
    try:
        # 确保使用传统模式
        automation.config.use_roxybrowser = False
        
        # 启动浏览器
        if automation.init_browser():
            print("[OK] 传统模式启动成功")
            
            # 导航到测试页面
            test_url = "https://httpbin.org/get"
            automation.navigate_to_page(test_url)
            print(f"[OK] 页面导航成功: {test_url}")
            
            # 等待一下
            time.sleep(3)
            
            return True
        else:
            print("[ERROR] 传统模式启动失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] 传统模式测试失败: {str(e)}")
        return False
        
    finally:
        automation.cleanup()

def test_roxybrowser_integration():
    """测试 RoxyBrowser 集成模式"""
    print("\n[FIRE] 测试 RoxyBrowser 集成模式...")

    # 检查配置
    config = DrissionPageConfig()
    if not config.roxybrowser_api_token:
        print("[WARN] 跳过 RoxyBrowser 测试 - 未设置 API Token")
        print("[TIP] 请在 .env 文件中设置: USE_ROXYBROWSER=true 和 ROXYBROWSER_API_TOKEN")
        return True

    # 临时启用 RoxyBrowser 集成
    config.use_roxybrowser = True

    # 创建自动化实例（这时会初始化 RoxyBrowser 集成）
    automation = DrissionPageAutomation()
    automation.config = config  # 使用修改后的配置

    # 手动初始化 RoxyBrowser 集成
    if not automation.roxybrowser_integration:
        from roxybrowser_integration import RoxyBrowserIntegration
        automation.roxybrowser_integration = RoxyBrowserIntegration(automation.config, automation.logger)

    try:
        
        # 启动浏览器
        if automation.init_browser():
            print("[OK] RoxyBrowser 集成模式启动成功")
            
            # 导航到测试页面
            test_url = "https://httpbin.org/get"
            automation.navigate_to_page(test_url)
            print(f"[OK] 页面导航成功: {test_url}")
            
            # 测试指纹检测
            fingerprint_url = "https://httpbin.org/headers"
            automation.navigate_to_page(fingerprint_url)
            print(f"[OK] 指纹检测页面加载成功: {fingerprint_url}")
            
            # 等待一下
            time.sleep(5)
            
            return True
        else:
            print("[ERROR] RoxyBrowser 集成模式启动失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] RoxyBrowser 集成测试失败: {str(e)}")
        return False
        
    finally:
        automation.cleanup()

def test_config_validation():
    """测试配置验证"""
    print("\n[SEARCH] 测试配置验证...")
    
    try:
        config = DrissionPageConfig()
        
        print(f"[OK] 配置加载成功")
        print(f"   [START] RoxyBrowser 集成: {'启用' if config.use_roxybrowser else '禁用'}")
        print(f"   [KEY] API Token: {'已设置' if config.roxybrowser_api_token else '未设置'}")
        print(f"   [WEB] API Host: {config.roxybrowser_api_host}")
        print(f"   [OFFICE] 工作空间ID: {config.roxybrowser_workspace_id}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] 配置验证失败: {str(e)}")
        return False

def test_roxybrowser_api_connection():
    """测试 RoxyBrowser API 连接"""
    print("\n[CONNECT] 测试 RoxyBrowser API 连接...")
    
    try:
        config = DrissionPageConfig()
        
        if not config.roxybrowser_api_token:
            print("[WARN] 跳过 API 连接测试 - 未设置 API Token")
            return True
        
        from roxybrowser_client import RoxyBrowserClient
        
        client = RoxyBrowserClient(
            api_host=config.roxybrowser_api_host,
            api_token=config.roxybrowser_api_token
        )
        
        if client.health_check():
            print("[OK] RoxyBrowser API 连接正常")
            
            # 获取工作空间信息
            workspaces = client.get_workspaces()
            print(f"[OK] 工作空间数量: {workspaces['data']['total']}")
            
            return True
        else:
            print("[ERROR] RoxyBrowser API 连接失败")
            return False
            
    except Exception as e:
        print(f"[ERROR] API 连接测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("[FIRE] DrissionPage + RoxyBrowser 集成测试")
    print("=" * 60)
    
    tests = [
        ("配置验证", test_config_validation),
        ("RoxyBrowser API 连接", test_roxybrowser_api_connection),
        ("传统 DrissionPage 模式", test_traditional_mode),
        ("RoxyBrowser 集成模式", test_roxybrowser_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                passed += 1
                print(f"[OK] {test_name} 测试通过")
            else:
                print(f"[ERROR] {test_name} 测试失败")
                
        except Exception as e:
            print(f"[BOOM] {test_name} 测试异常: {str(e)}")
        
        # 等待一下再进行下一个测试
        if test_name != tests[-1][0]:  # 不是最后一个测试
            print("[WAIT] 等待 3 秒后进行下一个测试...")
            time.sleep(3)
    
    print("\n" + "=" * 60)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("[SUCCESS] 所有测试通过!")
        print("\n[START] 集成系统已准备就绪!")
        print("[TIP] 使用方法:")
        print("   1. 设置 USE_ROXYBROWSER=true 启用 RoxyBrowser 集成")
        print("   2. 设置 USE_ROXYBROWSER=false 使用传统 DrissionPage 模式")
        print("   3. 运行 python run_drissionpage_verification.py")
        return True
    else:
        print("[WARN] 部分测试失败，请检查配置")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n[WARN] 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n[BOOM] 测试异常: {str(e)}")
        sys.exit(1)
