#!/usr/bin/env python3
"""
Ubuntu Snap Firefox 专用修复脚本
"""

import os
import subprocess
import sys

def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def find_real_firefox():
    """查找真实的 Firefox 二进制文件"""
    print("🔍 查找真实的 Firefox 二进制文件...")
    
    # 方法1: 查找 snap Firefox
    success, output, _ = run_command("find /snap/firefox -name firefox -type f 2>/dev/null")
    if success and output:
        paths = output.split('\n')
        for path in paths:
            if path and 'current' in path:  # 优先选择 current 版本
                print(f"🎯 找到 snap Firefox (current): {path}")
                return path
        # 如果没有 current，选择第一个
        if paths[0]:
            print(f"🎯 找到 snap Firefox: {paths[0]}")
            return paths[0]
    
    # 方法2: 查找系统 Firefox
    success, output, _ = run_command("find /usr -name firefox-bin -type f 2>/dev/null")
    if success and output:
        path = output.split('\n')[0]
        print(f"🎯 找到系统 Firefox: {path}")
        return path
    
    # 方法3: 查找 Firefox ESR
    success, output, _ = run_command("find /usr -name firefox-esr -type f 2>/dev/null")
    if success and output:
        path = output.split('\n')[0]
        print(f"🎯 找到 Firefox ESR: {path}")
        return path
    
    # 方法4: 尝试 flatpak
    success, output, _ = run_command("flatpak list | grep firefox")
    if success and output:
        print("🎯 检测到 Flatpak Firefox，但 Selenium 可能不支持")
    
    return None

def test_firefox_binary(firefox_path):
    """测试 Firefox 二进制文件"""
    print(f"🧪 测试 Firefox 二进制文件: {firefox_path}")
    
    # 检查文件是否存在
    if not os.path.exists(firefox_path):
        print("❌ 文件不存在")
        return False
    
    # 检查是否可执行
    if not os.access(firefox_path, os.X_OK):
        print("❌ 文件不可执行")
        return False
    
    # 测试版本
    success, version, error = run_command(f'"{firefox_path}" --version')
    if success:
        print(f"✅ 版本测试成功: {version}")
    else:
        print(f"❌ 版本测试失败: {error}")
        return False
    
    # 测试 headless 模式
    success, _, error = run_command(f'timeout 10 "{firefox_path}" --headless --version')
    if success:
        print("✅ Headless 模式测试成功")
    else:
        print(f"⚠️ Headless 模式测试失败: {error}")
    
    return True

def main():
    print("🦊 Ubuntu Snap Firefox 专用修复")
    print("=" * 40)
    
    # 1. 检查当前 Firefox
    print("🔍 检查当前 Firefox 设置...")
    success, output, _ = run_command("which firefox")
    if success:
        print(f"📍 which firefox: {output}")
        
        # 检查文件类型
        success, file_info, _ = run_command(f"file {output}")
        if success:
            print(f"📄 文件类型: {file_info}")
    
    # 2. 查找真实的 Firefox 二进制文件
    real_firefox = find_real_firefox()
    
    if not real_firefox:
        print("❌ 未找到真实的 Firefox 二进制文件")
        print("\n💡 建议:")
        print("1. 卸载 snap Firefox: sudo snap remove firefox")
        print("2. 安装 apt Firefox: sudo apt install firefox")
        print("3. 或者安装 Firefox ESR: sudo apt install firefox-esr")
        return False
    
    # 3. 测试 Firefox 二进制文件
    if not test_firefox_binary(real_firefox):
        print("❌ Firefox 二进制文件测试失败")
        return False
    
    # 4. 设置环境变量
    print("\n🔧 设置环境变量...")
    os.environ['FIREFOX_BINARY_PATH'] = real_firefox
    
    # 写入到 .bashrc
    bashrc_line = f'export FIREFOX_BINARY_PATH="{real_firefox}"'
    try:
        # 先检查是否已经存在
        with open(os.path.expanduser('~/.bashrc'), 'r') as f:
            content = f.read()
        
        if 'FIREFOX_BINARY_PATH' not in content:
            with open(os.path.expanduser('~/.bashrc'), 'a') as f:
                f.write(f'\n# Real Firefox binary path for automation\n{bashrc_line}\n')
            print(f"✅ 已添加到 ~/.bashrc: {bashrc_line}")
        else:
            print("✅ ~/.bashrc 中已存在 FIREFOX_BINARY_PATH 设置")
    except Exception as e:
        print(f"⚠️ 无法写入 ~/.bashrc: {e}")
    
    # 5. 创建临时测试脚本
    test_script = """
import os
from selenium import webdriver
from selenium.webdriver.firefox.options import Options

try:
    options = Options()
    options.add_argument('--headless')
    firefox_path = os.environ.get('FIREFOX_BINARY_PATH')
    if firefox_path:
        options.binary_location = firefox_path
        print(f"使用 Firefox 路径: {firefox_path}")
    
    driver = webdriver.Firefox(options=options)
    print("✅ Selenium Firefox 测试成功!")
    driver.quit()
except Exception as e:
    print(f"❌ Selenium Firefox 测试失败: {e}")
"""
    
    with open('/tmp/test_selenium_firefox.py', 'w') as f:
        f.write(test_script)
    
    print("\n🧪 运行 Selenium 测试...")
    success, output, error = run_command(f"cd {os.getcwd()} && python3 /tmp/test_selenium_firefox.py")
    if success:
        print(output)
    else:
        print(f"❌ Selenium 测试失败: {error}")
    
    print("\n✅ 修复完成！")
    print(f"\n🎯 真实 Firefox 路径: {real_firefox}")
    print("\n🚀 现在可以运行:")
    print("   source ~/.bashrc")
    print("   python3 run_firefox_fixed.py")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"\n❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
