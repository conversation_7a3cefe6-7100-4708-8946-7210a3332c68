import sys
import time
from pathlib import Path

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import DrissionPageAutomation
from handlers import AugmentAuth, TokenStorage, OneMailHandler

def run_drissionpage_verification():
    """
    DrissionPage Email Verification Runner
    使用 DrissionPage 执行邮箱验证流程
    """
    automation = DrissionPageAutomation()
    augment_auth = AugmentAuth()
    token_storage = TokenStorage()
    onemail_handler = OneMailHandler()
    
    start_time = time.time()
    temp_email = None
    
    try:
        automation.logger.log_flow_start()
        
        print('[START] 开始 DrissionPage 邮箱验证流程')
        print('')
        
        # 步骤1: 生成授权URL
        print('[LOCK] 步骤1: 生成 Augment 授权 URL')
        auth_url = augment_auth.generate_auth_url()
        print(f'[OK] 授权 URL 已生成: {auth_url}')
        print('')
        
        # 步骤2: 生成临时邮箱
        print('[EMAIL] 步骤2: 生成临时邮箱')
        temp_email = onemail_handler.generate_email()
        if not temp_email:
            raise Exception('临时邮箱生成失败')
        automation.temp_email = temp_email
        print(f'[OK] 临时邮箱生成成功: {temp_email}')
        print('')
        
        # 步骤3: 启动 DrissionPage 浏览器
        print('[WEB] 步骤3: 启动 DrissionPage 浏览器')
        automation.init_browser()
        print('')
        
        # 步骤4: 导航到授权页面
        print('[LINK] 步骤4: 导航到授权页面')
        automation.navigate_to_page(auth_url)
        print('')
        
        # 步骤5: 输入邮箱
        print('[EMAIL] 步骤5: 输入邮箱')
        automation.enter_email(temp_email)
        print('')
        
        # 步骤6: 处理Turnstile验证码
        print('[BOT] 步骤6: 处理验证码')
        automation.handle_captcha()
        print('')
        
        # 步骤7: 点击Continue
        print('[REFRESH] 步骤7: 点击Continue')
        automation.click_continue()
        print('')
        
        # 步骤8: 检查是否已进入验证页面
        print('[WAIT] 步骤8: 检查是否已进入验证页面')
        current_url = automation.page.url
        print(f'当前URL: {current_url}')

        # 检查是否已经在验证码页面
        if 'passwordless-email-challenge' in current_url:
            print('[OK] 已进入验证码页面')
        else:
            # 尝试等待验证页面
            try:
                automation.wait_for_verification_page()
            except Exception as e:
                print(f'[WARN] 等待验证页面失败: {str(e)}')
                # 检查是否有验证码输入框
                code_input = automation.page.ele('input[name="code"], input[id="code"]', timeout=3)
                if code_input:
                    print('[OK] 检测到验证码输入框，继续流程')
                else:
                    raise Exception('未找到验证码页面或验证码输入框')
        print('')
        
        # 步骤9: 获取验证码（模拟 headless-automation 的方式）
        print('📨 步骤9: 获取邮箱验证码')
        print('[EMAIL] 模拟等待邮件到达的行为...')

        # 模拟等待邮件发送的时间（Auth0需要时间发送邮件）- 随机8-15秒
        import random
        wait_seconds = random.randint(8, 15)
        print(f'⏰ 等待Auth0发送邮件（{wait_seconds}秒）...')
        time.sleep(wait_seconds)

        print('[SEARCH] 开始检查邮箱中的验证码（最多等待2分钟）...')
        verification_code = onemail_handler.get_verification_code(temp_email, 2)
        
        if not verification_code:
            raise Exception('未能获取到验证码')
        
        print(f'[OK] 验证码获取成功: {verification_code}')
        print('')
        
        # 步骤10: 输入验证码
        print('[CODE] 步骤10: 输入验证码')
        automation.enter_verification_code(verification_code)
        print('')
        
        # 步骤11: 点击验证码页面的Continue
        print('[REFRESH] 步骤11: 点击验证码页面的Continue')
        automation.click_verification_continue()
        print('')
        
        # 步骤12: 等待授权码页面
        print('[WAIT] 步骤12: 等待授权码页面')
        automation.wait_for_authorization_code()
        print('')
        
        # 步骤13: 提取授权码
        print('[COPY] 步骤13: 提取授权码')
        authorization_code = automation.extract_authorization_code()
        
        if not authorization_code:
            raise Exception('未能提取到授权码')
        
        print(f'[OK] 授权码提取成功: {authorization_code}')
        print('')
        
        # 步骤14: 完成OAuth流程
        print('[LOCK] 步骤14: 完成 OAuth 流程')
        print('[START] 开始调用真实的 Augment API...')
        
        token_response = augment_auth.complete_oauth_flow(authorization_code)
        
        print('[OK] 真实 API 调用成功！获取到真实访问令牌！')
        print(f'[KEY] 真实访问令牌: {token_response.access_token[:30] if token_response.access_token else "N/A"}...')
        print(f'[OFFICE] 租户 URL: {token_response.tenant_url}')
        print('')
        
        # 步骤15: 保存令牌
        print('[SAVE] 步骤15: 保存令牌')
        token_id = token_storage.add_token(token_response, {
            'description': 'DrissionPage token from Augment API via email verification',
            'user_agent': 'drissionpage-email-verification',
            'session_id': f'drissionpage_session_{int(time.time())}',
            'email': temp_email
        })
        
        print(f'[OK] 真实令牌已保存到 tokens.json，ID: {token_id}')
        print('')
        
        # 成功完成
        automation.logger.log_flow_end(True)
        
        duration = time.time() - start_time
        print('[SUCCESS] DrissionPage 邮箱验证流程成功完成！')
        print(f'[TIMER] 总耗时: {duration:.2f}秒')
        print(f'[CHART] 总步骤数: {automation.logger.step_counter}')
        print('')
        print('[FOLDER] 查看详细记录:')
        stats = automation.logger.get_stats()
        print(f'   [CAPTURE] 截图目录: {stats["screenshots_dir"]}')
        print(f'   [PAGE] HTML目录: {stats["html_dir"]}')
        print(f'   [COPY] 日志文件: {stats["log_file"]}')
        print(f'   [SAVE] 令牌文件: tokens.json')
        
    except Exception as error:
        automation.logger.log_flow_end(False)
        
        print('')
        print(f'[BOOM] DrissionPage 邮箱验证流程失败: {str(error)}')
        print('[FOLDER] 请查看截图和HTML文件进行调试')
        print('[FOLDER] 请查看日志文件获取详细错误信息')
        
        stats = automation.logger.get_stats()
        print(f'[CHART] 失败前执行步骤数: {stats["step_count"]}')
        print(f'[TIMER] 失败前耗时: {stats["duration_str"]}')
        print(f'[CAPTURE] 截图目录: {stats["screenshots_dir"]}')
        print(f'[PAGE] HTML目录: {stats["html_dir"]}')
        print(f'[COPY] 日志文件: {stats["log_file"]}')
        
        sys.exit(1)
        
    finally:
        # 清理资源
        automation.cleanup()

if __name__ == '__main__':
    run_drissionpage_verification()
