#!/usr/bin/env python3
"""
CthulhuJS Anti-Fingerprint 插件集成模块
用于在 DrissionPage 中加载 CthulhuJS Anti-Fingerprint 浏览器扩展
"""

import os
import json
import shutil
import zipfile
import requests
from pathlib import Path
from drissionpage_logger import DrissionPageLogger

class CthulhuJSPlugin:
    """CthulhuJS Anti-Fingerprint 插件管理器"""

    def __init__(self, logger=None):
        self.logger = logger or DrissionPageLogger()
        self.plugin_dir = Path(__file__).parent / 'cthulhujs_anti_fingerprint'
        self.plugin_id = 'pmcpffnpjncfplinfnjebjoonbncnjfl'  # CthulhuJS Anti-Fingerprint 插件 ID
        self.plugin_url = f'https://chromewebstore.google.com/detail/cthulhujs-anti-fingerprin/{self.plugin_id}'
        
    def setup_plugin_directory(self, plugin_source=None):
        """设置插件目录"""
        try:
            self.logger.log('🐙 设置 CthulhuJS Anti-Fingerprint 插件目录...')
            
            # 创建插件目录
            self.plugin_dir.mkdir(parents=True, exist_ok=True)
            
            if plugin_source:
                if isinstance(plugin_source, str) and plugin_source.endswith('.crx'):
                    # 从 .crx 文件加载
                    self._extract_crx_file(plugin_source)
                elif isinstance(plugin_source, str) and os.path.isdir(plugin_source):
                    # 从目录复制
                    self._copy_plugin_directory(plugin_source)
                else:
                    self.logger.log('⚠️ 不支持的插件源格式')
                    return False
            else:
                # 创建基本的插件结构（如果没有提供插件源）
                self._create_basic_plugin()
            
            self.logger.log(f'✅ CthulhuJS Anti-Fingerprint 插件目录已设置: {self.plugin_dir}')
            return True
            
        except Exception as e:
            self.logger.log(f'❌ 插件目录设置失败: {e}')
            return False
    
    def _extract_crx_file(self, crx_path):
        """从 .crx 文件提取插件"""
        self.logger.log(f'📦 从 CRX 文件提取插件: {crx_path}')

        try:
            # CRX 文件实际上是一个特殊格式的 ZIP 文件
            # 需要跳过 CRX 头部，然后解压 ZIP 内容
            with open(crx_path, 'rb') as f:
                # 读取 CRX 头部
                magic = f.read(4)
                if magic != b'Cr24':
                    # 如果不是 CRX 格式，尝试作为普通 ZIP 文件处理
                    self.logger.log('⚠️ 不是标准 CRX 格式，尝试作为 ZIP 文件处理')
                    f.seek(0)
                    zip_data = f.read()
                else:
                    # 标准 CRX 格式处理
                    version = int.from_bytes(f.read(4), 'little')
                    pub_key_len = int.from_bytes(f.read(4), 'little')
                    sig_len = int.from_bytes(f.read(4), 'little')

                    # 跳过公钥和签名
                    f.seek(pub_key_len + sig_len, 1)

                    # 剩余的就是 ZIP 数据
                    zip_data = f.read()

            # 解压到插件目录
            import io
            try:
                with zipfile.ZipFile(io.BytesIO(zip_data)) as zip_file:
                    zip_file.extractall(self.plugin_dir)
                self.logger.log(f'✅ CRX 文件解压成功')
            except zipfile.BadZipFile:
                # 如果 ZIP 解压失败，尝试直接重命名文件
                self.logger.log('⚠️ ZIP 解压失败，尝试直接使用 CRX 文件')
                # 创建基本插件结构
                self._create_basic_plugin()

        except Exception as e:
            self.logger.log(f'⚠️ CRX 文件处理失败: {e}，使用基本插件')
            self._create_basic_plugin()
    
    def _copy_plugin_directory(self, source_dir):
        """从目录复制插件"""
        self.logger.log(f'📁 从目录复制插件: {source_dir}')
        
        # 清空目标目录
        if self.plugin_dir.exists():
            shutil.rmtree(self.plugin_dir)
        
        # 复制插件目录
        shutil.copytree(source_dir, self.plugin_dir)
        self.logger.log('✅ 插件目录复制完成')
    
    def _create_basic_plugin(self):
        """创建基本的 CthulhuJS Anti-Fingerprint 插件结构"""
        self.logger.log('🔧 创建基本 CthulhuJS Anti-Fingerprint 插件结构...')
        
        # 创建 manifest.json
        manifest = {
            "manifest_version": 3,
            "name": "CthulhuJS Anti-Fingerprint (Basic)",
            "version": "1.0.0",
            "description": "Basic anti-fingerprinting protection inspired by CthulhuJS",
            "permissions": [
                "activeTab",
                "scripting",
                "storage"
            ],
            "host_permissions": [
                "<all_urls>"
            ],
            "content_scripts": [
                {
                    "matches": ["<all_urls>"],
                    "js": ["content.js"],
                    "run_at": "document_start",
                    "all_frames": True
                }
            ],
            "background": {
                "service_worker": "background.js"
            },
            "web_accessible_resources": [
                {
                    "resources": ["inject.js"],
                    "matches": ["<all_urls>"]
                }
            ]
        }
        
        with open(self.plugin_dir / 'manifest.json', 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2)
        
        # 创建 background.js
        background_js = '''
// CthulhuJS Anti-Fingerprint Background Script
console.log('CthulhuJS Anti-Fingerprint extension loaded');

// 监听扩展安装
chrome.runtime.onInstalled.addListener(() => {
    console.log('CthulhuJS Anti-Fingerprint extension installed');
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'loading' && tab.url) {
        // 注入反指纹脚本
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['inject.js']
        }).catch(err => {
            // 忽略错误，某些页面可能无法注入
        });
    }
});
'''
        
        with open(self.plugin_dir / 'background.js', 'w', encoding='utf-8') as f:
            f.write(background_js)
        
        # 创建 content.js
        content_js = '''
// CthulhuJS Anti-Fingerprint Content Script
(function() {
    'use strict';
    
    console.log('CthulhuJS Anti-Fingerprint content script loaded');
    
    // 注入反指纹脚本到页面
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('inject.js');
    script.onload = function() {
        this.remove();
    };
    (document.head || document.documentElement).appendChild(script);
})();
'''
        
        with open(self.plugin_dir / 'content.js', 'w', encoding='utf-8') as f:
            f.write(content_js)
        
        # 创建 inject.js - 主要的反指纹逻辑
        inject_js = '''
// CthulhuJS Anti-Fingerprint Injection Script
(function() {
    'use strict';

    console.log('CthulhuJS Anti-Fingerprint injection script loaded');

    // 防止重复注入
    if (window.cthulhuJSAntiFingerprint) {
        return;
    }
    window.cthulhuJSAntiFingerprint = true;

    // WebGL 指纹防护
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
        if (contextType === 'webgl' || contextType === 'webgl2' || contextType === 'experimental-webgl') {
            const context = originalGetContext.call(this, contextType, contextAttributes);
            if (context) {
                // 随机化 WebGL 参数
                const originalGetParameter = context.getParameter;
                context.getParameter = function(parameter) {
                    const result = originalGetParameter.call(this, parameter);

                    // 随机化一些关键参数
                    if (parameter === context.RENDERER) {
                        return 'Intel Iris OpenGL Engine';
                    }
                    if (parameter === context.VENDOR) {
                        return 'Intel Inc.';
                    }
                    if (parameter === context.VERSION) {
                        return 'OpenGL ES 2.0';
                    }

                    return result;
                };
            }
            return context;
        }
        return originalGetContext.call(this, contextType, contextAttributes);
    };

    // Canvas 指纹防护
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function() {
        // 添加微小的随机噪声
        const context = this.getContext('2d');
        if (context) {
            const imageData = context.getImageData(0, 0, this.width, this.height);
            const data = imageData.data;

            // 随机修改少量像素
            for (let i = 0; i < data.length; i += 4) {
                if (Math.random() < 0.001) { // 0.1% 的像素
                    data[i] = Math.min(255, data[i] + Math.floor(Math.random() * 3) - 1);
                    data[i + 1] = Math.min(255, data[i + 1] + Math.floor(Math.random() * 3) - 1);
                    data[i + 2] = Math.min(255, data[i + 2] + Math.floor(Math.random() * 3) - 1);
                }
            }

            context.putImageData(imageData, 0, 0);
        }

        return originalToDataURL.apply(this, arguments);
    };

    // AudioContext 指纹防护
    if (window.AudioContext || window.webkitAudioContext) {
        const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
        const audioContexts = new WeakMap();

        function AudioContextWrapper() {
            const context = new OriginalAudioContext();
            audioContexts.set(this, context);
            return new Proxy(this, {
                get(target, prop) {
                    const realContext = audioContexts.get(target);
                    if (prop === 'createAnalyser') {
                        return function() {
                            const analyser = realContext.createAnalyser();
                            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                            analyser.getFloatFrequencyData = function(array) {
                                originalGetFloatFrequencyData.call(this, array);
                                // 添加微小的随机噪声
                                for (let i = 0; i < array.length; i++) {
                                    array[i] += (Math.random() - 0.5) * 0.0001;
                                }
                            };
                            return analyser;
                        };
                    }
                    return realContext[prop];
                }
            });
        }

        AudioContextWrapper.prototype = OriginalAudioContext.prototype;
        window.AudioContext = AudioContextWrapper;
        if (window.webkitAudioContext) {
            window.webkitAudioContext = AudioContextWrapper;
        }
    }

    // 屏幕分辨率随机化
    Object.defineProperty(screen, 'width', {
        get: function() { return 1920 + Math.floor(Math.random() * 100); }
    });
    Object.defineProperty(screen, 'height', {
        get: function() { return 1080 + Math.floor(Math.random() * 100); }
    });
    Object.defineProperty(screen, 'availWidth', {
        get: function() { return 1920 + Math.floor(Math.random() * 100); }
    });
    Object.defineProperty(screen, 'availHeight', {
        get: function() { return 1040 + Math.floor(Math.random() * 60); }
    });

    // 时区随机化
    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
    Date.prototype.getTimezoneOffset = function() {
        // 返回一些常见的时区偏移
        const commonOffsets = [-480, -420, -360, -300, -240, -180, 0, 60, 120, 180, 240, 300, 360, 420, 480, 540];
        return commonOffsets[Math.floor(Math.random() * commonOffsets.length)];
    };

    // 语言随机化
    Object.defineProperty(navigator, 'language', {
        get: function() {
            const languages = ['en-US', 'en-GB', 'zh-CN', 'zh-TW', 'ja-JP', 'ko-KR', 'fr-FR', 'de-DE', 'es-ES', 'it-IT'];
            return languages[Math.floor(Math.random() * languages.length)];
        }
    });

    Object.defineProperty(navigator, 'languages', {
        get: function() {
            const allLanguages = [
                ['en-US', 'en'],
                ['en-GB', 'en'],
                ['zh-CN', 'zh'],
                ['zh-TW', 'zh'],
                ['ja-JP', 'ja'],
                ['ko-KR', 'ko'],
                ['fr-FR', 'fr'],
                ['de-DE', 'de'],
                ['es-ES', 'es'],
                ['it-IT', 'it']
            ];
            return allLanguages[Math.floor(Math.random() * allLanguages.length)];
        }
    });

    // 硬件并发数随机化
    Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: function() {
            const cores = [2, 4, 6, 8, 12, 16];
            return cores[Math.floor(Math.random() * cores.length)];
        }
    });

    // 内存大小随机化
    Object.defineProperty(navigator, 'deviceMemory', {
        get: function() {
            const memories = [2, 4, 8, 16, 32];
            return memories[Math.floor(Math.random() * memories.length)];
        }
    });

    console.log('CthulhuJS Anti-Fingerprint protection activated');
})();
'''

        with open(self.plugin_dir / 'inject.js', 'w', encoding='utf-8') as f:
            f.write(inject_js)

        self.logger.log('✅ 基本 CthulhuJS Anti-Fingerprint 插件结构创建完成')

    def validate_plugin(self):
        """验证插件是否有效"""
        try:
            # 检查必要文件是否存在
            required_files = ['manifest.json', 'background.js', 'content.js', 'inject.js']

            for file_name in required_files:
                file_path = self.plugin_dir / file_name
                if not file_path.exists():
                    return False, f'缺少必要文件: {file_name}'

            # 验证 manifest.json 格式
            try:
                with open(self.plugin_dir / 'manifest.json', 'r', encoding='utf-8') as f:
                    manifest = json.load(f)

                if 'manifest_version' not in manifest:
                    return False, 'manifest.json 缺少 manifest_version'

                if 'name' not in manifest:
                    return False, 'manifest.json 缺少 name'

            except json.JSONDecodeError:
                return False, 'manifest.json 格式错误'

            return True, '插件验证成功'

        except Exception as e:
            return False, f'插件验证失败: {e}'

    def get_plugin_path(self):
        """获取插件路径"""
        return str(self.plugin_dir)

    def get_plugin_info(self):
        """获取插件信息"""
        try:
            with open(self.plugin_dir / 'manifest.json', 'r', encoding='utf-8') as f:
                manifest = json.load(f)
                return {
                    'name': manifest.get('name', 'Unknown'),
                    'version': manifest.get('version', 'Unknown'),
                    'description': manifest.get('description', 'No description'),
                    'path': str(self.plugin_dir)
                }
        except Exception:
            return {
                'name': 'CthulhuJS Anti-Fingerprint',
                'version': 'Unknown',
                'description': 'Anti-fingerprinting protection',
                'path': str(self.plugin_dir)
            }

    def print_installation_guide(self):
        """打印安装指南"""
        self.logger.log('📖 CthulhuJS Anti-Fingerprint 插件安装指南:')
        self.logger.log('   1. 访问 Chrome Web Store:')
        self.logger.log(f'      {self.plugin_url}')
        self.logger.log('   2. 点击 "添加至 Chrome" 安装插件')
        self.logger.log('   3. 或者手动下载 .crx 文件放置到项目目录')
        self.logger.log('   4. 重新运行自动化脚本')
        self.logger.log('')
        self.logger.log('💡 提示: 当前使用的是基本版本的反指纹保护')
        self.logger.log('   真实插件提供更强的保护效果')


def auto_download_cthulhujs_plugin():
    """自动下载 CthulhuJS Anti-Fingerprint 插件 (占位函数)"""
    logger = DrissionPageLogger()
    logger.log('⚠️ 自动下载功能暂未实现')
    logger.log('💡 请手动从 Chrome Web Store 下载插件')
    logger.log('   https://chromewebstore.google.com/detail/cthulhujs-anti-fingerprin/pmcpffnpjncfplinfnjebjoonbncnjfl')
    return None


if __name__ == '__main__':
    # 测试插件管理器
    logger = DrissionPageLogger()
    plugin_manager = CthulhuJSPlugin(logger)

    logger.log('🧪 测试 CthulhuJS Anti-Fingerprint 插件管理器...')

    # 设置插件目录
    if plugin_manager.setup_plugin_directory():
        # 验证插件
        is_valid, message = plugin_manager.validate_plugin()
        logger.log(f'验证结果: {message}')

        if is_valid:
            info = plugin_manager.get_plugin_info()
            logger.log(f'插件信息: {info["name"]} v{info["version"]}')
            logger.log(f'插件路径: {info["path"]}')

        # 打印安装指南
        plugin_manager.print_installation_guide()
    else:
        logger.log('❌ 插件设置失败')
