"""
RoxyBrowser Integration for DrissionPage
将 RoxyBrowser 专业反指纹浏览器与 DrissionPage 强大自动化能力完美结合
"""

import time
from typing import Dict, Optional
from roxybrowser_client import RoxyBrowserClient, RoxyBrowserManager


class RoxyBrowserIntegration:
    """
    RoxyBrowser 与 DrissionPage 集成器
    负责管理 RoxyBrowser 浏览器实例并提供给 DrissionPage 使用
    """
    
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        
        # RoxyBrowser API 客户端
        self.roxy_client = RoxyBrowserClient(
            api_host=self.config.roxybrowser_api_host,
            api_token=self.config.roxybrowser_api_token
        )
        
        # RoxyBrowser 管理器
        self.roxy_manager = RoxyBrowserManager(
            client=self.roxy_client,
            workspace_id=self.config.roxybrowser_workspace_id
        )
        
        # 当前浏览器窗口信息
        self.current_window = None
        
        # 验证 API 连接
        self._verify_api_connection()
    
    def _verify_api_connection(self):
        """验证 RoxyBrowser API 连接"""
        self.logger.log("[SEARCH] 验证 RoxyBrowser API 连接...")
        if not self.roxy_client.health_check():
            raise Exception("RoxyBrowser API 连接失败，请检查配置")
        self.logger.log("[OK] RoxyBrowser API 连接正常")

    def _apply_random_fingerprint(self):
        """应用随机指纹到当前窗口"""
        if not self.current_window:
            self.logger.log("[WARN] 无法应用随机指纹：当前窗口不存在")
            return

        try:
            self.logger.log("[DICE] 正在应用随机指纹...")
            self.logger.log(f"   🆔 窗口ID: {self.current_window['dir_id']}")

            # 调用随机指纹API
            result = self.roxy_client.random_fingerprint(
                workspace_id=self.config.roxybrowser_workspace_id,
                dir_id=self.current_window['dir_id']
            )

            if result.get('code') == 0:
                self.logger.log("[OK] 随机指纹应用成功！")
                self.logger.log("   [SHIELD] 浏览器指纹已随机化")
            else:
                self.logger.log(f"[WARN] 随机指纹应用失败: {result.get('msg', '未知错误')}")

        except Exception as e:
            self.logger.log(f"[ERROR] 随机指纹应用异常: {str(e)}")
            # 不抛出异常，因为这不是致命错误
    
    def create_browser_instance(self) -> Dict:
        """
        创建 RoxyBrowser 实例并返回连接信息
        返回格式: {'host': '127.0.0.1', 'port': 4104, 'ws': 'ws://...', 'pid': 12345}
        """
        try:
            self.logger.log("[START] 创建 RoxyBrowser 浏览器实例...")

            # 步骤1: 清理所有现有窗口（释放额度）
            self.logger.log("[CLEAN] 清理现有窗口以释放额度...")
            self._cleanup_all_existing_windows()

            # 打印配置信息
            self._print_roxybrowser_config()

            # 获取窗口配置
            window_config = self._get_window_config()

            # 创建并打开浏览器窗口
            self.current_window = self.roxy_manager.create_and_open_browser(window_config)

            # 应用随机指纹（每次都执行）
            self._apply_random_fingerprint()

            # 解析连接信息
            connection_info = self.current_window['connection_info']
            debug_port = connection_info['http'].split(':')[1]
            
            result = {
                'host': '127.0.0.1',
                'port': int(debug_port),
                'ws': connection_info['ws'],
                'pid': connection_info['pid'],
                'dir_id': self.current_window['dir_id'],
                'window_id': self.current_window['window_id']
            }
            
            self.logger.log(f"[OK] RoxyBrowser 实例创建成功")
            self.logger.log(f"   📡 调试端口: {result['port']}")
            self.logger.log(f"   🆔 进程ID: {result['pid']}")
            self.logger.log(f"   [WINDOW] 窗口ID: {result['window_id']}")
            
            return result
            
        except Exception as e:
            self.logger.error("[ERROR] RoxyBrowser 实例创建失败", e)
            raise e
    
    def cleanup_browser_instance(self):
        """清理 RoxyBrowser 实例（立即删除以释放额度）"""
        try:
            if self.current_window:
                self.logger.log("[CLEAN] 立即清理 RoxyBrowser 实例以释放额度...")
                self.roxy_manager.close_and_delete_current_window()
                self.current_window = None
                self.logger.log("[OK] RoxyBrowser 实例清理完成，额度已释放")
            else:
                self.logger.log("[OK] 没有活动的 RoxyBrowser 实例需要清理")
        except Exception as e:
            self.logger.log(f"[WARN] RoxyBrowser 实例清理失败: {str(e)}")

            # 如果常规清理失败，尝试强制清理
            try:
                if self.current_window:
                    dir_id = self.current_window.get('dir_id')
                    if dir_id:
                        self.logger.log("[CONFIG] 尝试强制清理窗口...")
                        self.roxy_client.close_browser(dir_id)
                        self.roxy_client.delete_browser(self.config.roxybrowser_workspace_id, [dir_id])
                        self.logger.log("[OK] 强制清理成功")
                        self.current_window = None
            except Exception as e2:
                self.logger.log(f"[WARN] 强制清理也失败: {str(e2)}")
    
    def emergency_cleanup(self):
        """紧急清理所有 RoxyBrowser 窗口"""
        try:
            self.logger.log("[ALERT] 执行 RoxyBrowser 紧急清理...")
            self.roxy_manager.cleanup_all_windows()
            self.current_window = None
            self.logger.log("[OK] RoxyBrowser 紧急清理完成")
        except Exception as e:
            self.logger.log(f"[WARN] RoxyBrowser 紧急清理失败: {str(e)}")

    def _cleanup_all_existing_windows(self):
        """清理所有现有窗口以释放额度"""
        try:
            # 获取所有现有窗口
            browser_list = self.roxy_client.get_browser_list(self.config.roxybrowser_workspace_id)
            windows = browser_list.get('data', {}).get('rows', [])

            if not windows:
                self.logger.log("[OK] 没有现有窗口需要清理")
                return

            self.logger.log(f"[SEARCH] 发现 {len(windows)} 个现有窗口，开始清理...")

            # 关闭所有窗口
            closed_count = 0
            for window in windows:
                dir_id = window['dirId']
                window_name = window.get('windowName', 'Unknown')
                try:
                    self.roxy_client.close_browser(dir_id)
                    self.logger.log(f"[LOCK] 关闭窗口: {window_name} ({dir_id})")
                    closed_count += 1
                except Exception as e:
                    self.logger.log(f"[WARN] 关闭窗口失败 {window_name}: {str(e)}")

            # 等待关闭完成
            if closed_count > 0:
                self.logger.log("[WAIT] 等待窗口关闭完成...")
                import time
                time.sleep(3)

            # 删除所有窗口
            dir_ids = [window['dirId'] for window in windows]
            if dir_ids:
                try:
                    self.roxy_client.delete_browser(self.config.roxybrowser_workspace_id, dir_ids)
                    self.logger.log(f"[DELETE] 删除 {len(dir_ids)} 个窗口")
                except Exception as e:
                    self.logger.log(f"[WARN] 批量删除窗口失败: {str(e)}")

                    # 尝试逐个删除
                    deleted_count = 0
                    for dir_id in dir_ids:
                        try:
                            self.roxy_client.delete_browser(self.config.roxybrowser_workspace_id, [dir_id])
                            deleted_count += 1
                        except Exception as e2:
                            self.logger.log(f"[WARN] 删除单个窗口失败 {dir_id}: {str(e2)}")

                    if deleted_count > 0:
                        self.logger.log(f"[DELETE] 逐个删除成功 {deleted_count} 个窗口")

            self.logger.log("[OK] 现有窗口清理完成，额度已释放")

        except Exception as e:
            self.logger.log(f"[WARN] 清理现有窗口时出错: {str(e)}")
            # 即使清理失败也继续执行，不影响主流程
    
    def _get_window_config(self) -> Dict:
        """获取 RoxyBrowser 窗口配置"""
        config = {
            "workspaceId": self.config.roxybrowser_workspace_id,
            "windowName": self.config.roxybrowser_window_name,
            "coreVersion": self.config.roxybrowser_core_version,
            "os": self.config.roxybrowser_os,
            "osVersion": self.config.roxybrowser_os_version,
            "fingerInfo": {
                "randomFingerprint": self.config.roxybrowser_random_fingerprint,
                "forbidSavePassword": True,
                "clearCacheFile": True,
                "clearCookie": True,
                "clearLocalStorage": True,
                "webRTC": 2,  # 禁止
                "webGL": True,  # 随机
                "canvas": True,  # 随机
                "audioContext": True,  # 随机
                "doNotTrack": True,
                "clientRects": True,
                "deviceInfo": True,
                "deviceNameSwitch": True,
                "macInfo": True,
                "portScanProtect": True,
                "useGpu": True,
                "sandboxPermission": False
            }
        }
        
        # 添加代理配置
        if self.config.roxybrowser_proxy and self.config.proxy_url:
            host, port = self.config.proxy_url.split(':')
            config["proxyInfo"] = {
                "proxyMethod": "custom",
                "proxyCategory": "SOCKS5",
                "ipType": "IPV4",
                "host": host,
                "port": port,
                "proxyUserName": self.config.proxy_user,
                "proxyPassword": self.config.proxy_pass
            }
        else:
            config["proxyInfo"] = {
                "proxyMethod": "custom",
                "proxyCategory": "noproxy"
            }
            
        return config
    
    def _print_roxybrowser_config(self):
        """打印 RoxyBrowser 配置信息"""
        self.logger.log('[CONFIG] RoxyBrowser 集成配置:')
        self.logger.log(f'   [WEB] API Host: {self.config.roxybrowser_api_host}')
        self.logger.log(f'   [KEY] API Token: {"已设置" if self.config.roxybrowser_api_token else "未设置"}')
        self.logger.log(f'   [OFFICE] 工作空间ID: {self.config.roxybrowser_workspace_id}')
        self.logger.log(f'   [WINDOW] 窗口名称: {self.config.roxybrowser_window_name}')
        self.logger.log(f'   [SCREEN] 操作系统: {self.config.roxybrowser_os} {self.config.roxybrowser_os_version}')
        self.logger.log(f'   [CONFIG] 内核版本: {self.config.roxybrowser_core_version}')
        self.logger.log(f'   📡 代理: {"启用" if self.config.roxybrowser_proxy else "禁用"}')
        if self.config.roxybrowser_proxy and self.config.proxy_url:
            self.logger.log(f'   [WEB] 代理地址: {self.config.proxy_url}')
            self.logger.log(f'   👤 代理用户: {self.config.proxy_user}')
        self.logger.log(f'   [SHIELD] 指纹防护: {"启用" if self.config.roxybrowser_fingerprint_protection else "禁用"}')
        self.logger.log(f'   [DICE] 随机指纹: {"启用" if self.config.roxybrowser_random_fingerprint else "禁用"}')
    
    def get_current_window_info(self) -> Optional[Dict]:
        """获取当前窗口信息"""
        return self.current_window
    
    def is_browser_running(self) -> bool:
        """检查浏览器是否正在运行"""
        return self.current_window is not None


class RoxyBrowserConnectionManager:
    """
    RoxyBrowser 连接管理器
    简化版本，专门用于 DrissionPage 集成
    """
    
    @staticmethod
    def create_connection_address(host: str, port: int) -> str:
        """创建 DrissionPage 连接地址"""
        return f"{host}:{port}"
    
    @staticmethod
    def validate_connection_info(connection_info: Dict) -> bool:
        """验证连接信息是否有效"""
        required_keys = ['host', 'port', 'ws', 'pid']
        return all(key in connection_info for key in required_keys)
    
    @staticmethod
    def format_connection_info(connection_info: Dict) -> str:
        """格式化连接信息用于日志显示"""
        return (f"Host: {connection_info.get('host', 'N/A')}, "
                f"Port: {connection_info.get('port', 'N/A')}, "
                f"PID: {connection_info.get('pid', 'N/A')}")


def test_roxybrowser_integration():
    """测试 RoxyBrowser 集成功能"""
    print("🧪 测试 RoxyBrowser 集成功能...")
    
    try:
        # 这里需要实际的配置和日志对象
        # 在实际使用中会从 DrissionPage 传入
        print("[WARN] 需要在 DrissionPage 环境中测试")
        return True
    except Exception as e:
        print(f"[ERROR] 测试失败: {str(e)}")
        return False


if __name__ == '__main__':
    test_roxybrowser_integration()
