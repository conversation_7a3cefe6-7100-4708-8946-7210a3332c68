[{"VENDOR": "WebKit", "RENDERER": "WebKit WebGL", "VERSION": "WebGL 2.0 (OpenGL ES 2.0 Chromium)", "SHADING_LANGUAGE_VERSION": "WebGL GLSL ES 2.00 (OpenGL ES GLSL ES 2.0 Chromium)", "UNMASKED_VENDOR_WEBGL": "Google Inc. (Nvidia)", "UNMASKED_RENDERER_WEBGL": ["ANGLE (NVIDIA, NVIDIA GeForce GTX 960 Direct3D11 vs_5_0 ps_5_0, D3D11-26.21.14.4575)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 750 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-25.21.14.1735)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 980 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.6089)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.5185)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.3245)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.4567)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.1234)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.6985)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.7654)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.2348)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.8460)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.2737)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.5387)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.3453)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.8246)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.14.8476)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.5185)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.3245)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.4567)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.1234)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.6985)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.7654)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.2348)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.8460)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.2737)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.5387)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.3453)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.8246)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.8476)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.5185)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.3245)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.4567)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.1234)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.6985)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.7654)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.2348)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.8460)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.2737)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.5387)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.3453)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.13.8246)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 1050 Direct3D11 vs_5_0 ps_5_0, D3D11-22.21.13.8476)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5185)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.3245)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.4567)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.1234)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.6985)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.7654)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.2348)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.8460)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.2737)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5387)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.3453)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.8246)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 1050 Direct3D11 vs_5_0 ps_5_0, D3D11-22.21.14.8476)"]}, {"VENDOR": "WebKit", "RENDERER": "WebKit WebGL", "VERSION": "WebGL 2.0 (OpenGL ES 3.0 Chromium)", "SHADING_LANGUAGE_VERSION": "WebGL GLSL ES 3.00 (OpenGL ES GLSL ES 3.0 Chromium)", "UNMASKED_VENDOR_WEBGL": "Google Inc. (Google)", "UNMASKED_RENDERER_WEBGL": ["ANGLE (Google, Vulkan 1.3.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.3.1 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.3.2 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.3.3 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.3.4 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.3.5 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.3.6 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.3.7 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.3.8 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.3.9 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.1 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.2 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.3 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.4 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.5 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.6 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.7 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.8 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.4.9 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.5.0 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.5.1 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.5.2 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.5.3 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.5.4 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.5.5 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.5.6 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANGLE (Google, Vulkan 1.5.7 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANG<PERSON> (Google, Vulkan 1.5.8 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)", "ANG<PERSON> (Google, Vulkan 1.5.9 (<PERSON><PERSON><PERSON><PERSON> (Subzero) (0x0000C0DE)), SwiftShader driver)"]}, {"VENDOR": "WebKit", "RENDERER": "WebKit WebGL", "VERSION": "WebGL 2.0 (OpenGL ES 3.0 Chromium)", "SHADING_LANGUAGE_VERSION": "WebGL GLSL ES 3.00 (OpenGL ES GLSL ES 3.0 Chromium)", "UNMASKED_VENDOR_WEBGL": "Google Inc. (Intel)", "UNMASKED_RENDERER_WEBGL": ["ANGLE (Intel, Mesa Intel(R) Graphics (RPL-K), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-A), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-B), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-C), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-D), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-E), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-F), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-G), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-H), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-I), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-J), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-K), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-L), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-M), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-N), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-O), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-P), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-Q), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-R), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-S), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-T), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-U), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-V), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-W), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-X), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-Y), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-Z), OpenGL 4.6)", "ANGLE (Intel, Intel(R) Iris(R) Plus Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)", "ANGLE (Intel, Intel(R) Iris(R) Plus Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)", "ANGLE (Intel, Intel(R) HD Graphics 4000 Direct3D11 vs_5_0 ps_5_0, D3D11-10.18.10.5059)", "ANGLE (Intel, Intel(R) HD Graphics 4000 Direct3D11 vs_5_0 ps_5_0, D3D11-10.18.13.5582)", "ANGLE (Intel, Intel(R) HD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-10.18.10.4885)", "ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3011 vs_5 0 ps_5_0, D3D11)"]}, {"VENDOR": "WebKit", "RENDERER": "WebKit WebGL", "VERSION": "WebGL 2.0 (OpenGL ES 3.0 Chromium)", "SHADING_LANGUAGE_VERSION": "WebGL GLSL ES 3.00 (OpenGL ES GLSL ES 3.0 Chromium)", "UNMASKED_VENDOR_WEBGL": "Google Inc. (Amd)", "UNMASKED_RENDERER_WEBGL": ["ANGLE (AMD, RENOIR(renoir LLVM 15.0.9), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.8), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.7), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.6), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.5), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.4), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.3), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.2), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.1), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.0), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.9), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.8), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.7), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.6), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.5), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.4), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.3), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.2), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.1), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.1.0), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.9), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.8), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.7), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.6), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.5), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.4), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.3), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.2), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.1), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.2.0), OpenGL 4.6)"]}]