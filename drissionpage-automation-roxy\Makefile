# DrissionPage Automation Makefile

.PHONY: help install test run clean setup dev

# Default target
help:
	@echo "DrissionPage Automation - Available commands:"
	@echo ""
	@echo "  install     - Install Python dependencies"
	@echo "  test        - Run environment tests"
	@echo "  run         - Run email verification automation"
	@echo "  setup       - Setup development environment"
	@echo "  clean       - Clean generated files"
	@echo "  dev         - Install development dependencies"
	@echo "  help        - Show this help message"
	@echo ""

# Install dependencies
install:
	@echo "📦 Installing Python dependencies..."
	pip install -r requirements.txt
	@echo "✅ Dependencies installed successfully!"

# Run environment tests
test:
	@echo "🧪 Running environment tests..."
	python test_setup.py

# Run the automation
run:
	@echo "🚀 Running DrissionPage email verification..."
	python run_drissionpage_verification.py

# Setup development environment
setup: install
	@echo "🔧 Setting up development environment..."
	@if [ ! -f "../.env" ]; then \
		echo "⚠️  .env file not found in parent directory"; \
		echo "💡 Please create ../.env with required configuration"; \
		echo "📖 See README.md for configuration details"; \
	else \
		echo "✅ .env file found"; \
	fi
	@echo "🧪 Running setup test..."
	python test_setup.py

# Install development dependencies
dev: install
	@echo "🛠️  Installing development dependencies..."
	pip install pytest pytest-asyncio black flake8 mypy
	@echo "✅ Development environment ready!"

# Clean generated files
clean:
	@echo "🧹 Cleaning generated files..."
	rm -rf logs/*.log
	rm -rf screenshots/*.png
	rm -rf html/*.html
	rm -rf __pycache__/
	rm -rf *.pyc
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	@echo "✅ Cleanup completed!"

# Quick start (install + test + run)
start: install test run

# Show project status
status:
	@echo "📊 DrissionPage Automation Status:"
	@echo ""
	@echo "Python version: $(shell python --version)"
	@echo "Pip version: $(shell pip --version)"
	@echo ""
	@echo "📁 Project structure:"
	@ls -la
	@echo ""
	@echo "📋 Recent logs:"
	@if [ -d "logs" ] && [ "$(shell ls -A logs)" ]; then \
		ls -la logs/ | tail -5; \
	else \
		echo "No log files found"; \
	fi
