import requests
import json
import time
from typing import Dict, Optional, Any

class RoxyBrowserClient:
    """
    RoxyBrowser API 客户端
    封装 RoxyBrowser API 调用
    """
    
    def __init__(self, api_host: str, api_token: str):
        self.api_host = api_host.rstrip('/')
        self.api_token = api_token
        self.headers = {
            'Content-Type': 'application/json',
            'token': api_token
        }
        
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """发送 API 请求"""
        url = f"{self.api_host}{endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, params=data, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=self.headers, json=data, timeout=30)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
            response.raise_for_status()
            result = response.json()
            
            if result.get('code') != 0:
                raise Exception(f"API Error: {result.get('msg', 'Unknown error')}")
                
            return result
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"Request failed: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"Invalid JSON response: {str(e)}")
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            result = self._make_request('GET', '/health')
            return result.get('code') == 0
        except:
            return False
    
    def get_workspaces(self) -> Dict:
        """获取工作空间列表"""
        return self._make_request('GET', '/browser/workspace')
    
    def get_browser_list(self, workspace_id: int, **kwargs) -> Dict:
        """获取浏览器窗口列表"""
        params = {'workspaceId': workspace_id}
        params.update(kwargs)
        return self._make_request('GET', '/browser/list_v3', params)
    
    def get_browser_detail(self, workspace_id: int, dir_id: str) -> Dict:
        """获取浏览器窗口详情"""
        params = {
            'workspaceId': workspace_id,
            'dirId': dir_id
        }
        return self._make_request('GET', '/browser/detail', params)
    
    def create_browser(self, config: Dict) -> Dict:
        """创建浏览器窗口"""
        return self._make_request('POST', '/browser/create', config)
    
    def modify_browser(self, config: Dict) -> Dict:
        """修改浏览器窗口"""
        return self._make_request('POST', '/browser/mdf', config)
    
    def delete_browser(self, workspace_id: int, dir_ids: list) -> Dict:
        """删除浏览器窗口"""
        data = {
            'workspaceId': workspace_id,
            'dirIds': dir_ids
        }
        return self._make_request('POST', '/browser/delete', data)
    
    def open_browser(self, workspace_id: int, dir_id: str, args: Optional[list] = None) -> Dict:
        """打开浏览器窗口"""
        data = {
            'workspaceId': workspace_id,
            'dirId': dir_id
        }
        if args:
            data['args'] = args
        return self._make_request('POST', '/browser/open', data)
    
    def close_browser(self, dir_id: str) -> Dict:
        """关闭浏览器窗口"""
        data = {'dirId': dir_id}
        return self._make_request('POST', '/browser/close', data)
    
    def get_connection_info(self, dir_ids: Optional[str] = None) -> Dict:
        """获取已打开窗口的连接信息"""
        params = {}
        if dir_ids:
            params['dirIds'] = dir_ids
        return self._make_request('GET', '/browser/connection_info', params)
    
    def random_fingerprint(self, workspace_id: int, dir_id: str) -> Dict:
        """随机生成窗口指纹"""
        data = {
            'workspaceId': workspace_id,
            'dirId': dir_id
        }
        return self._make_request('POST', '/browser/random_env', data)
    
    def clear_local_cache(self, dir_ids: list) -> Dict:
        """清空窗口本地缓存"""
        data = {'dirIds': dir_ids}
        return self._make_request('POST', '/browser/clear_local_cache', data)
    
    def clear_server_cache(self, workspace_id: int, dir_ids: list) -> Dict:
        """清空窗口服务器缓存"""
        data = {
            'workspaceId': workspace_id,
            'dirIds': dir_ids
        }
        return self._make_request('POST', '/browser/clear_server_cache', data)


class RoxyBrowserManager:
    """
    RoxyBrowser 管理器
    提供高级的浏览器窗口管理功能
    """
    
    def __init__(self, client: RoxyBrowserClient, workspace_id: int):
        self.client = client
        self.workspace_id = workspace_id
        self.current_window = None
        
    def create_and_open_browser(self, window_config: Dict, open_args: Optional[list] = None) -> Dict:
        """创建并打开浏览器窗口"""
        print(f"🔧 创建浏览器窗口: {window_config.get('windowName', 'Unknown')}")
        
        # 创建窗口
        create_result = self.client.create_browser(window_config)
        dir_id = create_result['data']['dirId']
        window_id = create_result['data']['windowId']
        
        print(f"✅ 窗口创建成功 - ID: {window_id}, DirID: {dir_id}")
        
        # 等待窗口创建完成
        time.sleep(2)
        
        # 打开窗口
        print(f"🚀 打开浏览器窗口...")
        open_result = self.client.open_browser(self.workspace_id, dir_id, open_args)
        
        # 保存当前窗口信息
        self.current_window = {
            'dir_id': dir_id,
            'window_id': window_id,
            'connection_info': open_result['data']
        }
        
        print(f"✅ 窗口打开成功")
        print(f"   📡 WebSocket: {open_result['data']['ws']}")
        print(f"   🌐 HTTP: {open_result['data']['http']}")
        print(f"   🚗 Driver: {open_result['data']['driver']}")
        print(f"   🆔 PID: {open_result['data']['pid']}")
        
        return self.current_window
    
    def close_and_delete_current_window(self):
        """关闭并删除当前窗口"""
        if not self.current_window:
            print("⚠️ 没有活动的窗口")
            return
            
        dir_id = self.current_window['dir_id']
        
        try:
            # 关闭窗口
            print(f"🔒 关闭浏览器窗口: {dir_id}")
            self.client.close_browser(dir_id)
            print("✅ 窗口关闭成功")
            
            # 等待关闭完成
            time.sleep(2)
            
            # 删除窗口
            print(f"🗑️ 删除浏览器窗口: {dir_id}")
            self.client.delete_browser(self.workspace_id, [dir_id])
            print("✅ 窗口删除成功")
            
        except Exception as e:
            print(f"⚠️ 清理窗口时出错: {str(e)}")
        finally:
            self.current_window = None
    
    def get_current_connection_info(self) -> Optional[Dict]:
        """获取当前窗口连接信息"""
        if not self.current_window:
            return None
        return self.current_window.get('connection_info')
    
    def cleanup_all_windows(self):
        """清理所有窗口（紧急清理）"""
        try:
            print("🧹 开始清理所有窗口...")
            
            # 获取所有窗口
            browser_list = self.client.get_browser_list(self.workspace_id)
            windows = browser_list.get('data', {}).get('rows', [])
            
            if not windows:
                print("✅ 没有需要清理的窗口")
                return
                
            # 关闭所有窗口
            for window in windows:
                dir_id = window['dirId']
                try:
                    self.client.close_browser(dir_id)
                    print(f"✅ 关闭窗口: {dir_id}")
                except:
                    pass
                    
            # 等待关闭完成
            time.sleep(3)
            
            # 删除所有窗口
            dir_ids = [window['dirId'] for window in windows]
            if dir_ids:
                self.client.delete_browser(self.workspace_id, dir_ids)
                print(f"✅ 删除 {len(dir_ids)} 个窗口")
                
        except Exception as e:
            print(f"⚠️ 清理窗口时出错: {str(e)}")
