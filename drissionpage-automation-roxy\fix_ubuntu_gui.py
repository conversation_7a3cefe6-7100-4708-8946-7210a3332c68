#!/usr/bin/env python3
"""
Ubuntu GUI模式修复工具
解决DrissionPage在Ubuntu上GUI模式连接失败的问题
"""

import os
import subprocess
import sys
import tempfile
import shutil
from pathlib import Path

def check_display_environment():
    """检查显示环境"""
    print("[SCREEN] 检查显示环境...")
    
    display = os.environ.get('DISPLAY')
    print(f"   DISPLAY环境变量: {display}")
    
    if not display:
        print("   [ERROR] DISPLAY环境变量未设置")
        return False
    
    # 检查X11是否运行
    try:
        result = subprocess.run(['xdpyinfo'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("   [OK] X11显示服务器正常运行")
            return True
        else:
            print("   [ERROR] X11显示服务器无法访问")
            return False
    except Exception as e:
        print(f"   [ERROR] 无法检查X11状态: {e}")
        return False

def fix_chrome_permissions():
    """修复Chrome权限问题"""
    print("[CONFIG] 修复Chrome权限...")
    
    try:
        # 清理可能冲突的Chrome进程
        subprocess.run(['pkill', '-f', 'chrome'], capture_output=True)
        subprocess.run(['pkill', '-f', 'chromium'], capture_output=True)
        print("   [OK] 清理Chrome进程完成")
        
        # 清理Chrome用户数据目录
        chrome_dirs = [
            os.path.expanduser('~/.config/google-chrome'),
            os.path.expanduser('~/.config/chromium'),
            '/tmp/chrome-drissionpage-gui'
        ]
        
        for chrome_dir in chrome_dirs:
            if os.path.exists(chrome_dir):
                try:
                    shutil.rmtree(chrome_dir)
                    print(f"   [OK] 清理目录: {chrome_dir}")
                except Exception as e:
                    print(f"   [WARN] 无法清理目录 {chrome_dir}: {e}")
        
        return True
        
    except Exception as e:
        print(f"   [ERROR] 权限修复失败: {e}")
        return False

def setup_xhost():
    """设置X11权限"""
    print("[LOCK] 设置X11权限...")
    
    try:
        # 允许本地连接
        result = subprocess.run(['xhost', '+local:'], capture_output=True, text=True)
        if result.returncode == 0:
            print("   [OK] X11本地权限设置成功")
            return True
        else:
            print(f"   [ERROR] X11权限设置失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   [ERROR] 无法设置X11权限: {e}")
        return False

def test_chrome_gui():
    """测试Chrome GUI模式"""
    print("🧪 测试Chrome GUI模式...")
    
    try:
        # 创建临时用户数据目录
        user_data_dir = os.path.join(tempfile.gettempdir(), 'chrome-test-gui')
        os.makedirs(user_data_dir, exist_ok=True)
        
        # 测试Chrome启动
        cmd = [
            'google-chrome',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--no-first-run',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-ipc-flooding-protection',
            '--disable-infobars',
            '--disable-extensions',
            f'--user-data-dir={user_data_dir}',
            '--remote-debugging-port=9223',  # 使用不同端口避免冲突
            '--window-size=800,600',
            '--app=https://www.google.com'
        ]
        
        print("   [START] 启动Chrome测试...")
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待几秒看是否成功启动
        import time
        time.sleep(3)
        
        if process.poll() is None:
            print("   [OK] Chrome GUI模式启动成功！")
            
            # 等待用户确认
            input("   👀 请检查是否看到Chrome窗口，然后按Enter继续...")
            
            # 关闭Chrome
            process.terminate()
            process.wait(timeout=5)
            
            # 清理测试目录
            shutil.rmtree(user_data_dir, ignore_errors=True)
            
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"   [ERROR] Chrome启动失败")
            print(f"   错误信息: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"   [ERROR] Chrome测试失败: {e}")
        return False

def test_drissionpage_gui():
    """测试DrissionPage GUI模式"""
    print("🧪 测试DrissionPage GUI模式...")
    
    try:
        # Add parent directory to path
        sys.path.append(str(Path(__file__).parent.parent))
        
        from DrissionPage import ChromiumPage, ChromiumOptions
        
        # 创建选项
        options = ChromiumOptions()
        
        # Ubuntu GUI模式配置
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-setuid-sandbox')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--no-first-run')
        options.set_argument('--disable-web-security')
        options.set_argument('--disable-features=VizDisplayCompositor')
        options.set_argument('--disable-ipc-flooding-protection')
        options.set_argument('--disable-infobars')
        options.set_argument('--disable-extensions')
        options.set_argument('--start-maximized')
        options.set_argument('--window-size=1920,1080')
        
        # 设置用户数据目录
        user_data_dir = os.path.join(tempfile.gettempdir(), 'chrome-drissionpage-test')
        os.makedirs(user_data_dir, exist_ok=True)
        options.set_argument(f'--user-data-dir={user_data_dir}')
        options.set_argument('--remote-debugging-port=9224')
        
        print("   [START] 启动DrissionPage...")
        page = ChromiumPage(addr_or_opts=options)
        
        print("   [OK] DrissionPage GUI模式启动成功！")
        
        # 测试导航
        page.get('https://www.google.com')
        print(f"   [PAGE] 页面标题: {page.title}")
        
        # 等待用户确认
        input("   👀 请检查是否看到DrissionPage控制的Chrome窗口，然后按Enter继续...")
        
        # 关闭浏览器
        page.quit()
        
        # 清理测试目录
        shutil.rmtree(user_data_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"   [ERROR] DrissionPage GUI测试失败: {e}")
        return False

def main():
    """主修复流程"""
    print("[CONFIG] Ubuntu GUI模式修复工具")
    print("=" * 50)
    
    success_count = 0
    total_steps = 5
    
    # 1. 检查显示环境
    if check_display_environment():
        success_count += 1
    print()
    
    # 2. 设置X11权限
    if setup_xhost():
        success_count += 1
    print()
    
    # 3. 修复Chrome权限
    if fix_chrome_permissions():
        success_count += 1
    print()
    
    # 4. 测试Chrome GUI
    if test_chrome_gui():
        success_count += 1
    print()
    
    # 5. 测试DrissionPage GUI
    if test_drissionpage_gui():
        success_count += 1
    print()
    
    # 总结
    print("[CHART] 修复结果:")
    print(f"   成功步骤: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("[SUCCESS] 所有测试通过！GUI模式应该可以正常工作了")
        print("[TIP] 现在可以运行: python3 run_drissionpage_verification.py")
    else:
        print("[WARN] 部分步骤失败，建议使用headless模式")
        print("[TIP] 在.env文件中设置: DRISSIONPAGE_HEADFULL=false")

if __name__ == '__main__':
    main()
