#!/usr/bin/env python3
"""
RoxyBrowser Automation Runner
使用 RoxyBrowser API + Selenium WebDriver 进行完整的 Augment 验证流程
"""

import sys
import time
import re
from pathlib import Path

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

# Selenium imports for verification code input
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from roxybrowser_automation import RoxyBrowserAutomation

# Import shared handlers
try:
    from handlers import AugmentAuth, TokenStorage, OneMailHandler, CaptchaHandler
except ImportError:
    # Try importing from drissionpage-automation-firefox directory
    sys.path.append(str(Path(__file__).parent.parent / 'drissionpage-automation-firefox'))
    from handlers import AugmentAuth, TokenStorage, OneMailHandler, CaptchaHandler

def run_roxybrowser_verification():
    """
    RoxyBrowser Email Verification Runner
    使用 RoxyBrowser 执行完整的邮箱验证流程
    """
    automation = RoxyBrowserAutomation()
    augment_auth = AugmentAuth()
    token_storage = TokenStorage()
    onemail_handler = OneMailHandler()
    captcha_handler = CaptchaHandler()

    start_time = time.time()
    temp_email = None

    try:
        automation.logger.log_flow_start()

        print('🔥 开始 RoxyBrowser 邮箱验证流程')
        print('')

        # 步骤1: 生成授权URL
        print('🔐 步骤1: 生成 Augment 授权 URL')
        auth_url = augment_auth.generate_auth_url()
        print(f'✅ 授权 URL 已生成: {auth_url}')
        print('')

        # 步骤2: 生成临时邮箱
        print('📧 步骤2: 生成临时邮箱')
        temp_email = onemail_handler.generate_email()
        if not temp_email:
            raise Exception('临时邮箱生成失败')
        automation.temp_email = temp_email
        print(f'✅ 临时邮箱生成成功: {temp_email}')
        print('')

        # 步骤3: 启动 RoxyBrowser
        print('🚀 步骤3: 启动 RoxyBrowser')
        if not automation.start_browser():
            raise Exception('RoxyBrowser 启动失败')
        print('')

        # 步骤4: 导航到授权页面
        print('🌐 步骤4: 导航到授权页面')
        if not automation.navigate_to_url(auth_url):
            raise Exception('页面导航失败')
        print('')

        # 步骤5: 输入邮箱
        print('📧 步骤5: 输入邮箱地址')
        if not automation.input_email(temp_email):
            raise Exception('邮箱输入失败')
        print('')

        # 步骤6: 处理验证码
        print('🤖 步骤6: 处理验证码')
        if not automation.handle_captcha(captcha_handler):
            raise Exception('验证码处理失败')
        print('')

        # 步骤7: 点击继续
        print('🔘 步骤7: 点击继续按钮')
        if not automation.click_continue():
            raise Exception('点击继续失败')
        print('')

        # 步骤8: 等待验证邮件
        print('📬 步骤8: 等待验证邮件')
        verification_code = None
        timeout_minutes = 2  # 2分钟超时

        print(f'   📧 等待验证码，超时: {timeout_minutes}分钟...')
        verification_code = onemail_handler.get_verification_code(temp_email, timeout_minutes)
        
        if not verification_code:
            raise Exception('未收到验证邮件或验证码')
        print('')

        # 步骤9: 输入验证码
        print('🔢 步骤9: 输入验证码')
        try:
            # 查找验证码输入框
            code_input = WebDriverWait(automation.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 
                    'input[type="text"], input[name*="code"], input[id*="code"], input[placeholder*="code"]'))
            )
            
            # 输入验证码
            code_input.clear()
            code_input.send_keys(verification_code)
            
            # 触发事件
            automation.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", code_input)
            automation.driver.execute_script("arguments[0].dispatchEvent(new Event('change', {bubbles: true}));", code_input)
            
            automation.logger.save_screenshot(automation.driver, "06_code_entered")
            print('✅ 验证码输入完成')
            
        except Exception as e:
            raise Exception(f'验证码输入失败: {str(e)}')
        print('')

        # 步骤10: 提交验证码
        print('📤 步骤10: 提交验证码')
        if not automation.click_continue():
            raise Exception('验证码提交失败')
        print('')

        # 步骤11: 等待授权完成
        print('⏳ 步骤11: 等待授权完成')
        max_wait_time = 60  # 增加等待时间
        start_wait = time.time()
        auth_code = None

        while time.time() - start_wait < max_wait_time:
            current_url = automation.get_current_url()
            page_title = automation.get_page_title()

            print(f'   🔍 检查授权状态... URL: {current_url[:100]}...')
            print(f'   📝 页面标题: {page_title}')

            # 检查多种授权完成的标志（与Firefox版本相同）
            try:
                # 检查URL中的授权码
                if 'code=' in current_url or 'authorization_code=' in current_url:
                    code_match = re.search(r'[?&]code=([^&]+)', current_url) or re.search(r'[?&]authorization_code=([^&]+)', current_url)
                    if code_match:
                        auth_code = code_match.group(1)
                        print(f'✅ 从URL获取授权码: {auth_code}')
                        break

                # 检查页面元素
                try:
                    copy_button = automation.driver.find_element(By.CSS_SELECTOR, '[data-testid="copy-button"], .copy-button, #copy-button')
                    if copy_button:
                        print('✅ 检测到复制按钮，授权页面已加载')
                        break
                except:
                    pass

                # 检查授权码元素
                try:
                    code_element = automation.driver.find_element(By.CSS_SELECTOR, '[data-testid="authorization-code"], .authorization-code, #authorization-code')
                    if code_element:
                        print('✅ 检测到授权码元素')
                        break
                except:
                    pass

                # 检查页面文本
                page_source = automation.driver.page_source
                has_auth_code = ('authorization_code=' in page_source or
                               'code=' in page_source or
                               'Authorization Code' in page_source or
                               '授权码' in page_source)

                if has_auth_code:
                    print('✅ 页面包含授权码相关内容')
                    break

            except Exception as e:
                print(f'   ⚠️ 检查授权状态时出错: {str(e)}')

            time.sleep(2)
        else:
            raise Exception('授权超时')

        # 如果还没有获取到授权码，尝试从页面提取
        if not auth_code:
            print('🔍 尝试从页面提取授权码...')

            # 尝试从JavaScript提取
            try:
                auth_data = automation.driver.execute_script("""
                    try {
                        // 检查全局变量
                        if (window.authorizationCode) return window.authorizationCode;
                        if (window.code) return window.code;

                        // 检查页面元素
                        const codeElement = document.querySelector('[data-testid="authorization-code"], .authorization-code, #authorization-code');
                        if (codeElement) return codeElement.textContent || codeElement.value;

                        // 检查输入框
                        const inputs = document.querySelectorAll('input[type="text"], textarea');
                        for (let input of inputs) {
                            if (input.value && input.value.length > 20) {
                                return input.value;
                            }
                        }

                        return null;
                    } catch { return null; }
                """)

                if auth_data:
                    auth_code = auth_data
                    print(f'✅ 从JavaScript提取授权码: {auth_code}')
            except Exception as e:
                print(f'⚠️ JavaScript提取失败: {str(e)}')

            # 如果还是没有，从页面内容正则提取
            if not auth_code:
                try:
                    page_content = automation.driver.page_source

                    # 尝试提取JSON格式的授权数据
                    json_match = re.search(r'\{[^}]*"code"\s*:\s*"([^"]+)"[^}]*\}', page_content)
                    if json_match:
                        auth_code = json_match.group(1)
                        print(f'✅ 从页面JSON提取授权码: {auth_code}')
                    else:
                        # 尝试直接提取授权码
                        code_match = re.search(r'code["\s]*[:=]["\s]*([A-Za-z0-9_-]{20,})', page_content)
                        if code_match:
                            auth_code = code_match.group(1)
                            print(f'✅ 从页面内容提取授权码: {auth_code}')
                except Exception as e:
                    print(f'⚠️ 页面内容提取失败: {str(e)}')

        if not auth_code:
            raise Exception('未能获取授权码')

        # 保存授权码
        token_storage.save_auth_code(auth_code)

        automation.logger.save_screenshot(automation.driver, "authorization_success")
        automation.logger.save_html(automation.driver, "authorization_success")
        print('')

        # 成功完成
        end_time = time.time()
        duration = end_time - start_time
        
        print('🎉 RoxyBrowser 验证流程完成!')
        print(f'📧 使用邮箱: {temp_email}')
        print(f'🔑 授权码: {auth_code}')
        print(f'⏱️ 总耗时: {duration:.2f}秒')
        
        automation.logger.log_flow_end(True, duration)
        return True

    except KeyboardInterrupt:
        print('\n⚠️ 用户中断操作')
        return False
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f'\n❌ 验证流程失败: {str(e)}')
        
        # 保存错误截图
        if automation.driver:
            automation.logger.save_screenshot(automation.driver, "ERROR_final_state")
            automation.logger.save_html(automation.driver, "ERROR_final_state")
        
        automation.logger.log_flow_end(False, duration)
        return False
        
    finally:
        # 清理资源
        automation.cleanup()
        
        # 清理临时邮箱
        if temp_email:
            try:
                onemail_handler.delete_email(temp_email)
                print(f'🗑️ 临时邮箱已清理: {temp_email}')
            except:
                pass

def main():
    """主函数"""
    print('🔥 RoxyBrowser Augment 验证工具')
    print('=' * 50)
    
    try:
        success = run_roxybrowser_verification()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print('\n⚠️ 程序被用户中断')
        sys.exit(1)
        
    except Exception as e:
        print(f'\n💥 程序异常: {str(e)}')
        sys.exit(1)

if __name__ == '__main__':
    main()
