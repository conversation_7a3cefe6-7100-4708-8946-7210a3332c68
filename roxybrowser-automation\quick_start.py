#!/usr/bin/env python3
"""
RoxyBrowser 快速启动脚本
快速测试 RoxyBrowser 配置和基本功能
"""

import sys
import time
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from config import RoxyBrowserConfig
from roxybrowser_client import RoxyBrowserClient

def main():
    """主函数"""
    print("🔥 RoxyBrowser 快速启动测试")
    print("=" * 50)
    
    try:
        # 1. 加载配置
        print("🔧 加载配置...")
        config = RoxyBrowserConfig()
        config.print_config()
        print("")
        
        # 2. 测试API连接
        print("🔍 测试 API 连接...")
        client = RoxyBrowserClient(config.api_host, config.api_token)
        
        if not client.health_check():
            print("❌ API 连接失败!")
            print("💡 请检查:")
            print("   1. RoxyBrowser 软件是否启动")
            print("   2. API 功能是否开启")
            print("   3. API Token 是否正确")
            print("   4. API 端口是否正确")
            return False
        
        print("✅ API 连接成功!")
        print("")
        
        # 3. 获取工作空间信息
        print("🏢 获取工作空间信息...")
        try:
            workspaces = client.get_workspaces()
            print(f"✅ 找到 {workspaces['data']['total']} 个工作空间")
            
            # 检查配置的工作空间是否存在
            workspace_found = False
            for workspace in workspaces['data']['rows']:
                if workspace['id'] == config.workspace_id:
                    workspace_found = True
                    print(f"✅ 工作空间验证成功: {workspace['workspaceName']} (ID: {workspace['id']})")
                    break
            
            if not workspace_found:
                print(f"⚠️ 警告: 配置的工作空间 ID {config.workspace_id} 不存在")
                print("💡 可用的工作空间:")
                for workspace in workspaces['data']['rows']:
                    print(f"   🏢 {workspace['workspaceName']} (ID: {workspace['id']})")
                
        except Exception as e:
            print(f"⚠️ 工作空间信息获取失败: {str(e)}")
        
        print("")
        
        # 4. 检查现有浏览器窗口
        print("🪟 检查现有浏览器窗口...")
        try:
            browsers = client.get_browser_list(config.workspace_id)
            window_count = browsers['data']['total']
            print(f"✅ 当前有 {window_count} 个浏览器窗口")
            
            if window_count > 0:
                print("📋 现有窗口列表:")
                for browser in browsers['data']['rows']:
                    print(f"   🪟 {browser['windowName']} (ID: {browser['dirId']})")
                    print(f"      📱 {browser['os']} {browser['osVersion']}, 内核: {browser['coreVersion']}")
                    
        except Exception as e:
            print(f"⚠️ 浏览器窗口信息获取失败: {str(e)}")
        
        print("")
        
        # 5. 测试窗口配置
        print("⚙️ 验证窗口配置...")
        window_config = config.get_window_config()
        print("✅ 窗口配置生成成功")
        print(f"   🪟 窗口名称: {window_config['windowName']}")
        print(f"   📱 操作系统: {window_config['os']} {window_config['osVersion']}")
        print(f"   🔧 内核版本: {window_config['coreVersion']}")
        
        # 检查代理配置
        if 'proxyInfo' in window_config and window_config['proxyInfo']['proxyCategory'] != 'noproxy':
            print(f"   📡 代理: {window_config['proxyInfo']['proxyCategory']} - {window_config['proxyInfo']['host']}:{window_config['proxyInfo']['port']}")
        else:
            print("   📡 代理: 未配置")
        
        print("")
        
        # 6. 成功总结
        print("🎉 RoxyBrowser 配置验证完成!")
        print("✅ 所有基本检查通过")
        print("")
        print("🚀 下一步可以运行:")
        print("   python test_roxybrowser_api.py          # 测试 API 功能")
        print("   python test_basic_functionality.py      # 测试基本功能")
        print("   python run_roxybrowser_verification.py  # 运行完整验证流程")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {str(e)}")
        print("")
        print("💡 请检查:")
        print("   1. .env 文件中的 RoxyBrowser 配置")
        print("   2. RoxyBrowser 软件是否正常运行")
        print("   3. API Token 是否正确设置")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 程序异常: {str(e)}")
        sys.exit(1)
