#!/usr/bin/env python3
"""
简单验证 CthulhuJS 插件状态
"""

import time
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import DrissionPageAutomation
from drissionpage_logger import DrissionPageLogger

def simple_check():
    """简单检查插件状态"""
    logger = DrissionPageLogger()
    logger.log('🔍 简单检查 CthulhuJS 插件状态...')
    
    try:
        # 检查插件文件是否存在
        plugin_dir = Path(__file__).parent / 'cthulhujs_anti_fingerprint'
        manifest_file = plugin_dir / 'manifest.json'
        
        logger.log('📁 检查插件文件:')
        logger.log(f'   插件目录: {"✅ 存在" if plugin_dir.exists() else "❌ 不存在"}')
        logger.log(f'   manifest.json: {"✅ 存在" if manifest_file.exists() else "❌ 不存在"}')
        
        if manifest_file.exists():
            # 读取 manifest 内容
            with open(manifest_file, 'r', encoding='utf-8') as f:
                import json
                manifest = json.load(f)
                logger.log(f'   插件名称: {manifest.get("name", "未知")}')
                logger.log(f'   插件版本: {manifest.get("version", "未知")}')
        
        # 创建自动化实例（不启动浏览器）
        automation = DrissionPageAutomation()
        
        # 检查配置
        logger.log('🔧 检查配置:')
        logger.log(f'   CthulhuJS 插件: {"✅ 启用" if automation.config.cthulhujs_plugin else "❌ 禁用"}')
        logger.log(f'   指纹防护: {"✅ 启用" if automation.config.fingerprint_protection else "❌ 禁用"}')
        
        # 检查插件管理器
        if hasattr(automation, 'cthulhujs_plugin') and automation.cthulhujs_plugin is None:
            logger.log('🐙 CthulhuJS 插件管理器: 未初始化（正常，会在浏览器启动时初始化）')
        
        logger.log('')
        logger.log('🎯 结论:')
        
        if plugin_dir.exists() and manifest_file.exists() and automation.config.cthulhujs_plugin:
            logger.log('✅ CthulhuJS 插件已正确配置并准备就绪')
            logger.log('🛡️ 插件会在浏览器启动时自动加载')
            logger.log('⚠️ "Basic" 显示是正常的 - 这是自动生成的基本版本')
            logger.log('🔧 插件仍然提供反指纹保护功能')
            logger.log('')
            logger.log('💡 解释 "灰色" 和 "Basic":')
            logger.log('   - 这表示使用的是我们自动生成的基本版本')
            logger.log('   - 不是真正的 Chrome Web Store 插件')
            logger.log('   - 但仍然提供有效的反指纹保护')
            logger.log('   - 要获得完整版本，需要手动下载真实插件')
            return True
        else:
            logger.log('❌ CthulhuJS 插件配置有问题')
            return False
            
    except Exception as e:
        logger.log(f'❌ 检查失败: {e}')
        return False

def show_plugin_content():
    """显示插件内容摘要"""
    logger = DrissionPageLogger()
    logger.log('📄 CthulhuJS 插件内容摘要:')
    
    try:
        plugin_dir = Path(__file__).parent / 'cthulhujs_anti_fingerprint'
        inject_file = plugin_dir / 'inject.js'
        
        if inject_file.exists():
            with open(inject_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查关键功能
            features = {
                'WebGL 指纹防护': 'getParameter' in content,
                'Canvas 指纹防护': 'toDataURL' in content,
                'AudioContext 防护': 'AudioContext' in content,
                '屏幕信息随机化': 'screen.width' in content,
                '语言随机化': 'navigator.language' in content,
                '硬件信息随机化': 'hardwareConcurrency' in content,
                '时区随机化': 'getTimezoneOffset' in content
            }
            
            logger.log('🛡️ 检测到的防护功能:')
            for feature, detected in features.items():
                status = "✅ 已实现" if detected else "❌ 未检测到"
                logger.log(f'   {feature}: {status}')
                
            implemented_count = sum(features.values())
            total_count = len(features)
            logger.log(f'📊 功能实现率: {implemented_count}/{total_count} ({implemented_count/total_count*100:.1f}%)')
            
        else:
            logger.log('❌ inject.js 文件不存在')
            
    except Exception as e:
        logger.log(f'❌ 读取插件内容失败: {e}')

if __name__ == '__main__':
    print('🔍 CthulhuJS Anti-Fingerprint 插件状态检查')
    print('=' * 50)
    
    # 简单检查
    success = simple_check()
    
    print('')
    
    # 显示插件内容
    show_plugin_content()
    
    print('')
    print('📖 总结:')
    if success:
        print('✅ CthulhuJS 插件已正确集成并准备工作')
        print('💡 "Basic" 和 "灰色" 显示是正常的')
        print('🛡️ 插件正在提供反指纹保护')
        print('🌟 要获得完整版本，请从 Chrome Web Store 下载')
    else:
        print('❌ 插件配置有问题，请检查')
    
    sys.exit(0 if success else 1)
