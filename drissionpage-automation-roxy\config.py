import os
from dotenv import load_dotenv
from pathlib import Path

# Load .env file from parent directory - 强制重新加载
import os
env_path = Path(__file__).parent.parent / '.env'

# 清除可能的缓存环境变量
proxy_vars = ['PROXY_URL', 'PROXY_USER', 'PROXY_PASS', 'DRISSIONPAGE_PROXY', 'DRISSON_ENHANCED_PROXY']
for var in proxy_vars:
    if var in os.environ:
        del os.environ[var]

load_dotenv(env_path, override=True)  # 强制覆盖现有环境变量

class DrissionPageConfig:
    """
    DrissionPage Automation Configuration
    配置 DrissionPage 自动化的各种选项
    使用根目录的 .env 文件配置
    """
    
    def __init__(self):
        # Enhanced 版本专用代理配置 - 优先使用 DRISSON_ENHANCED_PROXY
        enhanced_proxy = os.getenv('DRISSON_ENHANCED_PROXY', 'false').lower() == 'true'
        fallback_proxy = os.getenv('DRISSIONPAGE_PROXY', 'false').lower() == 'true'

        # Enhanced 版本使用专用配置，如果未设置则回退到通用配置
        self.drissionpage_proxy = enhanced_proxy if os.getenv('DRISSON_ENHANCED_PROXY') else fallback_proxy
        self.drissionpage_recaptcha_solve = os.getenv('DRISSIONPAGE_RECAPTCHA_SOLVE', 'false').lower() == 'true'

        # 指纹防护配置 - Enhanced 版本专用
        self.fingerprint_protection = os.getenv('DRISSON_FINGERPRINT_PROTECTION', 'true').lower() == 'true'

        # RoxyBrowser 集成配置
        self.use_roxybrowser = os.getenv('USE_ROXYBROWSER', 'false').lower() == 'true'
        self.roxybrowser_api_host = os.getenv('ROXYBROWSER_API_HOST', 'http://127.0.0.1:50000')
        self.roxybrowser_api_token = os.getenv('ROXYBROWSER_API_TOKEN')
        self.roxybrowser_workspace_id = int(os.getenv('ROXYBROWSER_WORKSPACE_ID', '1'))
        self.roxybrowser_window_name = os.getenv('ROXYBROWSER_WINDOW_NAME', 'DrissionPage-Auto')
        self.roxybrowser_core_version = os.getenv('ROXYBROWSER_CORE_VERSION', '138')
        self.roxybrowser_os = os.getenv('ROXYBROWSER_OS', 'Windows')
        self.roxybrowser_os_version = os.getenv('ROXYBROWSER_OS_VERSION', '11')
        self.roxybrowser_proxy = os.getenv('ROXYBROWSER_PROXY', 'false').lower() == 'true'
        self.roxybrowser_fingerprint_protection = os.getenv('ROXYBROWSER_FINGERPRINT_PROTECTION', 'true').lower() == 'true'
        self.roxybrowser_random_fingerprint = os.getenv('ROXYBROWSER_RANDOM_FINGERPRINT', 'true').lower() == 'true'

        # 浏览器显示模式配置
        self.headfull = os.getenv('DRISSIONPAGE_HEADFULL', 'false').lower() == 'true'
        
        # 代理配置 (复用现有的代理配置)
        self.proxy_url = os.getenv('PROXY_URL')
        self.proxy_user = os.getenv('PROXY_USER')
        self.proxy_pass = os.getenv('PROXY_PASS')
        
        # YesCaptcha 配置
        self.yescaptcha_client_key = os.getenv('YESCAPTCHA_CLIENT_KEY')
        
        # 调试配置
        self.debug_mode = os.getenv('DEBUG_MODE', 'true').lower() == 'true'
        self.save_screenshots = os.getenv('SAVE_SCREENSHOTS', 'true').lower() == 'true'
        self.save_html = os.getenv('SAVE_HTML', 'true').lower() == 'true'
        
        # 超时配置
        self.page_timeout = int(os.getenv('PAGE_TIMEOUT', '30000'))
        self.email_check_timeout = int(os.getenv('EMAIL_CHECK_TIMEOUT', '120000'))
        self.email_check_interval = int(os.getenv('EMAIL_CHECK_INTERVAL', '5000'))
        
        # 验证配置
        self.validate()

    def validate(self):
        """验证配置"""
        if self.drissionpage_proxy and (not self.proxy_url or not self.proxy_user or not self.proxy_pass):
            print('⚠️ 警告: 启用了代理但代理配置不完整')
            print('💡 请在 .env 文件中设置: PROXY_URL, PROXY_USER, PROXY_PASS')

        if self.drissionpage_recaptcha_solve and not self.yescaptcha_client_key:
            print('⚠️ 警告: 启用了验证码解决但未设置 YesCaptcha 密钥')
            print('💡 请在 .env 文件中设置: YESCAPTCHA_CLIENT_KEY')

        # RoxyBrowser 配置验证
        if self.use_roxybrowser:
            if not self.roxybrowser_api_token:
                print('⚠️ 错误: 启用了 RoxyBrowser 但未设置 API Token')
                print('💡 请在 .env 文件中设置: ROXYBROWSER_API_TOKEN')
                raise ValueError('RoxyBrowser API Token is required when USE_ROXYBROWSER=true')

            if self.roxybrowser_proxy and (not self.proxy_url or not self.proxy_user or not self.proxy_pass):
                print('⚠️ 警告: RoxyBrowser 启用了代理但代理配置不完整')
                print('💡 请在 .env 文件中设置: PROXY_URL, PROXY_USER, PROXY_PASS')
    
    def get_proxy_config(self):
        """获取代理配置（强制重新读取 .env 确保最新值）"""
        if not self.drissionpage_proxy or not self.proxy_url:
            return None

        # 强制重新读取 .env 文件，确保获取最新的用户名和密码
        load_dotenv(env_path, override=True)
        current_proxy_user = os.getenv('PROXY_USER', '')
        current_proxy_pass = os.getenv('PROXY_PASS', '')

        host, port = self.proxy_url.split(':')
        return {
            'host': host,
            'port': int(port),
            'username': current_proxy_user,
            'password': current_proxy_pass
        }
    
    def print_config(self):
        """打印配置信息"""
        print('🔧 DrissionPage + RoxyBrowser 集成配置:')
        print(f'   🖥️ 显示浏览器: {"是" if self.headfull else "否 (headless模式)"}')
        print(f'   🚀 RoxyBrowser 集成: {"启用" if self.use_roxybrowser else "禁用"}')

        if self.use_roxybrowser:
            print(f'   🌐 RoxyBrowser API: {self.roxybrowser_api_host}')
            print(f'   🔑 API Token: {"已设置" if self.roxybrowser_api_token else "未设置"}')
            print(f'   🏢 工作空间ID: {self.roxybrowser_workspace_id}')
            print(f'   🪟 窗口名称: {self.roxybrowser_window_name}')
            print(f'   🖥️ 操作系统: {self.roxybrowser_os} {self.roxybrowser_os_version}')
            print(f'   🔧 内核版本: {self.roxybrowser_core_version}')
            print(f'   📡 RoxyBrowser 代理: {"启用" if self.roxybrowser_proxy else "禁用"}')
            print(f'   🛡️ RoxyBrowser 指纹防护: {"启用" if self.roxybrowser_fingerprint_protection else "禁用"}')
        else:
            # 显示传统代理配置来源
            enhanced_proxy_set = os.getenv('DRISSON_ENHANCED_PROXY') is not None
            proxy_source = "DRISSON_ENHANCED_PROXY" if enhanced_proxy_set else "DRISSIONPAGE_PROXY (fallback)"
            print(f'   📡 传统代理: {"启用" if self.drissionpage_proxy else "禁用"} (来源: {proxy_source})')
            if self.drissionpage_proxy and self.proxy_url:
                print(f'   🌐 代理地址: {self.proxy_url}')
                print(f'   👤 代理用户: {self.proxy_user}')

        print(f'   🤖 验证码解决: {"启用" if self.drissionpage_recaptcha_solve else "禁用"}')
        print(f'   🛡️ DrissionPage 指纹防护: {"启用" if self.fingerprint_protection else "禁用"}')
        print(f'   📸 截图保存: {"启用" if self.save_screenshots else "禁用"}')
        print(f'   📄 HTML保存: {"启用" if self.save_html else "禁用"}')
        print(f'   ⏱️ 页面超时: {self.page_timeout}ms')
        print(f'   📧 邮箱检查超时: {self.email_check_timeout}ms')
