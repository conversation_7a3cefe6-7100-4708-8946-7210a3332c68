import time
import json
import re

def extract_authorization_code(self):
    """提取授权码"""
    self.logger.log('[COPY] 提取授权码...')
    
    try:
        # 等待按钮出现，保证页面资源完全加载
        try:
            copy_button = None
            buttons = self.page.eles('button', timeout=10)
            for btn in buttons:
                text = btn.text.lower()
                if 'copy' in text or '复制' in text:
                    copy_button = btn
                    break
        except:
            pass
        
        # 策略0: 劫持 clipboard.writeText 并点击按钮，直接获取 JSON 字符串
        try:
            hijacked = self.page.run_js("""
                try {
                    window.__authCopied = null;
                    const btn = document.getElementById('copyButton') || Array.from(document.querySelectorAll('button')).find(b => (b.textContent||'').toLowerCase().includes('copy'));
                    if (!btn) return { installed: false };

                    // 尝试覆盖 clipboard.writeText
                    let installed = false;
                    try {
                        const original = navigator.clipboard.writeText;
                        Object.defineProperty(navigator.clipboard, 'writeText', { configurable: true, writable: true, value: (text) => { window.__authCopied = text; return Promise.resolve(); } });
                        installed = true;
                    } catch (e) {
                        // 一些环境不可覆盖，退而求其次：在捕获阶段拦截点击并自行组装
                        btn.addEventListener('click', function () {
                            try {
                                const scripts = Array.from(document.querySelectorAll('script'));
                                for (const s of scripts) {
                                    const c = s.textContent || s.innerHTML || '';
                                    const m = c.match(/let\\s+data\\s*=\\s*(\\{[\\s\\S]*?\\})/);
                                    if (m) {
                                        try {
                                            // 直接 eval 对象字面量（仅作用于匹配到的对象文本）
                                            const obj = (0, eval)('(' + m[1] + ')');
                                            if (obj && obj.code && obj.state && obj.tenant_url) {
                                                window.__authCopied = JSON.stringify(obj);
                                                break;
                                            }
                                        } catch {}
                                    }
                                }
                            } catch {}
                        }, { capture: true, once: true });
                    }

                    btn.click();
                    return { installed };
                } catch (e) {
                    return { installed: false, error: e.message };
                }
            """)
            
            # 给页面时间执行监听
            time.sleep(0.8)
            
            captured = self.page.run_js("return window.__authCopied")
            if captured and captured.strip():
                self.logger.capture_step(self.page, 'authorization_copied_intercepted', '通过拦截clipboard获取授权码')
                self.logger.log(f'[COPY] 通过拦截clipboard获取授权码: {captured[:120]}...')
                return captured.strip()
        except:
            pass
        
        # 策略1: 尝试点击复制按钮 + 本地读取剪贴板
        if copy_button:
            copy_button.click()
            self.logger.log('[OK] 复制按钮点击成功')
            time.sleep(0.8)
            try:
                import pyperclip
                clipboard_content = pyperclip.paste()
                if clipboard_content and clipboard_content.strip():
                    self.logger.capture_step(self.page, 'authorization_copied', '授权码复制完成')
                    self.logger.log(f'[COPY] 从剪贴板获取授权码: {clipboard_content[:120]}...')
                    return clipboard_content.strip()
            except Exception as clipboard_error:
                self.logger.warn('剪贴板读取失败，尝试其他方法')
        
        # 策略2: 从页面JavaScript中提取完整的授权码数据
        auth_data_from_script = self.page.run_js("""
            try {
                const scripts = Array.from(document.querySelectorAll('script'));
                for (let i = 0; i < scripts.length; i++) {
                    const content = scripts[i].textContent || scripts[i].innerHTML || '';
                    // 宽松匹配对象字面量
                    const dataMatch = content.match(/let\\s+data\\s*=\\s*(\\{[\\s\\S]*?\\})/);
                    if (dataMatch) {
                        const objTxt = dataMatch[1];
                        try {
                            const obj = (0, eval)('(' + objTxt + ')');
                            if (obj && obj.code && obj.state && obj.tenant_url) {
                                return JSON.stringify(obj);
                            }
                        } catch {}
                        // 回退：单字段匹配
                        const codeMatch = objTxt.match(/code:\\s*["']([^"']+)["']/);
                        const stateMatch = objTxt.match(/state:\\s*["']([^"']+)["']/);
                        const tenantMatch = objTxt.match(/tenant_url:\\s*["']([^"']+)["']/);
                        if (codeMatch && stateMatch && tenantMatch) {
                            return JSON.stringify({ code: codeMatch[1], state: stateMatch[1], tenant_url: tenantMatch[1] });
                        }
                    }
                    // 跨脚本的单字段匹配
                    const codeMatch = content.match(/["']?code["']?\\s*:\\s*["']([^"']+)["']/);
                    const stateMatch = content.match(/["']?state["']?\\s*:\\s*["']([^"']+)["']/);
                    const tenantMatch = content.match(/["']?tenant_url["']?\\s*:\\s*["']([^"']+)["']/);
                    if (codeMatch && stateMatch && tenantMatch) {
                        return JSON.stringify({ code: codeMatch[1], state: stateMatch[1], tenant_url: tenantMatch[1] });
                    }
                }
                return null;
            } catch { return null; }
        """)
        
        if auth_data_from_script:
            self.logger.capture_step(self.page, 'authorization_extracted_script', '从JavaScript提取授权码完成')
            self.logger.log(f'[TARGET] 从页面JavaScript提取授权码数据: {auth_data_from_script[:120]}...')
            return auth_data_from_script
        
        # 策略3: 从页面URL提取
        current_url = self.page.url
        auth_code_match = re.search(r'[?&]code=([^&]+)', current_url) or re.search(r'[?&]authorization_code=([^&]+)', current_url)
        if auth_code_match:
            auth_code = auth_code_match.group(1)
            self.logger.capture_step(self.page, 'authorization_extracted_url', '从URL提取授权码完成')
            self.logger.log(f'[LINK] 从URL提取授权码: {auth_code}')
            return json.dumps({'code': auth_code, 'state': '', 'tenant_url': ''})
        
        # 策略4: 从页面内容正则提取
        page_content = self.page.html
        
        # 4.1 尝试匹配 let data = {...}
        auth_code_match = re.search(r'let\s+data\s*=\s*(\{[\s\S]*?\})', page_content)
        if auth_code_match:
            try:
                # 这里需要更安全的JSON解析，而不是eval
                obj_text = auth_code_match.group(1)
                # 简单的字段提取
                code_match = re.search(r'code:\s*["\']([^"\']+)["\']', obj_text)
                state_match = re.search(r'state:\s*["\']([^"\']+)["\']', obj_text)
                tenant_match = re.search(r'tenant_url:\s*["\']([^"\']+)["\']', obj_text)
                
                if code_match and state_match and tenant_match:
                    result = {
                        'code': code_match.group(1),
                        'state': state_match.group(1),
                        'tenant_url': tenant_match.group(1)
                    }
                    self.logger.capture_step(self.page, 'authorization_extracted_content', '从页面内容提取授权码完成')
                    return json.dumps(result)
            except:
                pass
        
        # 4.2 直接在整页内容里提取三个字段并组装
        try:
            code_match = re.search(r'["\']?code["\']?\s*:\s*["\']([^"\']+)["\']', page_content)
            state_match = re.search(r'["\']?state["\']?\s*:\s*["\']([^"\']+)["\']', page_content)
            tenant_match = re.search(r'["\']?tenant_url["\']?\s*:\s*["\']([^"\']+)["\']', page_content)
            
            if code_match and state_match and tenant_match:
                payload = {
                    'code': code_match.group(1),
                    'state': state_match.group(1),
                    'tenant_url': tenant_match.group(1)
                }
                self.logger.capture_step(self.page, 'authorization_extracted_content_fields', '从页面内容字段提取授权码完成')
                self.logger.log(f'[PAGE] 从页面字段提取授权码数据: {json.dumps(payload)}')
                return json.dumps(payload)
        except:
            pass
        
        raise Exception('未能提取到授权码')
        
    except Exception as error:
        self.logger.error('[ERROR] 授权码提取失败', error)
        self.logger.capture_error(self.page, 'authorization_extract_failed', str(error))
        raise error

def cleanup(self):
    """清理资源"""
    self.logger.log('[CLEAN] 清理资源...')
    
    try:
        if self.page:
            self.page.quit()
            self.logger.log('[OK] 浏览器已关闭')
    except Exception as error:
        self.logger.error('[ERROR] 资源清理失败', error)
