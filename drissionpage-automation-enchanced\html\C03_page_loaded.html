<!DOCTYPE html><html lang="en"><head>
<meta charset="utf-8">
<meta name="format-detection" content="telephone=no">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Canvas Fingerprinting - BrowserLeaks</title>
<meta name="description" content="Canvas fingerprinting is a tracking method that uses HTML5 Canvas code to generate a unique identifier for each individual user. The method is based on the fact that the unique pixels generated through Canvas code can vary depending on the system and browser used, making it possible to identify users.">
<meta property="og:type" content="website">
<meta property="og:url" content="https://browserleaks.com/canvas">
<meta property="og:site_name" content="BrowserLeaks">
<meta property="og:title" content="Canvas Fingerprinting">
<meta property="og:description" content="Canvas fingerprinting is a tracking method that uses HTML5 Canvas code to generate a unique identifier for each individual user. The method is based on the fact that the unique pixels generated through Canvas code can vary depending on the system and browser used, making it possible to identify users.">
<link type="text/css" rel="stylesheet" media="all" href="/css/style.css?v=59059628">
<noscript><style type="text/css">.script{opacity:.5}.js-hide-block{display:block}</style></noscript>
<script>(function(){var a='.doscript{display:block}.noscript{display:none!important}.script{opacity:1}',b=document.head||document.getElementsByTagName('head')[0],c=document.createElement('style');c.type='text/css';c.styleSheet?c.styleSheet.cssText=a:c.appendChild(document.createTextNode(a));b.appendChild(c)})()</script><style type="text/css">.doscript{display:block}.noscript{display:none!important}.script{opacity:1}</style>
<script src="/js/default.js?v=59059628"></script>
<link rel="icon" sizes="32x32" href="https://browserleaks.com/favicon.ico">
<link rel="icon" sizes="196x196" href="https://browserleaks.com/img/favicons/icon-196x196.png?v=59281755">
<link rel="apple-touch-icon" sizes="180x180" href="https://browserleaks.com/img/favicons/apple-touch-icon.png?v=59281755">
<link rel="manifest" href="https://browserleaks.com/manifest.json?v=59281755">
<meta name="msapplication-config" content="https://browserleaks.com/browserconfig.xml?v=59281755">
</head>
<body spellcheck="false"><div id="logo" class="n-640"><a href="/" title="Home" tabindex="-1"><strong>BrowserLeaks</strong></a></div>

<nav aria-haspopup="true">
<div id="nav">
  <div id="nav-pin" class="n-640">
    <form name="nav-pin-form" id="nav-pin-form" method="POST" action="/canvas" onsubmit="return false">
    <input type="submit" id="nav-pin-submit" name="nav-pin-submit" value="1" title="Toggle Menu">
    </form>
  </div>
  <div id="nav-hover">
    <div id="nav-menu">
      <input id="nav-checkbox" type="checkbox" autocomplete="off" aria-label="Menu" title="Menu">
      <span class="nav-hb"></span><span class="nav-hb"></span><span class="nav-hb"></span>
      <ul>
      <li><div class="nav-close"></div></li>
        <li class="margin"><a class="ico-index" href="/" title="Home Page" rel="nofollow">Home Page</a></li>
        <li><a class="ico-ip" href="/ip" title="What Is My IP Address">IP Address</a></li>
        <li><a class="ico-javascript" href="/javascript" title="JavaScript Browser Information">JavaScript</a></li>
        <li><a class="ico-webrtc" href="/webrtc" title="WebRTC Leak Test">WebRTC Leak Test</a></li>
        <li><a class="this ico-canvas" href="/canvas" title="HTML5 Canvas Fingerprinting" rel="nofollow">Canvas Fingerprint</a></li>
        <li><a class="ico-webgl" href="/webgl" title="WebGL Browser Report">WebGL Report</a></li>
        <li><a class="ico-fonts" href="/fonts" title="Font Fingerprinting">Font Fingerprinting</a></li>
        <li><a class="ico-geo" href="/geo" title="HTML5 Geolocation API and Reverse Geocoding">Geolocation API</a></li>
        <li><a class="ico-features" href="/features" title="HTML5 Features Detection">Features Detection</a></li>
        <li><a class="ico-tls" href="/tls" title="SSL/TLS Client Security Test">SSL/TLS Client Test</a></li>
        <li class="margin-less"><a class="ico-proxy" href="/proxy" title="Content Filtering Proxy Software Detection">Content Filters</a></li>
        <li class="margin-less"><a class="ico-more" href="https://browserleaks.com/#more" title="More Tools" rel="nofollow">More Tools</a></li>
        <li><a class="ico-settings" href="/settings" title="Site Settings" rel="nofollow">Settings</a></li>
      </ul>
      <div class="nav-dark"></div>
    </div>
  </div>
</div>
</nav>

<div class="responsive-header responsive-show">
  <div class="responsive-home"><a href="/" title="Home"><div class="svg-icon svg-fingerprint"></div></a></div>
  <div class="responsive-search"><div class="responsive-search-icon svg-icon svg-search"></div></div>
  <div class="responsive-search-form n"><input type="search" id="responsive-search-input" maxlength="512" aria-label="Enter IP Address or Domain Name" placeholder="Enter IP Address or Domain Name"><input type="button" class="responsive-search-submit n" value=""></div>
</div>

<div id="content">
  <div id="lookup-form" class="n-640">
    <form action="/search" method="POST">
    <ul>
      <li><input tabindex="2" aria-label="IP Address Lookup" placeholder="IP Address Lookup" type="text" id="lookup-input" class="placeholder" name="data" title="Enter IP Address or Domain Name" maxlength="512"></li>
      <li><input tabindex="3" type="submit" id="lookup-submit" class="svg-search" name="lookup-submit" value="" title="Submit"></li>
    </ul>
    </form>
  </div>

<header><h1><a href="/canvas">Canvas Fingerprinting</a></h1><div data-html2canvas-ignore="" id="screenshot" class="svg-photo" title="Take a screenshot of the entire page"></div></header>

<p>The Canvas API, which is designed for drawing graphics via JavaScript and HTML, can also be used for online tracking via browser fingerprinting. This technique relies on variations in how canvas images are rendered on different web browsers and platforms to create a personalized digital fingerprint of a user's browser.</p>

<div class="noscript warn">JavaScript Disabled</div>

<table id="canvas-data" class="script">
<tbody>
  <tr class="thead"><td colspan="5"><h3>Canvas Support Detection</h3></td></tr>
  <tr><td>Canvas 2D API</td><td id="canvas-support-2d"><span class="true">✔</span> True</td></tr>
  <tr><td>Text API for Canvas</td><td id="canvas-support-text"><span class="true">✔</span> True</td></tr>
  <tr><td>Canvas toDataURL</td><td id="canvas-support-todataurl"><span class="true">✔</span> True</td></tr>
  <tr class="thead"><td><h3>Canvas Fingerprint</h3></td></tr>
  <tr><td>Signature</td><td id="canvas-hash" class="wball mono upper">7a2ef827343609eeac3d9c10b31003d6</td></tr>
  <tr><td>Uniqueness</td><td id="canvas-ratio" class="">99.98% (45 of 225657 user agents have the same signature)</td></tr>
</tbody>
</table>

<table id="canvas-file" class="responsive-scroll nxmrg">
<tbody>
  <tr class="thead"><td class="vb"><h3>Image File Details</h3></td><td colspan="4"><div id="canvas-img"><img src="data:image/png;base64,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" alt="&nbsp;Error displaying &lt;img&gt; tag"></div></td></tr>
  <tr><td>File Size</td><td colspan="4" id="canvas-file-size">4712 bytes</td></tr>
  <tr><td>Number of Colors</td><td colspan="4" id="canvas-file-colors">224</td></tr>
  <tr><td class="n-640">PNG Headers</td><td class="thead br"><h3>Chunk</h3></td><td class="thead br"><h3>Length</h3></td><td class="thead br"><h3>CRC</h3></td><td class="thead"><h3>Content</h3></td></tr>
</tbody>
<tbody id="canvas-png" class=""><tr><td class="n-640 nt"></td><td class="br t">IHDR</td><td class="br t">13</td><td class="br t"><span class="mono upper">477a703e</span></td><td class="t wball">PNG image header: 220×30, 8 bits/sample, truecolor+alpha, noninterlaced</td></tr><tr><td class="n-640 nt"></td><td class="br t">sRGB</td><td class="br t">1</td><td class="br t"><span class="mono upper">aece1ce9</span></td><td class="t wball">sRGB color space, rendering intent: Perceptual</td></tr><tr><td class="n-640 nt"></td><td class="br t">IDAT</td><td class="br t">4642</td><td class="br t"><span class="mono upper">b82a42c7</span></td><td class="t wball">PNG image data</td></tr><tr><td class="n-640 nt"></td><td class="br t">IEND</td><td class="br t">0</td><td class="br t"><span class="mono upper">ae426082</span></td><td class="t wball">end-of-image marker</td></tr></tbody>
</table>

<table id="canvas-detect" class="wball nxmrg">
  <tbody><tr class="thead"><td colspan="6"><h3>Signature Stats</h3></td></tr>
  <tr><td id="canvas-detect-line" class="">It's very likely that your web browser is <span id="canvas-detect-browser" class="bold">Chrome</span> and your operating system is <span id="canvas-detect-os" class="bold">Windows</span>.</td></tr>
</tbody></table><table id="canvas-stats" class="responsive-scroll"><tbody><tr><td class="th r" colspan="2"><h3>Operating Systems</h3></td><td class="th r" colspan="2"><h3>Browsers</h3></td><td class="th" colspan="2"><h3>Devices</h3></td></tr><tr><td>Windows 10</td><td class="r">41/45</td><td>Chrome</td><td class="r">28/45</td><td>Desktop</td><td>44/45</td></tr><tr><td>Windows</td><td class="r">3/45</td><td>Microsoft Edge</td><td class="r">11/45</td><td>Apple</td><td>1/45</td></tr><tr><td>Mac</td><td class="r">1/45</td><td>Chromium</td><td class="r">2/45</td><td class="t th" colspan="2"><h3>Platforms</h3></td></tr><tr><td class="t th r" colspan="2"><h3>Engines</h3></td><td>DuckDuckGo Privacy Browser</td><td class="r">2/45</td><td>Win32</td><td>41/45</td></tr><tr><td>Blink 138</td><td class="r">6/45</td><td>Headless Chrome</td><td class="r">1/45</td><td>Linux armv81</td><td>2/45</td></tr><tr><td>Blink 137</td><td class="r">4/45</td><td>Vivaldi</td><td class="r">1/45</td><td>Linux aarch64</td><td>1/45</td></tr><tr><td>Blink 136</td><td class="r">4/45</td><td class="t th r" colspan="2"><h3>Browsers by Version</h3></td><td>Linux armv8l</td><td>1/45</td></tr><tr><td>Blink 134</td><td class="r">4/45</td><td>Chrome 138</td><td class="r">5/45</td><td></td><td></td></tr><tr><td>Blink 133</td><td class="r">4/45</td><td>Chrome 137</td><td class="r">4/45</td><td></td><td></td></tr><tr><td>Blink 135</td><td class="r">3/45</td><td>Chrome 136</td><td class="r">4/45</td><td></td><td></td></tr><tr><td>Blink 130</td><td class="r">3/45</td><td>Chrome 133</td><td class="r">3/45</td><td></td><td></td></tr><tr><td>Blink 120</td><td class="r">3/45</td><td>Chrome 120</td><td class="r">3/45</td><td></td><td></td></tr><tr><td></td><td class="r"></td><td>Chrome 135</td><td class="r">2/45</td><td></td><td></td></tr><tr><td></td><td class="r"></td><td>Chrome 134</td><td class="r">2/45</td><td></td><td></td></tr></tbody></table>

<h3 class="section" id="how-does-it-work" name="how-does-it-work"><a href="#how-does-it-work" title="Click to expand">How Canvas Fingerprinting Works</a></h3>
<div class="none">
  <p>The way an image is rendered on a canvas can vary based on the web browser, operating system, graphics card, and other factors, resulting in a unique image that can be used to create a fingerprint. The way that text is rendered on a canvas can also vary based on the font rendering settings and anti-aliasing algorithms used by different web browsers and operating systems.</p>
  <p>This small animated GIF illustrates the variability of canvas images among 35 different users. Although the JavaScript code remains the same, each frame is distinct due to differences in how the images are rendered on different systems:</p>
  <picture id="canvas-picture">
    <source srcset="/img/canvas/canvas-fingerprinting.png" type="image/apng">
    <source srcset="/img/canvas/canvas-fingerprinting.webp" type="image/webp">
    <img src="/img/canvas/canvas-fingerprinting.gif" alt="Canvas Fingerprinting">
  </picture>
  <p>Here is the JavaScript code that creates our image:</p>
  <ol class="code" contenteditable="true" spellcheck="false">
    <li><span><span class="c">// Text with lowercase/uppercase/punctuation symbols</span></span></li>
    <li><span><span class="z">var</span> txt = <span class="x">"BrowserLeaks,com &lt;canvas&gt; 1.0"</span>;</span></li>
    <li><span>ctx.textBaseline = <span class="x">"top"</span>;</span></li>
    <li><span><span class="c">// The most common type</span></span></li>
    <li><span>ctx.font = <span class="x">"14px 'Arial'"</span>;</span></li>
    <li><span>ctx.textBaseline = <span class="x">"alphabetic"</span>;</span></li>
    <li><span>ctx.fillStyle = <span class="x">"#f60"</span>;</span></li>
    <li><span>ctx.fillRect(125,1,62,20);</span></li>
    <li><span><span class="c">// Some tricks for color mixing to increase the difference in rendering</span></span></li>
    <li><span>ctx.fillStyle = <span class="x">"#069"</span>;</span></li>
    <li><span>ctx.fillText(txt, 2, 15);</span></li>
    <li><span>ctx.fillStyle = <span class="x">"rgba(102, 204, 0, 0.7)"</span>;</span></li>
    <li><span>ctx.fillText(txt, 4, 17);</span></li>
  </ol>
  <p>To generate a signature from the canvas, we need to extract the pixels from the application's memory by calling the toDataURL() function. This function returns a base64-encoded string representing the binary image file. We can then compute an MD5 hash of this string to obtain the canvas fingerprint. Alternatively, we could extract the CRC checksum from the IDAT chunk, which is located 16 to 12 bytes from the end of every PNG file, and use it as our canvas fingerprint.</p>
</div>

<h3 class="section nohistory" id="further-reading" name="further-reading"><a href="#further-reading" title="Click to expand">Further Reading</a></h3>
<div class="none">
<ul class="list">
  <li><a href="https://www.useragentman.com/blog/2009/11/29/how-to-detect-font-smoothing-using-javascript/" rel="noopener" target="_blank">How to Detect Font-Smoothing Using JavaScript</a> <p class="short">The first article about the difference in rendering pixels. Not about fingerprinting, but just after this article it became clear to us that something similar can be done.</p></li>
  <li><a href="https://hovav.net/ucsd/dist/canvas.pdf" rel="noopener" target="_blank">Pixel Perfect: Fingerprinting Canvas in HTML5</a> <p class="short">A well-known study, after which the topic has become known to the public.</p></li>
  <li><a href="https://securehomes.esat.kuleuven.be/~gacar/persistent/" rel="noopener" target="_blank">The Web never forgets: Persistent tracking mechanisms in the wild</a> <p class="short">And now it is used by thousands of sites...</p></li>
  <li><a href="https://addons.mozilla.org/en-US/firefox/addon/canvasblocker/" rel="noopener" target="_blank">CanvasBlocker</a> <p class="short">Firefox add-on that changes the JS-API for modifying &lt;canvas&gt; to prevent Canvas Fingerprinting.</p></li>
  <li><a href="https://en.wikipedia.org/wiki/Canvas_fingerprinting" rel="noopener" target="_blank">Canvas Fingerprinting – Wikipedia</a></li>
  <li><a href="https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API" rel="noopener" target="_blank">Canvas API – MDN</a></li>
  <li><a href="https://caniuse.com/#search=canvas" rel="noopener" target="_blank">Compatibility tables for support of Canvas &amp; WebGL in desktop and mobile browsers – Can I Use</a></li>
</ul>
</div>

<iframe id="canvas-iframe" sandbox="allow-same-origin"></iframe>

<script src="/js/canvas.js?v=59059628"></script>
<h3 data-html2canvas-ignore="" id="comments"><span id="comments-title">Leave a Comment</span> (338)<span id="comments-nojs" class="noscript"></span></h3>
<div data-html2canvas-ignore="" id="remark42"></div>

<footer><div id="footer"><div>BrowserLeaks © 2011-2025 All Right Reserved<br><span id="bdo"><bdo dir="rtl">m<!-- ` -->oc<span>.sk<!-- ` -->ae</span>lr<!-- ` -->es<span>wor<!-- ` -->b@t</span>ca<!-- ` -->tn<span>oc<!-- ` -->:ot</span>lia<!-- ` -->m</bdo></span></div></div></footer>

</div>



</body></html>