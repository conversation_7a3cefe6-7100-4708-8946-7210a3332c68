#!/usr/bin/env python3
"""
Firefox Complete Automation
完整的 Firefox 自动化流程，包含 Turnstile 处理
"""

import sys
import time
import random
from pathlib import Path

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import FirefoxAutomation
from handlers import AugmentAuth, OneMailHandler, TokenStorage

def run_firefox_complete():
    """完整的 Firefox 自动化流程"""
    print('🦊 Firefox 完整自动化流程')
    print('=' * 50)
    
    automation = None
    start_time = time.time()
    
    try:
        # 步骤1: 初始化组件
        print('🔧 步骤1: 初始化组件...')
        automation = FirefoxAutomation()
        augment_auth = AugmentAuth()
        onemail_handler = OneMailHandler()
        token_storage = TokenStorage()
        
        # 步骤2: 生成授权URL
        print('🔐 步骤2: 生成授权 URL...')
        auth_url = augment_auth.generate_auth_url()
        print(f'授权 URL: {auth_url}')
        
        # 步骤3: 生成临时邮箱
        print('📧 步骤3: 生成临时邮箱...')
        temp_email = onemail_handler.generate_email()
        if not temp_email:
            raise Exception('临时邮箱生成失败')
        automation.temp_email = temp_email
        print(f'临时邮箱: {temp_email}')
        
        # 步骤4: 启动浏览器
        print('🦊 步骤4: 启动 Firefox 浏览器...')
        automation.init_browser()
        
        # 步骤5: 导航到授权页面
        print('🌐 步骤5: 导航到授权页面...')
        automation.navigate_to_page(auth_url)
        
        # 步骤6: 输入邮箱
        print('📧 步骤6: 输入邮箱...')
        automation.enter_email(temp_email)
        
        # 步骤7: 处理 Turnstile 验证码
        print('🤖 步骤7: 处理 Turnstile 验证码...')
        automation.handle_captcha()
        
        # 步骤8: 点击 Continue
        print('🔄 步骤8: 点击 Continue...')
        automation.click_continue()
        
        # 步骤9: 等待验证码页面
        print('⏳ 步骤9: 等待验证码页面...')
        current_url = automation.driver.current_url
        print(f'当前URL: {current_url}')

        # 检查是否已经在验证码页面
        if 'passwordless-email-challenge' in current_url:
            print('✅ 已进入验证码页面')
        else:
            # 尝试等待验证页面
            try:
                automation.wait_for_verification_page()
            except Exception as e:
                print(f'⚠️ 等待验证页面失败: {str(e)}')
                # 检查是否有验证码输入框
                from selenium.webdriver.common.by import By
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                try:
                    code_input = WebDriverWait(automation.driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="code"], input[id="code"]'))
                    )
                    if code_input:
                        print('✅ 检测到验证码输入框，继续流程')
                    else:
                        raise Exception('未找到验证码页面或验证码输入框')
                except:
                    raise Exception('未找到验证码页面或验证码输入框')
        
        # 步骤10: 获取验证码
        print('📨 步骤10: 获取邮箱验证码...')
        print('📧 模拟等待邮件到达的行为...')

        # 模拟等待邮件发送的时间（Auth0需要时间发送邮件）- 随机8-15秒
        wait_seconds = random.randint(8, 15)
        print(f'⏰ 等待Auth0发送邮件（{wait_seconds}秒）...')
        time.sleep(wait_seconds)

        print('🔍 开始检查邮箱中的验证码（最多等待2分钟）...')
        verification_code = onemail_handler.get_verification_code(temp_email, 2)
        
        if not verification_code:
            raise Exception('未能获取到验证码')
        
        print(f'✅ 验证码获取成功: {verification_code}')
        
        # 步骤11: 输入验证码
        print('🔢 步骤11: 输入验证码...')
        
        # 查找验证码输入框
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        code_input = WebDriverWait(automation.driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="code"], input[id="code"]'))
        )
        
        # 输入验证码
        code_input.clear()
        code_input.send_keys(verification_code)
        
        # 步骤12: 点击验证码页面的Continue
        print('🔄 步骤12: 点击验证码页面的Continue...')
        
        verify_button = WebDriverWait(automation.driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"], button[name="action"]'))
        )
        verify_button.click()
        time.sleep(5)
        
        # 步骤13: 等待授权码页面
        print('⏳ 步骤13: 等待授权码页面...')
        
        # 检查最终结果
        final_url = automation.driver.current_url
        print(f'验证后 URL: {final_url}')
        
        # 步骤14: 提取授权码
        print('📋 步骤14: 提取授权码...')
        
        authorization_code = None
        if 'code=' in final_url:
            # 从URL中提取授权码
            import re
            match = re.search(r'code=([^&]+)', final_url)
            if match:
                authorization_code = match.group(1)
                print(f'✅ 授权码提取成功: {authorization_code}')
        
        if not authorization_code:
            raise Exception('未能提取到授权码')
        
        # 步骤15: 完成OAuth流程
        print('🔐 步骤15: 完成 OAuth 流程...')
        print('🚀 开始调用真实的 Augment API...')
        
        token_response = augment_auth.complete_oauth_flow(authorization_code)
        
        print('✅ 真实 API 调用成功！获取到真实访问令牌！')
        print(f'🔑 真实访问令牌: {token_response.access_token[:30] if token_response.access_token else "N/A"}...')
        print(f'🏢 租户 URL: {token_response.tenant_url}')
        
        # 步骤16: 保存令牌
        print('💾 步骤16: 保存令牌...')
        token_id = token_storage.add_token(token_response, {
            'description': 'Firefox token from Augment API via email verification',
            'user_agent': 'firefox-email-verification',
            'session_id': f'firefox_session_{int(time.time())}',
            'email': temp_email
        })
        
        print(f'✅ 真实令牌已保存到 tokens.json，ID: {token_id}')
        
        # 成功完成
        duration = time.time() - start_time
        print('\n🎉 Firefox 完整自动化流程成功完成！')
        print(f'⏱️ 总耗时: {duration:.2f}秒')
        print('')
        print('📁 查看详细记录:')
        print(f'   📸 截图目录: screenshots/')
        print(f'   📄 HTML目录: html/')
        print(f'   💾 令牌文件: tokens.json')
        
        # 保持浏览器打开
        input('\n按 Enter 键关闭浏览器...')
        
    except KeyboardInterrupt:
        print('\n⚠️ 用户中断操作')
    except Exception as e:
        print(f'\n❌ Firefox 自动化流程失败: {e}')
        import traceback
        traceback.print_exc()
        
        # 保存调试信息
        if automation and automation.driver:
            try:
                automation.driver.save_screenshot('error_screenshot.png')
                print('📸 已保存错误截图: error_screenshot.png')
            except:
                pass
    finally:
        # 清理资源
        if automation:
            automation.cleanup()
        
        print('👋 程序结束')

if __name__ == '__main__':
    run_firefox_complete()
