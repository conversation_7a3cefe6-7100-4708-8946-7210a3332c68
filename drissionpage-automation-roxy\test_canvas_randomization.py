#!/usr/bin/env python3
"""
Canvas 随机化专项测试
专门测试 Canvas 指纹随机化功能
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from drissionpage_automation import DrissionPageAutomation

def test_canvas_randomization():
    """测试 Canvas 随机化功能"""
    print("🎨 Canvas 指纹随机化专项测试")
    print("=" * 60)
    
    automation = None
    try:
        # 初始化
        print("🚀 启动浏览器...")
        automation = DrissionPageAutomation()
        automation.init_browser()
        
        # 导航到真实网页（触发完整的指纹防护）
        print("📄 导航到测试页面...")
        automation.navigate_to_page('https://httpbin.org/html')
        
        # 等待防护完全激活
        time.sleep(3)
        
        print("\n🧪 执行 Canvas 随机化测试...")
        
        # 测试 1: 基础 Canvas 指纹测试
        print("\n📋 测试 1: 基础 Canvas 指纹")
        basic_fingerprints = []
        for i in range(5):
            fingerprint = automation.page.run_js(f"""
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 50;
                const ctx = canvas.getContext('2d');
                
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillStyle = '#f60';
                ctx.fillRect(10, 5, 100, 20);
                ctx.fillStyle = '#069';
                ctx.fillText('Test {i+1}', 15, 10);
                
                return canvas.toDataURL();
            """)
            
            basic_fingerprints.append(fingerprint[:80] + '...')
            print(f"   指纹 {i+1}: {basic_fingerprints[i]}")
            time.sleep(0.5)
        
        basic_unique = len(set(basic_fingerprints))
        print(f"   结果: {basic_unique}/5 个不同指纹")
        
        # 测试 2: 复杂 Canvas 指纹测试
        print("\n📋 测试 2: 复杂 Canvas 指纹")
        complex_fingerprints = []
        for i in range(5):
            fingerprint = automation.page.run_js(f"""
                const canvas = document.createElement('canvas');
                canvas.width = 300;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                
                // 复杂绘制
                ctx.textBaseline = 'top';
                ctx.font = '16px Arial';
                ctx.fillStyle = '#f60';
                ctx.fillRect(125, 1, 62, 20);
                ctx.fillStyle = '#069';
                ctx.fillText('Canvas Fingerprint Test {i+1} 🎨', 2, 15);
                ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
                ctx.fillText('Random: ' + Math.random(), 4, 40);
                
                // 添加渐变
                const gradient = ctx.createLinearGradient(0, 0, 300, 0);
                gradient.addColorStop(0, 'red');
                gradient.addColorStop(1, 'blue');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 70, 300, 30);
                
                // 添加圆形
                ctx.beginPath();
                ctx.arc(250, 50, 20, 0, 2 * Math.PI);
                ctx.fillStyle = 'yellow';
                ctx.fill();
                
                return canvas.toDataURL();
            """)
            
            complex_fingerprints.append(fingerprint[:80] + '...')
            print(f"   指纹 {i+1}: {complex_fingerprints[i]}")
            time.sleep(0.5)
        
        complex_unique = len(set(complex_fingerprints))
        print(f"   结果: {complex_unique}/5 个不同指纹")
        
        # 测试 3: 文本渲染指纹测试
        print("\n📋 测试 3: 文本渲染指纹")
        text_fingerprints = []
        test_texts = ['Hello World', 'Canvas Test', 'Fingerprint', 'Random Text', 'Test 12345']
        
        for i, text in enumerate(test_texts):
            fingerprint = automation.page.run_js(f"""
                const canvas = document.createElement('canvas');
                canvas.width = 250;
                canvas.height = 60;
                const ctx = canvas.getContext('2d');
                
                ctx.textBaseline = 'top';
                ctx.font = '18px Arial';
                ctx.fillStyle = '#333';
                ctx.fillText('{text}', 10, 20);
                
                // 添加描边文本
                ctx.strokeStyle = '#666';
                ctx.lineWidth = 1;
                ctx.strokeText('{text}', 10, 20);
                
                return canvas.toDataURL();
            """)
            
            text_fingerprints.append(fingerprint[:80] + '...')
            print(f"   文本 '{text}': {text_fingerprints[i]}")
        
        text_unique = len(set(text_fingerprints))
        print(f"   结果: {text_unique}/5 个不同指纹")
        
        # 测试 4: 实时随机化测试
        print("\n📋 测试 4: 实时随机化")
        realtime_fingerprints = []
        for i in range(3):
            fingerprint = automation.page.run_js("""
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 50;
                const ctx = canvas.getContext('2d');
                
                // 使用当前时间戳作为随机种子
                const timestamp = Date.now();
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillStyle = '#f60';
                ctx.fillText('Time: ' + timestamp, 2, 15);
                
                return canvas.toDataURL();
            """)
            
            realtime_fingerprints.append(fingerprint[:80] + '...')
            print(f"   实时 {i+1}: {realtime_fingerprints[i]}")
            time.sleep(1)
        
        realtime_unique = len(set(realtime_fingerprints))
        print(f"   结果: {realtime_unique}/3 个不同指纹")
        
        # 综合分析
        print(f"\n📊 Canvas 随机化分析")
        print("=" * 60)
        
        total_tests = 4
        passed_tests = 0
        
        if basic_unique > 1:
            print("✅ 基础 Canvas 指纹随机化: 有效")
            passed_tests += 1
        else:
            print("❌ 基础 Canvas 指纹随机化: 无效")
        
        if complex_unique > 1:
            print("✅ 复杂 Canvas 指纹随机化: 有效")
            passed_tests += 1
        else:
            print("❌ 复杂 Canvas 指纹随机化: 无效")
        
        if text_unique > 1:
            print("✅ 文本渲染指纹随机化: 有效")
            passed_tests += 1
        else:
            print("❌ 文本渲染指纹随机化: 无效")
        
        if realtime_unique > 1:
            print("✅ 实时指纹随机化: 有效")
            passed_tests += 1
        else:
            print("❌ 实时指纹随机化: 无效")
        
        success_rate = (passed_tests / total_tests) * 100
        print(f"\n📈 随机化成功率: {passed_tests}/{total_tests} ({success_rate:.0f}%)")
        
        if success_rate >= 75:
            print("🎉 Canvas 指纹随机化效果优秀！")
            status = "excellent"
        elif success_rate >= 50:
            print("⚠️ Canvas 指纹随机化效果一般")
            status = "moderate"
        else:
            print("❌ Canvas 指纹随机化效果较差")
            status = "poor"
        
        # 额外的检测测试
        print(f"\n🔍 额外检测测试")
        print("=" * 60)
        
        # 检查是否有随机化脚本注入
        randomization_detected = automation.page.run_js("""
            // 检查是否有随机化相关的修改
            const canvas = document.createElement('canvas');
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            
            // 检查 toDataURL 是否被修改
            const isModified = originalToDataURL.toString().includes('[native code]') === false;
            
            return {
                toDataURLModified: isModified,
                canvasSupported: !!canvas.getContext('2d'),
                randomizationActive: window.console && console.log.toString().includes('Canvas') || false
            };
        """)
        
        print(f"Canvas 支持: {'✅' if randomization_detected.get('canvasSupported') else '❌'}")
        print(f"toDataURL 修改: {'✅' if randomization_detected.get('toDataURLModified') else '❌'}")
        
        return {
            'status': status,
            'success_rate': success_rate,
            'basic_unique': basic_unique,
            'complex_unique': complex_unique,
            'text_unique': text_unique,
            'realtime_unique': realtime_unique,
            'detection': randomization_detected
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None
    finally:
        if automation and automation.page:
            automation.cleanup()

def main():
    """主函数"""
    print("🎨 Canvas 指纹随机化专项测试工具")
    print("=" * 70)
    
    result = test_canvas_randomization()
    
    if result:
        print(f"\n💡 建议和解决方案:")
        if result['status'] == 'excellent':
            print("   - Canvas 随机化工作正常")
            print("   - 继续保持当前配置")
        elif result['status'] == 'moderate':
            print("   - 部分随机化功能正常")
            print("   - 检查脚本注入时机")
            print("   - 验证插件加载状态")
        else:
            print("   - Canvas 随机化未生效")
            print("   - 检查指纹防护配置")
            print("   - 验证 JavaScript 注入")
            print("   - 尝试手动激活随机化")
        
        print(f"\n🔧 故障排除:")
        print("   1. 确认 DRISSON_FINGERPRINT_PROTECTION=true")
        print("   2. 检查浏览器控制台是否有错误")
        print("   3. 验证插件是否正确加载")
        print("   4. 尝试不同的测试网站")
        
        return result['status'] != 'poor'
    else:
        print("\n❌ 测试失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
