#!/usr/bin/env python3
"""
CthulhuJS Anti-Fingerprint 插件测试脚本
用于验证插件是否正确加载和工作
"""

import time
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import DrissionPageAutomation
from drissionpage_logger import DrissionPageLogger
from cthulhujs_plugin import CthulhuJSPlugin

def test_plugin_manager():
    """测试插件管理器基本功能"""
    logger = DrissionPageLogger()
    logger.log('🧪 测试 CthulhuJS 插件管理器...')
    
    try:
        # 创建插件管理器
        plugin_manager = CthulhuJSPlugin(logger)
        
        # 设置插件目录
        if plugin_manager.setup_plugin_directory():
            logger.log('✅ 插件目录设置成功')
            
            # 验证插件
            is_valid, message = plugin_manager.validate_plugin()
            logger.log(f'验证结果: {message}')
            
            if is_valid:
                # 获取插件信息
                info = plugin_manager.get_plugin_info()
                logger.log(f'插件名称: {info["name"]}')
                logger.log(f'插件版本: {info["version"]}')
                logger.log(f'插件路径: {info["path"]}')
                return True
            else:
                logger.log('❌ 插件验证失败')
                return False
        else:
            logger.log('❌ 插件目录设置失败')
            return False
            
    except Exception as e:
        logger.log(f'❌ 插件管理器测试失败: {e}')
        return False

def test_browser_integration():
    """测试浏览器集成"""
    logger = DrissionPageLogger()
    logger.log('🧪 测试浏览器集成...')
    
    try:
        # 创建自动化实例
        automation = DrissionPageAutomation()
        
        # 初始化浏览器
        logger.log('🚀 初始化浏览器...')
        automation.init_browser()
        
        # 检查插件是否加载
        if automation.cthulhujs_plugin:
            logger.log('✅ CthulhuJS 插件管理器已初始化')
        else:
            logger.log('⚠️ CthulhuJS 插件管理器未初始化')
        
        # 导航到测试页面
        logger.log('🌐 导航到测试页面...')
        test_url = 'data:text/html,<html><head><title>CthulhuJS Test</title></head><body><h1>CthulhuJS Anti-Fingerprint Test</h1><script>console.log("Test page loaded");</script></body></html>'
        automation.navigate_to_page(test_url)
        
        # 等待页面加载
        time.sleep(2)
        
        # 执行指纹测试脚本
        logger.log('🔍 执行指纹测试...')
        test_script = '''
        // 测试 CthulhuJS 插件是否工作
        const results = {};
        
        // 检查插件标记
        results.pluginLoaded = !!window.cthulhuJSAntiFingerprint;
        
        // 测试屏幕分辨率随机化
        results.screenWidth = screen.width;
        results.screenHeight = screen.height;
        
        // 测试语言随机化
        results.language = navigator.language;
        results.languages = navigator.languages;
        
        // 测试硬件信息随机化
        results.hardwareConcurrency = navigator.hardwareConcurrency;
        results.deviceMemory = navigator.deviceMemory;
        
        // 测试时区随机化
        results.timezoneOffset = new Date().getTimezoneOffset();
        
        // 测试 Canvas 指纹防护
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('CthulhuJS Test', 2, 2);
        results.canvasFingerprint = canvas.toDataURL();
        
        return results;
        '''
        
        results = automation.page.run_js(test_script)
        
        # 分析测试结果
        logger.log('📊 测试结果分析:')
        
        if results.get('pluginLoaded'):
            logger.log('✅ CthulhuJS 插件已加载并激活')
        else:
            logger.log('⚠️ CthulhuJS 插件未检测到（可能使用基本版本）')
        
        logger.log(f'🖥️ 屏幕尺寸: {results.get("screenWidth")}x{results.get("screenHeight")}')
        logger.log(f'🌍 语言设置: {results.get("language")} {results.get("languages")}')
        logger.log(f'💻 硬件信息: {results.get("hardwareConcurrency")} 核心, {results.get("deviceMemory")}GB 内存')
        logger.log(f'🕐 时区偏移: {results.get("timezoneOffset")} 分钟')
        
        canvas_fp = results.get('canvasFingerprint', '')
        if canvas_fp:
            logger.log(f'🎨 Canvas 指纹: {canvas_fp[:50]}...')
        
        # 关闭浏览器
        automation.cleanup()
        
        logger.log('✅ 浏览器集成测试完成')
        return True
        
    except Exception as e:
        logger.log(f'❌ 浏览器集成测试失败: {e}')
        try:
            automation.cleanup()
        except:
            pass
        return False

def test_fingerprint_randomization():
    """测试指纹随机化效果"""
    logger = DrissionPageLogger()
    logger.log('🧪 测试指纹随机化效果...')
    
    fingerprints = []
    
    try:
        for i in range(3):
            logger.log(f'🔄 第 {i+1} 次测试...')
            
            # 创建新的自动化实例
            automation = DrissionPageAutomation()
            automation.init_browser()
            
            # 导航到测试页面
            test_url = 'data:text/html,<html><body><script>console.log("Fingerprint test");</script></body></html>'
            automation.navigate_to_page(test_url)
            
            time.sleep(1)
            
            # 获取指纹信息
            fingerprint_script = '''
            return {
                screen: screen.width + 'x' + screen.height,
                language: navigator.language,
                hardwareConcurrency: navigator.hardwareConcurrency,
                deviceMemory: navigator.deviceMemory,
                timezoneOffset: new Date().getTimezoneOffset()
            };
            '''
            
            fingerprint = automation.page.run_js(fingerprint_script)
            fingerprints.append(fingerprint)
            
            logger.log(f'   指纹 {i+1}: {fingerprint}')
            
            # 关闭浏览器
            automation.cleanup()
            
            # 短暂等待
            time.sleep(1)
        
        # 分析随机化效果
        logger.log('📊 随机化效果分析:')
        
        unique_screens = set(fp.get('screen') for fp in fingerprints)
        unique_languages = set(fp.get('language') for fp in fingerprints)
        unique_hardware = set(fp.get('hardwareConcurrency') for fp in fingerprints)
        unique_memory = set(fp.get('deviceMemory') for fp in fingerprints)
        unique_timezone = set(fp.get('timezoneOffset') for fp in fingerprints)
        
        logger.log(f'🖥️ 屏幕尺寸变化: {len(unique_screens)}/3 ({len(unique_screens)/3*100:.1f}%)')
        logger.log(f'🌍 语言设置变化: {len(unique_languages)}/3 ({len(unique_languages)/3*100:.1f}%)')
        logger.log(f'💻 硬件信息变化: {len(unique_hardware)}/3 ({len(unique_hardware)/3*100:.1f}%)')
        logger.log(f'💾 内存信息变化: {len(unique_memory)}/3 ({len(unique_memory)/3*100:.1f}%)')
        logger.log(f'🕐 时区信息变化: {len(unique_timezone)}/3 ({len(unique_timezone)/3*100:.1f}%)')
        
        # 计算总体随机化效果
        total_changes = len(unique_screens) + len(unique_languages) + len(unique_hardware) + len(unique_memory) + len(unique_timezone)
        max_changes = 15  # 5 个属性 × 3 次测试
        randomization_rate = total_changes / max_changes * 100
        
        logger.log(f'🎯 总体随机化率: {randomization_rate:.1f}%')
        
        if randomization_rate > 60:
            logger.log('✅ 随机化效果良好')
            return True
        elif randomization_rate > 30:
            logger.log('⚠️ 随机化效果一般')
            return True
        else:
            logger.log('❌ 随机化效果较差')
            return False
            
    except Exception as e:
        logger.log(f'❌ 指纹随机化测试失败: {e}')
        return False

def main():
    """主测试函数"""
    logger = DrissionPageLogger()
    logger.log('🚀 开始 CthulhuJS Anti-Fingerprint 插件测试')
    logger.log('=' * 60)
    
    test_results = []
    
    # 测试 1: 插件管理器
    logger.log('\n📋 测试 1: 插件管理器基本功能')
    logger.log('-' * 40)
    result1 = test_plugin_manager()
    test_results.append(('插件管理器', result1))
    
    # 测试 2: 浏览器集成
    logger.log('\n📋 测试 2: 浏览器集成')
    logger.log('-' * 40)
    result2 = test_browser_integration()
    test_results.append(('浏览器集成', result2))
    
    # 测试 3: 指纹随机化
    logger.log('\n📋 测试 3: 指纹随机化效果')
    logger.log('-' * 40)
    result3 = test_fingerprint_randomization()
    test_results.append(('指纹随机化', result3))
    
    # 总结测试结果
    logger.log('\n📊 测试结果总结')
    logger.log('=' * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = '✅ 通过' if result else '❌ 失败'
        logger.log(f'{test_name}: {status}')
        if result:
            passed += 1
    
    logger.log('-' * 40)
    logger.log(f'总计: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)')
    
    if passed == total:
        logger.log('🎉 所有测试通过！CthulhuJS 插件工作正常')
        return True
    else:
        logger.log('⚠️ 部分测试失败，请检查配置和日志')
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
