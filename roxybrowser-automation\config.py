import os
from dotenv import load_dotenv
from pathlib import Path

# Load .env file from parent directory - 强制重新加载
env_path = Path(__file__).parent.parent / '.env'

# 清除可能的缓存环境变量
proxy_vars = ['PROXY_URL', 'PROXY_USER', 'PROXY_PASS', 'ROXYBROWSER_PROXY']
for var in proxy_vars:
    if var in os.environ:
        del os.environ[var]

load_dotenv(env_path, override=True)  # 强制覆盖现有环境变量

class RoxyBrowserConfig:
    """
    RoxyBrowser API Automation Configuration
    配置 RoxyBrowser API 自动化的各种选项
    使用根目录的 .env 文件配置
    """

    def __init__(self):
        # RoxyBrowser API 基础配置
        self.api_host = os.getenv('ROXYBROWSER_API_HOST', 'http://127.0.0.1:50000')
        self.api_token = os.getenv('ROXYBROWSER_API_TOKEN')
        
        # 代理配置
        self.roxybrowser_proxy = os.getenv('ROXYBROWSER_PROXY', 'false').lower() == 'true'
        self.roxybrowser_recaptcha_solve = os.getenv('ROXYBROWSER_RECAPTCHA_SOLVE', 'false').lower() == 'true'
        
        # 浏览器显示模式配置
        self.headfull = os.getenv('ROXYBROWSER_HEADFULL', 'true').lower() == 'true'
        
        # 代理配置 (复用现有的代理配置)
        self.proxy_url = os.getenv('PROXY_URL')
        self.proxy_user = os.getenv('PROXY_USER')
        self.proxy_pass = os.getenv('PROXY_PASS')
        
        # YesCaptcha 配置
        self.yescaptcha_client_key = os.getenv('YESCAPTCHA_CLIENT_KEY')
        
        # 调试配置
        self.debug_mode = os.getenv('DEBUG_MODE', 'true').lower() == 'true'
        self.save_screenshots = os.getenv('SAVE_SCREENSHOTS', 'true').lower() == 'true'
        self.save_html = os.getenv('SAVE_HTML', 'true').lower() == 'true'
        
        # 超时配置
        self.page_timeout = int(os.getenv('PAGE_TIMEOUT', '30000'))
        self.email_check_timeout = int(os.getenv('EMAIL_CHECK_TIMEOUT', '120000'))
        self.email_check_interval = int(os.getenv('EMAIL_CHECK_INTERVAL', '5000'))
        
        # RoxyBrowser 窗口配置
        self.workspace_id = int(os.getenv('ROXYBROWSER_WORKSPACE_ID', '1'))
        self.window_name = os.getenv('ROXYBROWSER_WINDOW_NAME', 'Augment-Auto')
        self.core_version = os.getenv('ROXYBROWSER_CORE_VERSION', '125')
        self.os_type = os.getenv('ROXYBROWSER_OS', 'Windows')
        self.os_version = os.getenv('ROXYBROWSER_OS_VERSION', '11')
        
        # 指纹配置
        self.fingerprint_protection = os.getenv('ROXYBROWSER_FINGERPRINT_PROTECTION', 'true').lower() == 'true'
        self.random_fingerprint = os.getenv('ROXYBROWSER_RANDOM_FINGERPRINT', 'true').lower() == 'true'
        
        # 验证配置
        self.validate()
    
    def validate(self):
        """验证配置"""
        if not self.api_token:
            print('⚠️ 错误: 未设置 RoxyBrowser API Token')
            print('💡 请在 .env 文件中设置: ROXYBROWSER_API_TOKEN')
            raise ValueError('RoxyBrowser API Token is required')
            
        if self.roxybrowser_proxy and (not self.proxy_url or not self.proxy_user or not self.proxy_pass):
            print('⚠️ 警告: 启用了代理但代理配置不完整')
            print('💡 请在 .env 文件中设置: PROXY_URL, PROXY_USER, PROXY_PASS')
        
        if self.roxybrowser_recaptcha_solve and not self.yescaptcha_client_key:
            print('⚠️ 警告: 启用了验证码解决但未设置 YesCaptcha 密钥')
            print('💡 请在 .env 文件中设置: YESCAPTCHA_CLIENT_KEY')
    
    def get_proxy_config(self):
        """获取代理配置（强制重新读取 .env 确保最新值）"""
        if not self.roxybrowser_proxy or not self.proxy_url:
            return None

        # 强制重新读取 .env 文件，确保获取最新的用户名和密码
        load_dotenv(env_path, override=True)
        current_proxy_user = os.getenv('PROXY_USER', '')
        current_proxy_pass = os.getenv('PROXY_PASS', '')

        host, port = self.proxy_url.split(':')
        return {
            'host': host,
            'port': int(port),
            'username': current_proxy_user,
            'password': current_proxy_pass
        }
    
    def get_window_config(self):
        """获取 RoxyBrowser 窗口创建配置"""
        config = {
            "workspaceId": self.workspace_id,
            "windowName": self.window_name,
            "coreVersion": self.core_version,
            "os": self.os_type,
            "osVersion": self.os_version,
            "fingerInfo": {
                "randomFingerprint": self.random_fingerprint,
                "forbidSavePassword": True,
                "clearCacheFile": True,
                "clearCookie": True,
                "clearLocalStorage": True,
                "webRTC": 2,  # 禁止
                "webGL": True,  # 随机
                "canvas": True,  # 随机
                "audioContext": True,  # 随机
                "doNotTrack": True,
                "clientRects": True,
                "deviceInfo": True,
                "deviceNameSwitch": True,
                "macInfo": True,
                "portScanProtect": True,
                "useGpu": True,
                "sandboxPermission": False
            }
        }
        
        # 添加代理配置
        proxy_config = self.get_proxy_config()
        if proxy_config:
            config["proxyInfo"] = {
                "proxyMethod": "custom",
                "proxyCategory": "SOCKS5",
                "ipType": "IPV4",
                "host": proxy_config['host'],
                "port": str(proxy_config['port']),
                "proxyUserName": proxy_config['username'],
                "proxyPassword": proxy_config['password']
            }
        else:
            config["proxyInfo"] = {
                "proxyMethod": "custom",
                "proxyCategory": "noproxy"
            }
            
        return config
    
    def print_config(self):
        """打印配置信息"""
        print('🔧 RoxyBrowser 配置:')
        print(f'   🌐 API Host: {self.api_host}')
        print(f'   🔑 API Token: {"已设置" if self.api_token else "未设置"}')
        print(f'   🏢 工作空间ID: {self.workspace_id}')
        print(f'   🪟 窗口名称: {self.window_name}')
        print(f'   🖥️ 操作系统: {self.os_type} {self.os_version}')
        print(f'   🔧 内核版本: {self.core_version}')
        print(f'   📡 代理: {"启用" if self.roxybrowser_proxy else "禁用"}')
        if self.roxybrowser_proxy and self.proxy_url:
            print(f'   🌐 代理地址: {self.proxy_url}')
            print(f'   👤 代理用户: {self.proxy_user}')
        print(f'   🤖 验证码解决: {"启用" if self.roxybrowser_recaptcha_solve else "禁用"}')
        print(f'   🛡️ 指纹防护: {"启用" if self.fingerprint_protection else "禁用"}')
        print(f'   🎲 随机指纹: {"启用" if self.random_fingerprint else "禁用"}')
        print(f'   📸 截图保存: {"启用" if self.save_screenshots else "禁用"}')
        print(f'   📄 HTML保存: {"启用" if self.save_html else "禁用"}')
        print(f'   ⏱️ 页面超时: {self.page_timeout}ms')
        print(f'   📧 邮箱检查超时: {self.email_check_timeout}ms')
