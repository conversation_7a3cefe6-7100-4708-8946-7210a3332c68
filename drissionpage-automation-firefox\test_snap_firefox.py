#!/usr/bin/env python3
"""
测试 snap Firefox 路径
"""

import os
import sys
from pathlib import Path

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import FirefoxAutomation

def test_snap_firefox():
    """测试 snap Firefox 路径"""
    print("🦊 测试 snap Firefox 路径")
    print("=" * 30)
    
    # 直接设置环境变量
    snap_firefox_path = "/snap/firefox/current/usr/lib/firefox/firefox"
    os.environ['FIREFOX_BINARY_PATH'] = snap_firefox_path
    
    print(f"🎯 设置 Firefox 路径: {snap_firefox_path}")
    
    # 检查文件是否存在
    if os.path.exists(snap_firefox_path):
        print("✅ Firefox 文件存在")
    else:
        print("❌ Firefox 文件不存在")
        return False
    
    # 检查是否可执行
    if os.access(snap_firefox_path, os.X_OK):
        print("✅ Firefox 文件可执行")
    else:
        print("❌ Firefox 文件不可执行")
        return False
    
    try:
        # 初始化 Firefox 自动化
        print("🔧 初始化 Firefox 自动化...")
        automation = FirefoxAutomation()
        
        print("🚀 启动 Firefox 浏览器...")
        automation.init_browser()
        
        print("✅ Firefox 浏览器启动成功！")
        
        # 测试导航
        print("🌐 测试页面导航...")
        automation.driver.get("https://www.google.com")
        
        print(f"📄 页面标题: {automation.driver.title}")
        
        # 清理
        automation.cleanup()
        print("✅ 测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_snap_firefox()
    if success:
        print("\n🎉 snap Firefox 测试成功！")
        print("现在可以运行完整的自动化流程了")
    else:
        print("\n❌ snap Firefox 测试失败")
        sys.exit(1)
