function l(n,t){let i=t.split(/[.\[\]]/).filter(u=>u!==""),e=n;for(let u of i){if(e[u]===void 0)return;e=e[u]}return e}function r(n){return JSON.parse(JSON.stringify(n))}function f(n=""){let t=[],i=0;for(let e=0;e<n.length;e++){let u=n[e];if(u==="."||u==="["){let s=n.substring(i,e);t.push(s),i=e+1;continue}if(u==="]"){let s=n.substring(i,e);t.push(Number(s)),i=e+1}}if(i<n.length){let e=n.substring(i);n[i-1]==="["?t.push(Number(e)):t.push(e)}return t}function o(n=[]){let t="";for(let i=0;i<n.length;i++){let e=n[i];e===""||e===void 0||e===null||(Number.isInteger(e)?t+="["+e+"]":t+=e+".")}return t.endsWith(".")&&(t=t.substring(0,t.length-1)),t}export{o as a,r as c,l as g,f as s};
