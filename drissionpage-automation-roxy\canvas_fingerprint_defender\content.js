
// Enhanced Fingerprint Defender Content Script
(function() {
    'use strict';

    console.log('🛡️ Enhanced Fingerprint Defender content script loaded');

    // 移除 WebDriver 相关属性
    try {
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
        });

        // 清理 Selenium 变量
        const seleniumVars = [
            '$cdc_asdjflasutopfhvcZLmcfl_', '$chrome_asyncScriptInfo', '__webdriver_script_fn',
            '__driver_evaluate', '__webdriver_evaluate', '__selenium_evaluate', '__fxdriver_evaluate',
            '__driver_unwrapped', '__webdriver_unwrapped', '__selenium_unwrapped', '__fxdriver_unwrapped',
            '_Selenium_IDE_Recorder', 'calledSelenium', 'calledPhantom', '__nightmare', '_phantom'
        ];

        seleniumVars.forEach(varName => {
            try {
                delete window[varName];
                delete window.document[varName];
            } catch(e) {}
        });

        // 伪装 Chrome 对象
        if (!window.chrome) {
            window.chrome = {
                runtime: {
                    onConnect: undefined,
                    onMessage: undefined
                },
                app: {
                    isInstalled: false,
                    InstallState: {
                        DISABLED: 'disabled',
                        INSTALLED: 'installed',
                        NOT_INSTALLED: 'not_installed'
                    }
                }
            };
        }

        // Canvas 指纹随机化
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function(...args) {
            const context = this.getContext('2d');
            if (context) {
                // 添加随机噪声
                const imageData = context.getImageData(0, 0, this.width, this.height);
                for (let i = 0; i < imageData.data.length; i += 4) {
                    if (Math.random() < 0.01) { // 1% 的像素添加噪声
                        imageData.data[i] = Math.min(255, Math.max(0, imageData.data[i] + (Math.random() * 4 - 2)));
                        imageData.data[i + 1] = Math.min(255, Math.max(0, imageData.data[i + 1] + (Math.random() * 4 - 2)));
                        imageData.data[i + 2] = Math.min(255, Math.max(0, imageData.data[i + 2] + (Math.random() * 4 - 2)));
                    }
                }
                context.putImageData(imageData, 0, 0);
            }
            return originalToDataURL.apply(this, args);
        };

        // Canvas 绘制随机化
        const originalFillText = CanvasRenderingContext2D.prototype.fillText;
        CanvasRenderingContext2D.prototype.fillText = function(text, x, y, maxWidth) {
            const noise = () => Math.random() * 0.1 - 0.05;
            return originalFillText.call(this, text, x + noise(), y + noise(), maxWidth);
        };

        const originalStrokeText = CanvasRenderingContext2D.prototype.strokeText;
        CanvasRenderingContext2D.prototype.strokeText = function(text, x, y, maxWidth) {
            const noise = () => Math.random() * 0.1 - 0.05;
            return originalStrokeText.call(this, text, x + noise(), y + noise(), maxWidth);
        };

        // WebGL 指纹伪装
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            switch (parameter) {
                case this.VENDOR:
                    return 'Google Inc. (Intel)';
                case this.RENDERER:
                    return 'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)';
                case this.VERSION:
                    return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
                case this.SHADING_LANGUAGE_VERSION:
                    return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
                default:
                    return originalGetParameter.call(this, parameter);
            }
        };

        console.log('🛡️ Enhanced fingerprint protection activated');
    } catch (e) {
        console.log('Fingerprint protection error:', e);
    }
})();
