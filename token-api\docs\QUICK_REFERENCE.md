# Token API Quick Reference

## Base Configuration
```
URL: http://localhost:9043
Auth: Bearer your_secret_password_here_change_this
```

## Endpoints Summary

| Method | Endpoint | Auth | Description |
|--------|----------|------|-------------|
| `GET` | `/health` | ❌ | Health check |
| `GET` | `/api/tokens` | ✅ | Get unused token |
| `POST` | `/api/tokens/save` | ✅ | Save new token |
| `GET` | `/api/tokens/valid-count` | ✅ | Get valid token count |
| `GET` | `/api/tokens/stats` | ✅ | Get token statistics |
| `POST` | `/api/tokens/trigger-automation` | ✅ | Trigger automation |

## Quick Commands

### cURL Commands
```bash
# Health check
curl http://localhost:9043/health

# Get valid count
curl -H "Authorization: Bearer your_secret_password_here_change_this" \
     http://localhost:9043/api/tokens/valid-count

# Save token
curl -X POST \
     -H "Authorization: Bearer your_secret_password_here_change_this" \
     -H "Content-Type: application/json" \
     -d '{"access_token":"TOKEN","tenant_url":"URL"}' \
     http://localhost:9043/api/tokens/save

# Get unused token
curl -H "Authorization: Bearer your_secret_password_here_change_this" \
     http://localhost:9043/api/tokens

# Get stats
curl -H "Authorization: Bearer your_secret_password_here_change_this" \
     http://localhost:9043/api/tokens/stats

# Trigger automation
curl -X POST \
     -H "Authorization: Bearer your_secret_password_here_change_this" \
     -H "Content-Type: application/json" \
     -d '{"count":1}' \
     http://localhost:9043/api/tokens/trigger-automation
```

### Python One-Liners
```python
import requests

# Setup
headers = {'Authorization': 'Bearer your_secret_password_here_change_this', 'Content-Type': 'application/json'}
base = 'http://localhost:9043'

# Get valid count
requests.get(f'{base}/api/tokens/valid-count', headers=headers).json()['validCount']

# Save token
requests.post(f'{base}/api/tokens/save', headers=headers, json={'access_token':'TOKEN','tenant_url':'URL'}).json()

# Get unused token
requests.get(f'{base}/api/tokens', headers=headers).json()['token']

# Get stats
requests.get(f'{base}/api/tokens/stats', headers=headers).json()['stats']
```

### JavaScript One-Liners
```javascript
const axios = require('axios');
const headers = {'Authorization': 'Bearer your_secret_password_here_change_this'};
const base = 'http://localhost:9043';

// Get valid count
(await axios.get(`${base}/api/tokens/valid-count`, {headers})).data.validCount

// Save token
(await axios.post(`${base}/api/tokens/save`, {access_token:'TOKEN',tenant_url:'URL'}, {headers})).data

// Get unused token
(await axios.get(`${base}/api/tokens`, {headers})).data.token

// Get stats
(await axios.get(`${base}/api/tokens/stats`, {headers})).data.stats
```

## Response Formats

### Success Response
```json
{
  "success": true,
  "data": "...",
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error Type",
  "message": "Error description",
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

## Status Codes
- `200` - Success
- `400` - Bad Request (missing fields)
- `401` - Unauthorized (invalid auth)
- `404` - Not Found (no tokens)
- `500` - Server Error

## Required Fields for Save Token
```json
{
  "access_token": "string (required)",
  "tenant_url": "string (required)"
}
```

## Optional Fields for Save Token
```json
{
  "id": "string (auto-generated)",
  "description": "string",
  "email_note": "string", 
  "user_agent": "string",
  "session_id": "string",
  "created_timestamp": "number"
}
```

## Token Validity Rules
- **Valid Period**: 7 days from creation
- **Buffer**: 2 hours before expiration
- **Effective Valid Time**: 6 days 22 hours
- **Status**: Must be unused (`used = false`)

## Configuration Files
- **Config**: `token-api/config.json`
- **Environment**: `.env` (AUTH_PASSWORD, TOKEN_API_PORT)
- **Database**: `token-api/tokens.db` (SQLite)
- **Logs**: `logs/summary.log`

## Server Management
```bash
# Start server
npm run token-api

# Start with wrapper
npm run start-token-api

# Migrate existing tokens
npm run migrate-tokens
```

## Monitoring
```bash
# Check logs
tail -f logs/summary.log

# Check database
sqlite3 token-api/tokens.db "SELECT COUNT(*) FROM tokens WHERE used = 0;"

# Check valid tokens via API
curl -H "Authorization: Bearer your_secret_password_here_change_this" \
     http://localhost:9043/api/tokens/valid-count
```
