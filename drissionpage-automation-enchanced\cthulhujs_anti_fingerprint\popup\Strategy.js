import{I as ke,_ as re,k as X,r as _,o as V,g as j,F as C,p as M,c as S,w,h as ie,a as A,t as b,L as Z,e as R,l as ee,d as n,u as d,J as Te,f as $e,z as je,b as c,B as Ee,q as Le,x as se,s as Ie,j as Ae,v as E,K as Ce}from"./popup.js";import{g as de,e as Me,p as le,q as Se,t as He,u as Pe,v as Be,w as De,x as Re,l as We,y as T,z as qe,A as Ne,B as Fe,C as Oe,D as Je,E as Qe}from"./logo.js";var Ge=function(a,i){i===!0&&(i=0);var x="";if(typeof a=="string")try{x=new URL(a).protocol}catch{}else a&&a.constructor===URL&&(x=a.protocol);var e=x.split(/\:|\+/).filter(Boolean);return typeof i=="number"?e[i]:e},Ke=Ge;function Xe(r){var a={protocols:[],protocol:null,port:null,resource:"",host:"",user:"",password:"",pathname:"",hash:"",search:"",href:r,query:{},parse_failed:!1};try{var i=new URL(r);a.protocols=Ke(i),a.protocol=a.protocols[0],a.port=i.port,a.resource=i.hostname,a.host=i.host,a.user=i.username||"",a.password=i.password||"",a.pathname=i.pathname,a.hash=i.hash.slice(1),a.search=i.search.slice(1),a.href=i.href,a.query=Object.fromEntries(i.searchParams)}catch{a.protocols=["file"],a.protocol=a.protocols[0],a.port="",a.resource="",a.user="",a.pathname="",a.hash="",a.search="",a.href=r,a.query={},a.parse_failed=!0}return a}var Ye=Xe;const Ze=ke(Ye),et="text/plain",tt="us-ascii",ne=(r,a)=>a.some(i=>i instanceof RegExp?i.test(r):i===r),ot=(r,{stripHash:a})=>{const i=/^data:(?<type>[^,]*?),(?<data>[^#]*?)(?:#(?<hash>.*))?$/.exec(r);if(!i)throw new Error(`Invalid URL: ${r}`);let{type:x,data:e,hash:f}=i.groups;const h=x.split(";");f=a?"":f;let z=!1;h[h.length-1]==="base64"&&(h.pop(),z=!0);const u=(h.shift()||"").toLowerCase(),g=[...h.map(t=>{let[k,m=""]=t.split("=").map(U=>U.trim());return k==="charset"&&(m=m.toLowerCase(),m===tt)?"":`${k}${m?`=${m}`:""}`}).filter(Boolean)];return z&&g.push("base64"),(g.length>0||u&&u!==et)&&g.unshift(u),`data:${g.join(";")},${z?e.trim():e}${f?`#${f}`:""}`};function at(r,a){if(a={defaultProtocol:"http:",normalizeProtocol:!0,forceHttp:!1,forceHttps:!1,stripAuthentication:!0,stripHash:!1,stripTextFragment:!0,stripWWW:!0,removeQueryParameters:[/^utm_\w+/i],removeTrailingSlash:!0,removeSingleSlash:!0,removeDirectoryIndex:!1,sortQueryParameters:!0,...a},r=r.trim(),/^data:/i.test(r))return ot(r,a);if(/^view-source:/i.test(r))throw new Error("`view-source:` is not supported as it is a non-standard protocol");const i=r.startsWith("//");!i&&/^\.*\//.test(r)||(r=r.replace(/^(?!(?:\w+:)?\/\/)|^\/\//,a.defaultProtocol));const e=new URL(r);if(a.forceHttp&&a.forceHttps)throw new Error("The `forceHttp` and `forceHttps` options cannot be used together");if(a.forceHttp&&e.protocol==="https:"&&(e.protocol="http:"),a.forceHttps&&e.protocol==="http:"&&(e.protocol="https:"),a.stripAuthentication&&(e.username="",e.password=""),a.stripHash?e.hash="":a.stripTextFragment&&(e.hash=e.hash.replace(/#?:~:text.*?$/i,"")),e.pathname){const h=/\b[a-z][a-z\d+\-.]{1,50}:\/\//g;let z=0,u="";for(;;){const g=h.exec(e.pathname);if(!g)break;const t=g[0],k=g.index,m=e.pathname.slice(z,k);u+=m.replace(/\/{2,}/g,"/"),u+=t,z=k+t.length}const y=e.pathname.slice(z,e.pathname.length);u+=y.replace(/\/{2,}/g,"/"),e.pathname=u}if(e.pathname)try{e.pathname=decodeURI(e.pathname)}catch{}if(a.removeDirectoryIndex===!0&&(a.removeDirectoryIndex=[/^index\.[a-z]+$/]),Array.isArray(a.removeDirectoryIndex)&&a.removeDirectoryIndex.length>0){let h=e.pathname.split("/");const z=h[h.length-1];ne(z,a.removeDirectoryIndex)&&(h=h.slice(0,-1),e.pathname=h.slice(1).join("/")+"/")}if(e.hostname&&(e.hostname=e.hostname.replace(/\.$/,""),a.stripWWW&&/^www\.(?!www\.)[a-z\-\d]{1,63}\.[a-z.\-\d]{2,63}$/.test(e.hostname)&&(e.hostname=e.hostname.replace(/^www\./,""))),Array.isArray(a.removeQueryParameters))for(const h of[...e.searchParams.keys()])ne(h,a.removeQueryParameters)&&e.searchParams.delete(h);if(a.removeQueryParameters===!0&&(e.search=""),a.sortQueryParameters){e.searchParams.sort();try{e.search=decodeURIComponent(e.search)}catch{}}a.removeTrailingSlash&&(e.pathname=e.pathname.replace(/\/$/,""));const f=r;return r=e.toString(),!a.removeSingleSlash&&e.pathname==="/"&&!f.endsWith("/")&&e.hash===""&&(r=r.replace(/\/$/,"")),(a.removeTrailingSlash||e.pathname==="/")&&e.hash===""&&a.removeSingleSlash&&(r=r.replace(/\/$/,"")),i&&!a.normalizeProtocol&&(r=r.replace(/^http:\/\//,"//")),a.stripProtocol&&(r=r.replace(/^(?:https?:)?\/\//,"")),r}const te=(r,a=!1)=>{const i=/^(?:([a-z_][a-z0-9_-]{0,31})@|https?:\/\/)([\w\.\-@]+)[\/:]([\~,\.\w,\-,\_,\/]+?(?:\.git|\/)?)$/,x=f=>{const h=new Error(f);throw h.subject_url=r,h};(typeof r!="string"||!r.trim())&&x("Invalid url."),r.length>te.MAX_INPUT_LENGTH&&x("Input exceeds maximum length. If needed, change the value of parseUrl.MAX_INPUT_LENGTH."),a&&(typeof a!="object"&&(a={stripHash:!1}),r=at(r,a));const e=Ze(r);if(e.parse_failed){const f=e.href.match(i);f?(e.protocols=["ssh"],e.protocol="ssh",e.resource=f[2],e.host=f[2],e.user=f[1],e.pathname=`/${f[3]}`,e.parse_failed=!1):x("URL parsing failed.")}return e};te.MAX_INPUT_LENGTH=2048;const st={class:"col col-between"},lt={__name:"OptionItems",props:{modelValue:null,items:{type:Array,default:[]}},emits:["update:modelValue"],setup(r,{emit:a}){const i=r,x=X(()=>{let u=[],y=i.items.length,g=y<=5?y:y%3===0?3:y%4===0?4:5;return i.items.forEach((t,k)=>{let m=k/g|0,U=u[m]||[];U.push(t),u[m]=U}),u}),e=X(()=>{let u=100,y=!1,g=x.value[0].length;return g>0&&(typeof i.items[0]=="object"&&(y=!0),u=100/g+"%"),{width:u,isObj:y}}),f=X(()=>i.modelValue),h=a;function z(u){h("update:modelValue",u)}return(u,y)=>{const g=_("el-radio-button"),t=_("el-radio-group");return V(),j("div",st,[(V(!0),j(C,null,M(x.value,k=>(V(),S(t,{modelValue:f.value,"onUpdate:modelValue":y[0]||(y[0]=m=>f.value=m),onChange:z,style:{width:"100%"},size:"small"},{default:w(()=>[(V(!0),j(C,null,M(k,m=>(V(),S(g,{label:m.value,style:ie(`width:${e.value.width};`)},{default:w(()=>[A(b(m.key),1)]),_:2},1032,["label","style"]))),256))]),_:2},1032,["modelValue"]))),256))])}}},Y=re(lt,[["__scopeId","data-v-5f82826e"]]),nt={class:"row",style:{height:"100%",width:"100%"}},rt={class:"row",style:{float:"left",width:"20%","align-items":"center"}},it=["src"],dt={style:{float:"right",width:"100%",color:"var(--el-text-color-secondary)","font-size":"13px","text-align":"right"}},ct={__name:"BrowserSelector",emits:["update:modelValue"],setup(r,{emit:a}){let{tr:i,ident:x,defined:e}=Z.tr();const f=a,h=R([]),z=ee(!1),u=ee(-1);function y(t=""){z.value=!0,Me(t,1,5e3).then(k=>{h.splice(0,h.length),((k.data||[]).filter(U=>!U.isTemp).filter(U=>!(U.browserId+"").startsWith("random"))||[]).forEach(U=>h.push(U)),z.value=!1})}function g(){f("update:modelValue",u)}return y(),(t,k)=>{const m=_("el-option"),U=_("el-select");return V(),S(U,{modelValue:u.value,"onUpdate:modelValue":k[0]||(k[0]=p=>u.value=p),filterable:"",remote:"",placeholder:d(x)("selectB"),"remote-show-suffix":"","remote-method":y,onChange:g,loading:z.value,size:"small"},{default:w(()=>[(V(!0),j(C,null,M(h,p=>(V(),S(m,{key:p.id,label:p.name,value:p.id},{default:w(()=>[n("div",nt,[n("div",rt,[n("img",{src:d(de)(p.uaInfo.product.name),alt:"logo",style:{width:"20px",height:"20px"}},null,8,it)]),n("div",dt,b(p.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","loading"])}}},ht={class:"col",style:{height:"100%",width:"96%",margin:"auto"}},ut={class:"row",style:{height:"6%","box-shadow":"0 8px 6px -8px #737373",position:"relative","z-index":"5","justify-content":"center"}},mt={class:"row",style:{"align-items":"center","align-content":"center",width:"100%"}},pt={class:"row",style:{"align-items":"center",height:"10%",width:"100%","margin-bottom":"1%"}},ft={style:{"font-weight":"900","font-size":"1rem","text-align":"left",width:"38%",color:"#409dfe"}},gt={class:"row",style:{width:"auto","margin-right":"4%"}},bt={class:"col",style:{height:"10%",width:"100%",margin:"3% 0 1% 0"}},wt={style:{"font-weight":"900","font-size":"1rem","text-align":"left",width:"90%",color:"#409dfe"}},vt={class:"col",style:{height:"25%",width:"100%",margin:"2% 0 1% 0"}},yt={style:{"font-weight":"900","font-size":"1rem","text-align":"left",width:"90%",color:"#409dfe"}},_t={key:0,class:"col",style:{height:"25%",width:"100%",margin:"2% 0 1% 0"}},xt={style:{"font-weight":"900","font-size":"0.9rem","text-align":"left",width:"90%",color:"#409dfe"}},zt={class:"row",style:{"align-items":"center",height:"10%",width:"100%","margin-bottom":"1%"}},Ut={style:{"font-weight":"900","font-size":"1rem","text-align":"left",width:"19%",color:"#409dfe"}},Vt={class:"row",style:{width:"80%","margin-right":"4%"}},kt={class:"col"},Tt={class:"col",style:{"vertical-align":"center","place-content":"center",width:"96%"}},$t={style:{width:"90%","font-size":"1.6rem","font-weight":"bold",color:"#363636"}},jt={class:"col",style:{margin:"0.2rem 0"}},Et={style:{"font-size":"1rem","font-weight":"bold",color:"var(--el-text-color-secondary)",margin:"auto 0"}},Lt={class:"col",style:{margin:"0.2rem 0"}},It={style:{"font-size":"1rem","font-weight":"bold",color:"var(--el-text-color-secondary)",margin:"auto 0"}},At={class:"col",style:{margin:"0.2rem 0"}},Ct={style:{"font-size":"1rem","font-weight":"bold",color:"var(--el-text-color-secondary)",margin:"auto 0"}},Mt={class:"row-between",style:{width:"100%"}},St={class:"omit",style:{"margin-right":"10px",width:"50%"}},Ht={style:{color:"var(--el-text-color-secondary)","font-size":"1rem"}},Pt={class:"col",style:{margin:"0.2rem 0"}},Bt={style:{"font-size":"1rem","font-weight":"bold",color:"var(--el-text-color-secondary)",margin:"auto 0"}},Dt={class:"row-between",style:{width:"100%"}},Rt={style:{color:"var(--el-text-color-secondary)","font-size":"1rem"}},Wt={class:"col",style:{margin:"0.2rem 0"}},qt={class:"col",style:{margin:"0.5rem 0"}},Nt={style:{width:"100%","font-size":"1rem","font-weight":"bold",color:"var(--el-text-color-secondary)",margin:"auto 0"}},Ft={class:"row-between",style:{"flex-wrap":"wrap",width:"98%",margin:"auto"}},Ot={class:"col",style:{width:"100%","margin-top":"5px"}},Jt={class:"row",style:{width:"100%","justify-content":"space-between","margin-bottom":"5px"}},Qt={class:"row",style:{width:"49%"}},Gt={style:{width:"78%","font-size":"1.6rem","font-weight":"bold",color:"#363636"}},Kt={class:"row",style:{width:"49%"}},Xt={style:{width:"78%","font-size":"1.6rem","font-weight":"bold",color:"#363636"}},Yt={class:"row",style:{width:"100%"}},Zt={class:"row",style:{width:"100%","justify-content":"space-between"}},eo={__name:"Strategy",setup(r){let{languages:a,regions:i}=Ie,{tr:x,ident:e,defined:f}=Z.tr();f({onNew:{zh:"打开新页面时",en:"On new tab"},wu:{zh:"无",en:"no-ops"},jt:{zh:"静态",en:"static"},pp:{zh:"匹配",en:"match"},sjxq:{zh:"随机选取",en:"random select"},sjsc:{zh:"随机生成",en:"random generate"},sjyc:{zh:"随机一次",en:"random once"},mlsb:{zh:"模拟设备",en:"Simulated device"},mlxt:{zh:"模拟系统",en:"Simulated os"},dl:{zh:"电脑",en:"computer"},sj:{zh:"手机",en:"mobile phone"},pb:{zh:"平板",en:"tablet PC"},data:{zh:"数据",en:"Data"},drsj:{zh:"导入数据",en:"Import data"},drcg:{zh:"导入成功",en:"Import successful"},drsb:{zh:"导入失败",en:"Import failure"},dcsj:{zh:"导出数据",en:"Export data"},dccg:{zh:"导出成功",en:"Export successful"},dcsb:{zh:"导出失败",en:"Export failure"},ppgz:{zh:"匹配规则",en:"Matching rules"},hbmd:{zh:"黑白名单",en:"Black and White List"},jtpz:{zh:"静态配置",en:"Static configuration"},pplb:{zh:"匹配列表",en:"Match List"},selectB:{zh:"选择你的浏览器",en:"Please select your browser configuration"},bds:{zh:"表达式",en:"matching expression"},llqpz:{zh:"浏览器配置",en:"browser configuration"},bmd:{zh:"白名单",en:"Whitelist"},hmd:{zh:"黑名单",en:"Blacklist"},inputB:{zh:"输入白名单域名或URL",en:"input a whitelist domain name or URL"},inputH:{zh:"输入黑名单域名或URL",en:"input a blacklist domain name or URL"},ym:{zh:"域名",en:"domain name"},aqms:{zh:"安全模式",en:"Safe mode"},webglaqms:{zh:"webgl安全模式",en:"Webgl safe mode"},aqmsSM:{zh:"安全模式启用时，随机的UA不会与真实环境具有较大的差异，避免因为模拟的环境与正式的环境过大、暴露的方面越多；同时canvas、audio和webgl等缓冲区的干扰也会从加噪音算法切换为其他更保守的算法",en:"When the safe mode is enabled,the extension will not switch the UA with large differences in version, and will only make changes on the source UA;At the same time, the interference in buffers such as canvas, audio, and webgl will switch from noise-adding algorithms to other more conservative algorithms."},webglaqmsSM:{zh:"启用时，不会完全修改webgl的参数信息",en:"When enabled,the parameter information of WebGL will not be completely modified"},jyW:{zh:"禁用worker",en:"Disable Worker"},jyWSM:{zh:"强制worker之间不允许通讯",en:"Block all types of Worker from communicating with the current page"},hszz:{zh:"函数追踪",en:"Function Trace"},hszzSM:{zh:"开启后可以使部分高级的检测手段失效（严重影响浏览器性能，慎用）",en:"Enabling this may disable some advanced detection methods (severely impacting browser performance, use with caution)"},fts:{zh:"绕过反调试",en:"Bypass anti-debugger"},ftsSM:{zh:"开启后可以禁用掉网页无限调试",en:"After enabling, unlimited debugging of web pages can be disabled"},jtllq:{zh:"静态浏览器",en:"Static browser configuration"},jtua:{zh:"静态UA",en:"Static userAgent"},jtsq:{zh:"静态时区",en:"Static timezone"},jtyy:{zh:"静态语言",en:"Static language"},gnqykz:{zh:"功能启用控制",en:"Function activation control"},inputM:{zh:"输入匹配表达式",en:"Enter matching expression"},ymError:{zh:"无法解析出域名",en:"Unable to resolve domain name"},ymInvalid:{zh:"请输入正确的域名或URL",en:"Please enter the correct domain name or URL"},bdsEmp:{zh:"表达式不能为空",en:"The expression cannot be empty"},inputUA:{zh:"请输入userAgent",en:"Please enter a userAgent"},selectLang:{zh:"请选择语言",en:"Please select a language"},selectTime:{zh:"请选择时区",en:"Please select a timezone"},fmtError:{zh:"格式错误",en:"Format error"}});const h=Ae(),z=[{statusName:"safeMode",label:"aqms",description:"aqmsSM",switch(){let s=!t.safeMode;return t.safeMode=s,T("safeMode",s),!1},enable:!0},{statusName:"webglSafeMode",label:"webglaqms",description:"webglaqmsSM",switch(){let s=!t.webglSafeMode;return t.webglSafeMode=s,T("webglSafeMode",s),!1},enable:!0},{statusName:"disableWorker",label:"jyW",description:"jyWSM",switch(){let s=!t.disableWorker;return t.disableWorker=s,T("disableWorker",s),!1},enable:!0},{statusName:"antiDebugger",label:"fts",description:"ftsSM",switch(){let s=!t.antiDebugger;return t.antiDebugger=s,T("antiDebugger",s),!1},enable:!1}],u=["wu","jt","sjxq","sjsc"].map(s=>({value:e(s,{lang:"zh"}),key:e(s)})),y=[{value:"desktop",key:e("dl")},{value:"mobile",key:e("sj")},{value:"tablet",key:e("pb")}],g=ee([]),t=R({loading:!1,option:"无",deviceType:"",os:"",tabId:"",safeMode:!1,webglSafeMode:!1,disableWorker:!1,functionTrace:!1,antiDebugger:!1,randomSeed:void 0,fixedBrowserId:"",fixedUA:"",fixedTimezone:"",fixedLanguage:"",iceServers:"",enables:(()=>{let s={};for(let o of le)s[o]=0;return s})(),hostEnable:"",disabled:{export:!1,import:!1}}),k={desktop:["windows","macos","linux"],mobile:["android","ios"],tablet:["android","ios"]};Te(()=>t.deviceType,s=>{if(!s){g.value=[];return}g.value=k[s]||[]});const m=R({page:1,size:5,total:1,matches:[],regex:"",browserId:""}),U=R({page:1,size:5,total:1,proxies:[],input:""}),p=R({writeHost:"",blackHost:"",white:[],black:[]});$e(()=>{ce()});function ce(){xe(),O(),_e(),Se().then(s=>{let{fixedBrowserId:o,fixedUA:v,fixedLanguage:$,fixedTimezone:P,enables:J,option:B,hostTable:D,hostEnable:H,deviceType:W,os:q,safeMode:Q,webglSafeMode:L,disableWorker:N,functionTrace:G,antiDebugger:K,iceServers:F,randomSeed:l}=s;t.option=B,t.deviceType=W,t.os=q,t.fixedBrowserId=o,t.fixedUA=v,t.fixedTimezone=P,t.fixedLanguage=$,t.hostEnable=H,t.safeMode=Q||!1,t.webglSafeMode=L||!1,t.disableWorker=N||!1,t.functionTrace=G||!1,t.antiDebugger=K||!1,t.randomSeed=l||0,t.enables={...t.enables,...J},t.iceServers=F?JSON.stringify(F):""})}function he(s){const o=s.target.files[0];let v=new FileReader;v.onload=async()=>{try{let $=v.result||"{}",P=JSON.parse($);await qe(P),E.success(`${e("drcg")}!`)}catch($){E.error(`${e("drsb")}!${$.message}`)}},v.onerror=$=>{E.error(e("drsb!"))},v.readAsText(o)}async function ue(){t.disabled.export=!0;try{let s=await Ne(),o=JSON.stringify(s),v=new Blob([o],{type:"application/json"}),$=URL.createObjectURL(v);Fe($,"CthulhuJs.json").then(()=>{E.success(`${e("dccg")}!`)})}catch(s){E.error(`${e("dcsb")}!${s.message}`)}finally{setTimeout(()=>{t.disabled.export=!1},500)}}function me(s){Oe(s)}function pe(s){T("deviceType",s),t.os="",T("os","")}function fe(s){T("os",s)}function ge(s){t.fixedBrowserId=s,T("fixedBrowserId",s)}function be(s){t.fixedLanguage=s,T("fixedLanguage",s)}function we(s){t.fixedTimezone=s,T("fixedTimezone",s)}function ve(s){t.fixedUA=s,T("fixedUA",s)}function ye(){h.push({name:"Home"})}function _e(){He(U.page,U.size).then(s=>{U.proxies=s.data,U.total=s.total})}function xe(){Pe(m.page,m.size).then(s=>{m.matches=s.data||[],m.matches.forEach(ze),m.total=s.total})}function O(){Be().then(s=>{p.white=[],p.black=[],Object.keys(s||{}).forEach(o=>{s[o]===1?p.white.push({host:o,index:p.white.length+1}):p.black.push({host:o,index:p.black.length+1})})})}async function ze(s){let o=await De(s.browserId);if(!o||!o.userAgent)return;let v=await Re(o.userAgent);v&&(s.logo=de((v.product||v.browser||{}).name),s.name=o.name)}function oe(s,o){try{let v=te(s);if(!v.resource){E.warning(e("ymError"));return}s=v.resource}catch{E.warning(e("ymInvalid"));return}Je(s,o).then(()=>{p.writeHost="",p.blackHost="",O()}).catch(v=>{E.error(`${e("tjsb")},`+v.message)})}function Ue(){return T("enables",t.enables).then(s=>{})}function Ve(){let s=JSON.parse(t.iceServers||"[]");if(!(s instanceof Array)||s.length&&!s.filter(o=>o.urls).length){E.error(`iceServers: ${e("fmtError")}`);return}return T("iceServers",s).then(o=>{})}function ae(s){Qe(s).then(()=>{O()})}return(s,o)=>{const v=_("el-avatar"),$=_("el-page-header"),P=_("el-icon"),J=_("el-popover"),B=_("el-switch"),D=_("el-button"),H=_("el-input"),W=_("el-option"),q=_("el-select"),Q=_("el-checkbox"),L=_("el-table-column"),N=_("el-table"),G=_("el-divider"),K=_("el-scrollbar"),F=Ee("loading");return je((V(),j("div",ht,[n("div",ut,[c($,{onBack:ye,style:{width:"100%"}},{content:w(()=>[n("div",mt,[c(v,{class:"mr-3",size:28,src:d(We),style:{"margin-right":"3px",float:"left"}},null,8,["src"])])]),_:1}),n("div",{class:"text-large font-600",style:ie(`font-weight:bold;${d(Z).curLang==="zh"?"letter-spacing: 5px;":""}width:60%;text-align: center;position: absolute;top:10%;height:100%`)},b(d(e)("setT")),5)]),c(K,{style:{width:"100%",height:"90%"},id:"scrollbar"},{default:w(()=>[(V(),j(C,null,M(z,l=>n("div",pt,[c(J,{placement:"top-start",trigger:"click",content:d(e)(l.description)},{reference:w(()=>[c(P,{style:{color:"orange","margin-right":"2px",cursor:"pointer"}},{default:w(()=>[c(d(Ce))]),_:1})]),_:2},1032,["content"]),n("div",ft,b(d(e)(l.label)),1),n("div",gt,[c(B,{modelValue:t[l.statusName],"onUpdate:modelValue":I=>t[l.statusName]=I,"active-value":!0,"inactive-value":!1,"before-change":l.switch,disabled:!l.enable},null,8,["modelValue","onUpdate:modelValue","before-change","disabled"])])])),64)),n("div",bt,[n("div",wt,b(d(e)("onNew")),1),c(Y,{modelValue:t.option,"onUpdate:modelValue":o[0]||(o[0]=l=>t.option=l),items:d(u),onChange:o[1]||(o[1]=l=>me(t.option)),style:{width:"96%"}},null,8,["modelValue","items"])]),n("div",vt,[n("div",yt,b(d(e)("mlsb")),1),c(Y,{modelValue:t.deviceType,"onUpdate:modelValue":o[2]||(o[2]=l=>t.deviceType=l),items:y,onChange:o[3]||(o[3]=l=>pe(t.deviceType)),style:{width:"96%"}},null,8,["modelValue"])]),g.value.length?(V(),j("div",_t,[n("div",xt,b(d(e)("mlxt")),1),c(Y,{modelValue:t.os,"onUpdate:modelValue":o[4]||(o[4]=l=>t.os=l),items:g.value.map(l=>({value:l,key:l})),onChange:o[5]||(o[5]=l=>fe(t.os)),style:{width:"96%","margin-top":"0.6rem"}},null,8,["modelValue","items"])])):Le("",!0),n("div",zt,[n("div",Ut,b(d(e)("data")),1),n("div",Vt,[c(D,{type:"warning",text:"",size:"small",class:"file-box",disabled:t.disabled.import,style:{width:"50%",float:"left"}},{default:w(()=>[n("input",{name:"file",accept:"application/json",type:"file",onChange:he,class:"file-btn"},null,32),A(" "+b(d(e)("drsj")),1)]),_:1},8,["disabled"]),c(D,{type:"success",text:"",onClick:ue,size:"small",disabled:t.disabled.export,style:{width:"50%",float:"right"}},{default:w(()=>[A(b(d(e)("dcsj")),1)]),_:1},8,["disabled"])])]),n("div",kt,[n("div",Tt,[n("span",$t,b(d(e)("jtpz")),1),n("div",jt,[n("span",Et,b(d(e)("jtllq")),1),c(ct,{modelValue:t.fixedBrowserId,"onUpdate:modelValue":o[6]||(o[6]=l=>t.fixedBrowserId=l),onChange:ge},null,8,["modelValue"])]),n("div",Lt,[n("span",It,b(d(e)("jtua")),1),c(H,{modelValue:t.fixedUA,"onUpdate:modelValue":o[7]||(o[7]=l=>t.fixedUA=l),placeholder:d(e)("inputUA"),clearable:"",onFocusout:o[8]||(o[8]=l=>ve(t.fixedUA)),size:"small"},null,8,["modelValue","placeholder"])]),n("div",At,[n("span",Ct,b(d(e)("jtsq")),1),c(q,{modelValue:t.fixedTimezone,"onUpdate:modelValue":o[9]||(o[9]=l=>t.fixedTimezone=l),placeholder:d(e)("selectTime"),filterable:"","allow-create":"",clearable:"",onChange:o[10]||(o[10]=l=>we(t.fixedTimezone)),size:"small"},{default:w(()=>[(V(!0),j(C,null,M(d(i),(l,I)=>(V(),S(W,{value:l.timezone,label:l.region_zh+"-"+l.city_zh+"-"+l.timezone},{default:w(()=>[n("div",Mt,[n("div",St,b(l.region_zh+"-"+l.city_zh),1),n("span",Ht,b(l.timezone),1)])]),_:2},1032,["value","label"]))),256))]),_:1},8,["modelValue","placeholder"])]),n("div",Pt,[n("span",Bt,b(d(e)("jtyy")),1),c(q,{modelValue:t.fixedLanguage,"onUpdate:modelValue":o[11]||(o[11]=l=>t.fixedLanguage=l),placeholder:d(e)("selectLang"),filterable:"","allow-create":"",clearable:"",onChange:o[12]||(o[12]=l=>be(t.fixedLanguage)),size:"small"},{default:w(()=>[(V(!0),j(C,null,M(d(a),(l,I)=>(V(),S(W,{value:l.code,label:l.name+"-"+l.code},{default:w(()=>[n("div",Dt,[n("span",null,b(l.name),1),n("span",Rt,b(l.code),1)])]),_:2},1032,["value","label"]))),256))]),_:1},8,["modelValue","placeholder"])]),n("div",Wt,[o[22]||(o[22]=n("span",{style:{"font-size":"1rem","font-weight":"bold",color:"var(--el-text-color-secondary)"}},"ICE Servers",-1)),c(H,{modelValue:t.iceServers,"onUpdate:modelValue":o[13]||(o[13]=l=>t.iceServers=l),size:"small",clearable:"",onChange:Ve},null,8,["modelValue"])]),n("div",qt,[n("div",Nt,b(d(e)("gnqykz")),1),n("div",Ft,[(V(!0),j(C,null,M(d(le),l=>(V(),S(Q,{modelValue:t.enables[l],"onUpdate:modelValue":I=>t.enables[l]=I,label:l,"true-value":1,"false-value":0,size:"small",border:"",style:{margin:`2px 0 2px\r
                           2px`,width:"8rem"},onChange:Ue,disabled:l==="revird".split("").reverse().join("")},null,8,["modelValue","onUpdate:modelValue","label","disabled"]))),256))])])]),n("div",Ot,[n("div",Jt,[n("div",Qt,[n("span",Gt,b(d(e)("bmd")),1),c(B,{modelValue:t.hostEnable,"onUpdate:modelValue":o[14]||(o[14]=l=>t.hostEnable=l),"active-value":"white","inactive-value":0,onClick:o[15]||(o[15]=l=>T("hostEnable",t.hostEnable))},null,8,["modelValue"])]),n("div",Kt,[n("span",Xt,b(d(e)("hmd")),1),c(B,{modelValue:t.hostEnable,"onUpdate:modelValue":o[16]||(o[16]=l=>t.hostEnable=l),"active-value":"black","inactive-value":0,onClick:o[17]||(o[17]=l=>T("hostEnable",t.hostEnable))},null,8,["modelValue"])])]),n("div",Yt,[c(H,{clearable:"",placeholder:d(e)("inputB"),modelValue:p.writeHost,"onUpdate:modelValue":o[18]||(o[18]=l=>p.writeHost=l),onKeydown:o[19]||(o[19]=se(l=>oe(p.writeHost,!0),["enter"])),style:{width:"47%"},size:"small"},{suffix:w(()=>o[23]||(o[23]=[A(" ↲ ")])),_:1},8,["placeholder","modelValue"]),o[25]||(o[25]=n("div",{style:{width:"3%"}},null,-1)),c(H,{clearable:"",placeholder:d(e)("inputH"),modelValue:p.blackHost,"onUpdate:modelValue":o[20]||(o[20]=l=>p.blackHost=l),onKeydown:o[21]||(o[21]=se(l=>oe(p.blackHost,!1),["enter"])),style:{width:"47%"},size:"small"},{suffix:w(()=>o[24]||(o[24]=[A(" ↲ ")])),_:1},8,["placeholder","modelValue"])]),n("div",Zt,[c(N,{data:p.white,style:{width:"49%"},size:"small",height:"200"},{default:w(()=>[c(L,{prop:"index",label:"#",width:"15"}),c(L,{prop:"host",label:d(e)("ym")},null,8,["label"]),c(L,{prop:"",label:" ",width:"45"},{default:w(l=>[c(D,{size:"small",plain:"",style:{width:"5%",height:"4%"},type:"danger",onClick:I=>ae(l.row.host,l.$index,!0)},{default:w(()=>o[26]||(o[26]=[A("－ ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),c(G,{direction:"vertical",style:{height:"90%",position:"relative",top:"10%"}}),c(N,{data:p.black,style:{width:"49%"},size:"small",height:"200","row-class-name":"blackhost"},{default:w(()=>[c(L,{prop:"index",label:"#",width:"15"}),c(L,{prop:"host",label:d(e)("ym")},null,8,["label"]),c(L,{prop:"",label:" ",width:"45"},{default:w(l=>[c(D,{size:"small",plain:"",style:{width:"5%",height:"4%"},type:"danger",onClick:I=>ae(l.row.host,l.$index,!1)},{default:w(()=>o[27]||(o[27]=[A("－ ")])),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])])])])]),_:1})])),[[F,t.loading]])}}},ao=re(eo,[["__scopeId","data-v-107384a7"]]);export{ao as default};
