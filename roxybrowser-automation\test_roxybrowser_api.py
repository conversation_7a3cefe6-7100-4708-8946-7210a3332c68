#!/usr/bin/env python3
"""
RoxyBrowser API 测试脚本
测试 RoxyBrowser API 的基本功能
"""

import sys
import time
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from config import RoxyBrowserConfig
from roxybrowser_client import RoxyBrowserClient, RoxyBrowserManager

def test_api_connection():
    """测试 API 连接"""
    print("🔍 测试 API 连接...")
    
    try:
        config = RoxyBrowserConfig()
        client = RoxyBrowserClient(config.api_host, config.api_token)
        
        if client.health_check():
            print("✅ API 连接正常")
            return True
        else:
            print("❌ API 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ API 连接异常: {str(e)}")
        return False

def test_workspace_info():
    """测试工作空间信息"""
    print("\n🏢 测试工作空间信息...")
    
    try:
        config = RoxyBrowserConfig()
        client = RoxyBrowserClient(config.api_host, config.api_token)
        
        # 获取工作空间列表
        workspaces = client.get_workspaces()
        print(f"✅ 工作空间数量: {workspaces['data']['total']}")
        
        for workspace in workspaces['data']['rows']:
            print(f"   🏢 工作空间: {workspace['workspaceName']} (ID: {workspace['id']})")
            for project in workspace['project_details']:
                print(f"      📁 项目: {project['projectName']} (ID: {project['projectId']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作空间信息获取失败: {str(e)}")
        return False

def test_browser_list():
    """测试浏览器窗口列表"""
    print("\n🪟 测试浏览器窗口列表...")
    
    try:
        config = RoxyBrowserConfig()
        client = RoxyBrowserClient(config.api_host, config.api_token)
        
        # 获取浏览器列表
        browsers = client.get_browser_list(config.workspace_id)
        print(f"✅ 浏览器窗口数量: {browsers['data']['total']}")
        
        for browser in browsers['data']['rows']:
            print(f"   🪟 窗口: {browser['windowName']} (ID: {browser['dirId']})")
            print(f"      📱 系统: {browser['os']} {browser['osVersion']}")
            print(f"      🔧 内核: {browser['coreVersion']}")
            print(f"      👤 用户: {browser['userName']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器列表获取失败: {str(e)}")
        return False

def test_create_and_delete_browser():
    """测试创建和删除浏览器窗口"""
    print("\n🔧 测试创建和删除浏览器窗口...")
    
    try:
        config = RoxyBrowserConfig()
        client = RoxyBrowserClient(config.api_host, config.api_token)
        manager = RoxyBrowserManager(client, config.workspace_id)
        
        # 获取窗口配置
        window_config = config.get_window_config()
        window_config['windowName'] = f"Test-{int(time.time())}"
        
        print(f"🔧 创建测试窗口: {window_config['windowName']}")
        
        # 创建窗口
        create_result = client.create_browser(window_config)
        dir_id = create_result['data']['dirId']
        window_id = create_result['data']['windowId']
        
        print(f"✅ 窗口创建成功 - ID: {window_id}, DirID: {dir_id}")
        
        # 等待一下
        time.sleep(2)
        
        # 删除窗口
        print(f"🗑️ 删除测试窗口: {dir_id}")
        client.delete_browser(config.workspace_id, [dir_id])
        print("✅ 窗口删除成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建/删除窗口失败: {str(e)}")
        return False

def test_full_browser_lifecycle():
    """测试完整的浏览器生命周期"""
    print("\n🚀 测试完整的浏览器生命周期...")
    
    try:
        config = RoxyBrowserConfig()
        client = RoxyBrowserClient(config.api_host, config.api_token)
        manager = RoxyBrowserManager(client, config.workspace_id)
        
        # 获取窗口配置
        window_config = config.get_window_config()
        window_config['windowName'] = f"Lifecycle-Test-{int(time.time())}"
        
        print(f"🔧 创建并打开窗口: {window_config['windowName']}")
        
        # 创建并打开窗口
        window_info = manager.create_and_open_browser(window_config)
        
        print("✅ 窗口创建并打开成功")
        print(f"   📡 WebSocket: {window_info['connection_info']['ws']}")
        print(f"   🌐 HTTP: {window_info['connection_info']['http']}")
        print(f"   🆔 PID: {window_info['connection_info']['pid']}")
        
        # 等待一下
        print("⏳ 等待 5 秒...")
        time.sleep(5)
        
        # 关闭并删除窗口
        print("🔒 关闭并删除窗口...")
        manager.close_and_delete_current_window()
        
        print("✅ 完整生命周期测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 生命周期测试失败: {str(e)}")
        
        # 尝试清理
        try:
            manager.close_and_delete_current_window()
        except:
            pass
            
        return False

def main():
    """主测试函数"""
    print("🔥 RoxyBrowser API 测试工具")
    print("=" * 50)
    
    tests = [
        ("API 连接", test_api_connection),
        ("工作空间信息", test_workspace_info),
        ("浏览器列表", test_browser_list),
        ("创建/删除窗口", test_create_and_delete_browser),
        ("完整生命周期", test_full_browser_lifecycle)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试异常: {str(e)}")
        sys.exit(1)
