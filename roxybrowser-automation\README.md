# RoxyBrowser Automation

基于 RoxyBrowser API 的自动化验证系统，用于 Augment 邮箱验证流程。

## 功能特点

- 🔥 **RoxyBrowser API 集成**: 使用 RoxyBrowser 专业反指纹浏览器
- 🛡️ **高级指纹防护**: 内置多层指纹伪装和随机化
- 🌐 **代理支持**: 支持 SOCKS5/HTTP 代理配置
- 🤖 **验证码处理**: 集成 YesCaptcha 自动验证码解决
- 📧 **临时邮箱**: 自动生成和管理临时邮箱
- 📸 **调试支持**: 自动截图和 HTML 保存
- 🧹 **资源管理**: 自动清理浏览器窗口和资源

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

在根目录的 `.env` 文件中添加以下配置：

```env
# RoxyBrowser API 配置
ROXYBROWSER_API_HOST=http://127.0.0.1:50000
ROXYBROWSER_API_TOKEN=your_roxy_api_token_here

# RoxyBrowser 窗口配置
ROXYBROWSER_WORKSPACE_ID=1
ROXYBROWSER_WINDOW_NAME=Augment-Auto
ROXYBROWSER_CORE_VERSION=125
ROXYBROWSER_OS=Windows
ROXYBROWSER_OS_VERSION=11

# 代理配置
ROXYBROWSER_PROXY=true
PROXY_URL=your_proxy_host:port
PROXY_USER=your_proxy_username
PROXY_PASS=your_proxy_password

# 验证码配置
ROXYBROWSER_RECAPTCHA_SOLVE=true
YESCAPTCHA_CLIENT_KEY=your_yescaptcha_key

# 指纹配置
ROXYBROWSER_FINGERPRINT_PROTECTION=true
ROXYBROWSER_RANDOM_FINGERPRINT=true

# 调试配置
DEBUG_MODE=true
SAVE_SCREENSHOTS=true
SAVE_HTML=true
```

## 使用方法

### 基本验证流程

```bash
python run_roxybrowser_verification.py
```

### 测试 API 连接

```python
from roxybrowser_client import RoxyBrowserClient

client = RoxyBrowserClient("http://127.0.0.1:50000", "your_token")
print("API 状态:", client.health_check())
```

## 目录结构

```
roxybrowser-automation/
├── config.py                      # 配置管理
├── roxybrowser_client.py          # RoxyBrowser API 客户端
├── roxybrowser_automation.py      # 自动化核心逻辑
├── run_roxybrowser_verification.py # 主运行脚本
├── requirements.txt               # 依赖列表
├── screenshots/                   # 截图保存目录
├── html/                         # HTML 保存目录
└── logs/                         # 日志保存目录
```

## API 功能

### RoxyBrowserClient

- `health_check()` - 健康检查
- `create_browser(config)` - 创建浏览器窗口
- `open_browser(workspace_id, dir_id)` - 打开浏览器窗口
- `close_browser(dir_id)` - 关闭浏览器窗口
- `delete_browser(workspace_id, dir_ids)` - 删除浏览器窗口
- `random_fingerprint(workspace_id, dir_id)` - 随机生成指纹

### RoxyBrowserManager

- `create_and_open_browser(config)` - 创建并打开浏览器
- `close_and_delete_current_window()` - 关闭并删除当前窗口
- `cleanup_all_windows()` - 清理所有窗口

### RoxyBrowserAutomation

- `start_browser()` - 启动浏览器
- `navigate_to_url(url)` - 导航到URL
- `input_email(email)` - 输入邮箱
- `handle_captcha(handler)` - 处理验证码
- `click_continue()` - 点击继续按钮
- `cleanup()` - 清理资源

## 注意事项

1. **RoxyBrowser 软件**: 需要先安装并启动 RoxyBrowser 软件
2. **API 配置**: 在 RoxyBrowser 中启用 API 功能并获取 Token
3. **代理配置**: 确保代理服务器可用且配置正确
4. **验证码服务**: 如需自动验证码，确保 YesCaptcha 余额充足
5. **资源清理**: 程序会自动清理浏览器窗口，避免资源泄露

## 故障排除

### API 连接失败
- 检查 RoxyBrowser 软件是否启动
- 验证 API Token 是否正确
- 确认 API 端口是否开启

### 浏览器启动失败
- 检查工作空间 ID 是否正确
- 验证代理配置是否有效
- 查看 RoxyBrowser 软件日志

### 验证码处理失败
- 检查 YesCaptcha 配置
- 验证余额是否充足
- 确认验证码类型支持

## 与其他版本对比

| 功能 | Firefox版本 | RoxyBrowser版本 |
|------|------------|----------------|
| 指纹防护 | 基础 | 专业级 |
| 代理支持 | 标准 | 高级 |
| 资源管理 | 手动 | 自动化 |
| API 集成 | 无 | 完整 |
| 成功率 | 中等 | 高 |
