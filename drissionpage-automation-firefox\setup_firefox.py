#!/usr/bin/env python3
"""
Firefox Automation Setup Script
自动安装和配置 Firefox 自动化环境
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            print(f"❌ {description} 失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description} 出错: {e}")
        return False

def check_python_version():
    """检查 Python 版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    print(f"✅ Python 版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_firefox():
    """安装 Firefox 浏览器"""
    system = platform.system().lower()
    
    if system == "linux":
        # Ubuntu/Debian
        if run_command("which firefox", "检查 Firefox"):
            print("✅ Firefox 已安装")
            return True
        
        print("🦊 安装 Firefox...")
        commands = [
            "sudo apt update",
            "sudo apt install -y firefox"
        ]
        
        for cmd in commands:
            if not run_command(cmd, f"执行: {cmd}"):
                return False
        
    elif system == "darwin":
        # macOS
        if run_command("which firefox", "检查 Firefox"):
            print("✅ Firefox 已安装")
            return True
            
        print("🦊 请手动安装 Firefox:")
        print("   方法1: brew install firefox")
        print("   方法2: 从 https://www.mozilla.org/firefox/ 下载")
        return False
        
    elif system == "windows":
        print("🦊 请手动安装 Firefox:")
        print("   从 https://www.mozilla.org/firefox/ 下载并安装")
        return False
    
    return True

def install_python_dependencies():
    """安装 Python 依赖"""
    print("📦 安装 Python 依赖...")
    
    # 升级 pip
    run_command(f"{sys.executable} -m pip install --upgrade pip", "升级 pip")
    
    # 安装依赖
    requirements = [
        "selenium>=4.15.0",
        "webdriver-manager>=4.0.0", 
        "requests>=2.31.0",
        "python-dotenv>=1.0.0",
        "pyperclip>=1.8.2",
        "PyVirtualDisplay>=3.0",
        "colorama>=0.4.6"
    ]
    
    for req in requirements:
        if not run_command(f"{sys.executable} -m pip install {req}", f"安装 {req}"):
            print(f"⚠️ {req} 安装失败，但继续...")
    
    return True

def setup_geckodriver():
    """设置 GeckoDriver"""
    print("🔧 设置 GeckoDriver...")
    
    # 尝试使用 webdriver-manager 自动管理
    test_code = """
from selenium import webdriver
from selenium.webdriver.firefox.service import Service
from webdriver_manager.firefox import GeckoDriverManager

try:
    service = Service(GeckoDriverManager().install())
    print("✅ GeckoDriver 自动安装成功")
except Exception as e:
    print(f"❌ GeckoDriver 自动安装失败: {e}")
"""
    
    try:
        exec(test_code)
        return True
    except Exception as e:
        print(f"⚠️ GeckoDriver 自动设置失败: {e}")
        print("💡 请手动下载 GeckoDriver:")
        print("   1. 访问 https://github.com/mozilla/geckodriver/releases")
        print("   2. 下载适合您系统的版本")
        print("   3. 解压并添加到 PATH")
        return False

def create_env_file():
    """创建 .env 配置文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env 文件已存在")
        return True
    
    if env_example.exists():
        print("📝 创建 .env 配置文件...")
        try:
            with open(env_example, 'r', encoding='utf-8') as f:
                content = f.read()
            
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ .env 文件创建成功")
            print("💡 请编辑 .env 文件配置您的设置")
            return True
        except Exception as e:
            print(f"❌ 创建 .env 文件失败: {e}")
            return False
    else:
        print("⚠️ .env.example 文件不存在")
        return False

def test_firefox_automation():
    """测试 Firefox 自动化"""
    print("🧪 测试 Firefox 自动化...")
    
    test_code = """
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from drissionpage_automation import FirefoxAutomation
    automation = FirefoxAutomation()
    print("✅ Firefox 自动化模块加载成功")
    
    # 简单的浏览器启动测试
    automation.init_browser()
    automation.driver.get("https://www.google.com")
    print("✅ Firefox 浏览器启动测试成功")
    automation.driver.quit()
    
except Exception as e:
    print(f"❌ Firefox 自动化测试失败: {e}")
    import traceback
    traceback.print_exc()
"""
    
    try:
        exec(test_code)
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🦊 Firefox Automation Setup")
    print("=" * 50)
    
    # 检查 Python 版本
    if not check_python_version():
        return False
    
    # 安装 Firefox
    if not install_firefox():
        print("⚠️ Firefox 安装失败，请手动安装后重试")
    
    # 安装 Python 依赖
    if not install_python_dependencies():
        print("⚠️ Python 依赖安装失败")
        return False
    
    # 设置 GeckoDriver
    if not setup_geckodriver():
        print("⚠️ GeckoDriver 设置失败，请手动配置")
    
    # 创建配置文件
    if not create_env_file():
        print("⚠️ 配置文件创建失败")
    
    # 测试自动化
    print("\n🧪 运行测试...")
    if test_firefox_automation():
        print("\n🎉 Firefox 自动化环境设置成功！")
        print("\n📋 下一步:")
        print("   1. 编辑 .env 文件配置您的设置")
        print("   2. 运行: python run_firefox_verification.py")
        print("   3. 运行完整自动化: python drissionpage_automation.py")
    else:
        print("\n⚠️ 测试失败，请检查配置")
    
    return True

if __name__ == "__main__":
    main()
