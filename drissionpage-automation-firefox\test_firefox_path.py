#!/usr/bin/env python3
"""
测试 Firefox 路径检测
"""

import os
import platform
import subprocess

def find_firefox_path():
    """
    动态查找 Firefox 浏览器路径，优先支持 Ubuntu 环境
    """
    print(f"🖥️ 操作系统: {platform.system()} {platform.release()}")
    
    # 首先尝试使用 which 命令查找 Firefox
    try:
        print("🔍 尝试使用 which 命令...")
        result = subprocess.run(['which', 'firefox'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0 and result.stdout.strip():
            firefox_path = result.stdout.strip()
            print(f"✅ which 找到: {firefox_path}")
            if os.path.exists(firefox_path) and os.access(firefox_path, os.X_OK):
                return firefox_path
    except Exception as e:
        print(f"❌ which 命令失败: {e}")
    
    # 如果 which 失败，尝试 whereis
    try:
        print("🔍 尝试使用 whereis 命令...")
        result = subprocess.run(['whereis', 'firefox'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0 and result.stdout.strip():
            print(f"📍 whereis 输出: {result.stdout.strip()}")
            # whereis 输出格式: "firefox: /usr/bin/firefox /usr/share/man/man1/firefox.1.gz"
            parts = result.stdout.strip().split()
            for part in parts[1:]:  # 跳过第一个部分（命令名）
                if part.endswith('firefox') or part.endswith('firefox-esr'):
                    print(f"🔍 检查路径: {part}")
                    if os.path.exists(part) and os.access(part, os.X_OK):
                        print(f"✅ whereis 找到: {part}")
                        return part
    except Exception as e:
        print(f"❌ whereis 命令失败: {e}")

    # Ubuntu/Linux 常见路径（按优先级排序）
    linux_paths = [
        '/usr/bin/firefox',
        '/usr/bin/firefox-esr',
        '/opt/firefox/firefox',
        '/snap/bin/firefox',
        '/usr/local/bin/firefox',
        '/opt/mozilla/firefox/firefox',
        '/usr/lib/firefox/firefox',
        '/usr/lib/firefox-esr/firefox-esr'
    ]

    print("🔍 检查常见路径...")
    for path in linux_paths:
        print(f"   检查: {path}")
        if os.path.exists(path):
            print(f"   ✅ 存在")
            if os.access(path, os.X_OK):
                print(f"   ✅ 可执行")
                return path
            else:
                print(f"   ❌ 不可执行")
        else:
            print(f"   ❌ 不存在")

    return None

def test_firefox_version(firefox_path):
    """测试 Firefox 版本"""
    try:
        print(f"🧪 测试 Firefox 版本: {firefox_path}")
        result = subprocess.run([firefox_path, '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Firefox 版本: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Firefox 版本检查失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Firefox 版本检查异常: {e}")
        return False

def main():
    print("🦊 Firefox 路径检测测试")
    print("=" * 40)
    
    firefox_path = find_firefox_path()
    
    if firefox_path:
        print(f"\n🎉 找到 Firefox: {firefox_path}")
        
        # 测试版本
        if test_firefox_version(firefox_path):
            print("✅ Firefox 路径验证成功！")
        else:
            print("❌ Firefox 路径验证失败！")
    else:
        print("\n❌ 未找到 Firefox 路径")
        print("\n💡 建议:")
        print("1. 确认 Firefox 已安装: sudo apt update && sudo apt install firefox")
        print("2. 检查 Firefox 是否在 PATH 中: echo $PATH")
        print("3. 手动查找: find /usr -name firefox 2>/dev/null")

if __name__ == '__main__':
    main()
