#!/usr/bin/env python3
"""
高级浏览器指纹伪造模块
提供全方位的浏览器指纹防护和伪造功能
"""

import random
import json
from pathlib import Path
from drissionpage_logger import DrissionPageLogger

class AdvancedFingerprintProtection:
    """高级指纹防护器"""
    
    def __init__(self, page, logger=None):
        self.page = page
        self.logger = logger or DrissionPageLogger()
        
        # 预定义的伪造数据
        self.fake_data = {
            'user_agents': [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ],
            'screen_resolutions': [
                {'width': 1920, 'height': 1080, 'colorDepth': 24, 'pixelDepth': 24},
                {'width': 1366, 'height': 768, 'colorDepth': 24, 'pixelDepth': 24},
                {'width': 1536, 'height': 864, 'colorDepth': 24, 'pixelDepth': 24},
                {'width': 1440, 'height': 900, 'colorDepth': 24, 'pixelDepth': 24},
                {'width': 2560, 'height': 1440, 'colorDepth': 24, 'pixelDepth': 24}
            ],
            'languages': [
                ['en-US', 'en'],
                ['en-GB', 'en'],
                ['en-CA', 'en', 'fr'],
                ['en-AU', 'en']
            ],
            'timezones': [
                'America/New_York',
                'America/Los_Angeles',
                'Europe/London',
                'Europe/Berlin',
                'Asia/Tokyo'
            ],
            'platforms': [
                'Win32',
                'MacIntel',
                'Linux x86_64'
            ]
        }
    
    def inject_comprehensive_protection(self):
        """注入全面的指纹防护"""
        try:
            self.logger.log('[SHIELD] 注入高级指纹防护...')
            
            # 选择随机配置
            resolution = random.choice(self.fake_data['screen_resolutions'])
            languages = random.choice(self.fake_data['languages'])
            timezone = random.choice(self.fake_data['timezones'])
            platform = random.choice(self.fake_data['platforms'])
            
            protection_script = f"""
            (function() {{
                'use strict';
                
                console.log('[SHIELD] 高级指纹防护已激活');
                
                // 1. WebDriver 属性完全清除
                Object.defineProperty(navigator, 'webdriver', {{
                    get: () => undefined,
                    configurable: true
                }});
                
                // 清除所有自动化相关变量
                const automationVars = [
                    'webdriver', '_selenium', 'callSelenium', '_Selenium_IDE_Recorder',
                    'calledSelenium', '$cdc_asdjflasutopfhvcZLmcfl_', '$chrome_asyncScriptInfo',
                    '__$webdriverAsyncExecutor', '__webdriver_script_fn', '__driver_evaluate',
                    '__webdriver_evaluate', '__selenium_evaluate', '__fxdriver_evaluate',
                    '__driver_unwrapped', '__webdriver_unwrapped', '__selenium_unwrapped',
                    '__fxdriver_unwrapped', '_Selenium_IDE_Recorder', 'calledPhantom',
                    '__nightmare', '_phantom', '__phantomas', 'callPhantom'
                ];
                
                automationVars.forEach(varName => {{
                    try {{
                        delete window[varName];
                        delete window.document[varName];
                    }} catch(e) {{}}
                }});
                
                // 2. 伪装 Chrome 对象
                if (!window.chrome) {{
                    window.chrome = {{
                        runtime: {{
                            onConnect: undefined,
                            onMessage: undefined
                        }},
                        app: {{
                            isInstalled: false,
                            InstallState: {{
                                DISABLED: 'disabled',
                                INSTALLED: 'installed',
                                NOT_INSTALLED: 'not_installed'
                            }},
                            RunningState: {{
                                CANNOT_RUN: 'cannot_run',
                                READY_TO_RUN: 'ready_to_run',
                                RUNNING: 'running'
                            }}
                        }}
                    }};
                }}
                
                // 3. 屏幕和设备信息伪造
                Object.defineProperty(screen, 'width', {{ value: {resolution['width']}, configurable: true }});
                Object.defineProperty(screen, 'height', {{ value: {resolution['height']}, configurable: true }});
                Object.defineProperty(screen, 'availWidth', {{ value: {resolution['width']}, configurable: true }});
                Object.defineProperty(screen, 'availHeight', {{ value: {resolution['height'] - 40}, configurable: true }});
                Object.defineProperty(screen, 'colorDepth', {{ value: {resolution['colorDepth']}, configurable: true }});
                Object.defineProperty(screen, 'pixelDepth', {{ value: {resolution['pixelDepth']}, configurable: true }});
                
                // 4. Navigator 属性伪造
                Object.defineProperty(navigator, 'platform', {{ value: '{platform}', configurable: true }});
                Object.defineProperty(navigator, 'language', {{ value: '{languages[0]}', configurable: true }});
                Object.defineProperty(navigator, 'languages', {{ value: {json.dumps(languages)}, configurable: true }});
                Object.defineProperty(navigator, 'hardwareConcurrency', {{ value: {random.randint(4, 16)}, configurable: true }});
                
                // 5. 设备内存伪造
                if (navigator.deviceMemory) {{
                    Object.defineProperty(navigator, 'deviceMemory', {{ value: {random.choice([4, 8, 16])}, configurable: true }});
                }}
                
                // 6. 插件伪造
                const fakePlugins = [
                    {{ name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' }},
                    {{ name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '' }},
                    {{ name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }}
                ];
                
                Object.defineProperty(navigator, 'plugins', {{
                    value: fakePlugins,
                    configurable: true
                }});
                
                // 7. Canvas 指纹随机化
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                HTMLCanvasElement.prototype.toDataURL = function(...args) {{
                    const context = this.getContext('2d');
                    if (context) {{
                        // 添加随机噪声
                        const imageData = context.getImageData(0, 0, this.width, this.height);
                        for (let i = 0; i < imageData.data.length; i += 4) {{
                            if (Math.random() < 0.01) {{
                                imageData.data[i] = Math.min(255, imageData.data[i] + Math.random() * 4 - 2);
                                imageData.data[i + 1] = Math.min(255, imageData.data[i + 1] + Math.random() * 4 - 2);
                                imageData.data[i + 2] = Math.min(255, imageData.data[i + 2] + Math.random() * 4 - 2);
                            }}
                        }}
                        context.putImageData(imageData, 0, 0);
                    }}
                    return originalToDataURL.apply(this, args);
                }};
                
                // 8. WebGL 指纹伪造
                const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {{
                    switch (parameter) {{
                        case this.VENDOR:
                            return 'Google Inc. (Intel)';
                        case this.RENDERER:
                            return 'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)';
                        case this.VERSION:
                            return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
                        case this.SHADING_LANGUAGE_VERSION:
                            return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
                        default:
                            return originalGetParameter.call(this, parameter);
                    }}
                }};
                
                // 9. 音频指纹随机化
                if (window.AudioContext || window.webkitAudioContext) {{
                    const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
                    function FakeAudioContext(...args) {{
                        const context = new OriginalAudioContext(...args);
                        const originalCreateAnalyser = context.createAnalyser;
                        context.createAnalyser = function() {{
                            const analyser = originalCreateAnalyser.call(this);
                            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                            analyser.getFloatFrequencyData = function(array) {{
                                originalGetFloatFrequencyData.call(this, array);
                                for (let i = 0; i < array.length; i++) {{
                                    array[i] += Math.random() * 0.02 - 0.01;
                                }}
                            }};
                            return analyser;
                        }};
                        return context;
                    }}
                    FakeAudioContext.prototype = OriginalAudioContext.prototype;
                    window.AudioContext = FakeAudioContext;
                    if (window.webkitAudioContext) {{
                        window.webkitAudioContext = FakeAudioContext;
                    }}
                }}
                
                // 10. 时区伪造
                const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
                Date.prototype.getTimezoneOffset = function() {{
                    const timezoneOffsets = {{
                        'America/New_York': 300,
                        'America/Los_Angeles': 480,
                        'Europe/London': 0,
                        'Europe/Berlin': -60,
                        'Asia/Tokyo': -540
                    }};
                    return timezoneOffsets['{timezone}'] || originalGetTimezoneOffset.call(this);
                }};
                
                // 11. 字体指纹随机化
                const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
                const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');
                
                Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {{
                    get: function() {{
                        const width = originalOffsetWidth.get.call(this);
                        return width + (Math.random() * 0.2 - 0.1);
                    }}
                }});
                
                Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {{
                    get: function() {{
                        const height = originalOffsetHeight.get.call(this);
                        return height + (Math.random() * 0.2 - 0.1);
                    }}
                }});
                
                // 12. 鼠标和触摸事件随机化
                const originalAddEventListener = EventTarget.prototype.addEventListener;
                EventTarget.prototype.addEventListener = function(type, listener, options) {{
                    if (type === 'mousemove' || type === 'touchmove') {{
                        const wrappedListener = function(event) {{
                            // 添加微小的随机偏移
                            if (event.clientX !== undefined) {{
                                Object.defineProperty(event, 'clientX', {{
                                    value: event.clientX + (Math.random() * 2 - 1),
                                    configurable: true
                                }});
                            }}
                            if (event.clientY !== undefined) {{
                                Object.defineProperty(event, 'clientY', {{
                                    value: event.clientY + (Math.random() * 2 - 1),
                                    configurable: true
                                }});
                            }}
                            return listener.call(this, event);
                        }};
                        return originalAddEventListener.call(this, type, wrappedListener, options);
                    }}
                    return originalAddEventListener.call(this, type, listener, options);
                }};
                
                // 13. 性能指纹随机化
                if (window.performance && window.performance.memory) {{
                    Object.defineProperty(window.performance.memory, 'usedJSHeapSize', {{
                        get: () => Math.floor(Math.random() * 50000000) + 10000000,
                        configurable: true
                    }});
                    Object.defineProperty(window.performance.memory, 'totalJSHeapSize', {{
                        get: () => Math.floor(Math.random() * 100000000) + 50000000,
                        configurable: true
                    }});
                }}
                
                console.log('[OK] 高级指纹防护注入完成');
            }})();
            """
            
            self.page.run_js(protection_script)
            self.logger.log('[OK] 高级指纹防护注入成功')
            return True
            
        except Exception as e:
            self.logger.log(f'[ERROR] 高级指纹防护注入失败: {e}')
            return False
    
    def test_fingerprint_protection(self):
        """测试指纹防护效果"""
        try:
            self.logger.log('🧪 测试指纹防护效果...')
            
            test_results = self.page.run_js("""
                const results = {
                    webdriver: navigator.webdriver,
                    platform: navigator.platform,
                    language: navigator.language,
                    languages: navigator.languages,
                    hardwareConcurrency: navigator.hardwareConcurrency,
                    deviceMemory: navigator.deviceMemory,
                    screen: {
                        width: screen.width,
                        height: screen.height,
                        colorDepth: screen.colorDepth
                    },
                    plugins: navigator.plugins.length,
                    chrome: !!window.chrome,
                    timezoneOffset: new Date().getTimezoneOffset()
                };
                
                // Canvas 指纹测试
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Fingerprint Test [SHIELD]', 2, 2);
                results.canvasFingerprint = canvas.toDataURL().slice(0, 50) + '...';
                
                return results;
            """)
            
            self.logger.log(f'[SEARCH] 指纹测试结果:')
            self.logger.log(f'   WebDriver: {test_results.get("webdriver")}')
            self.logger.log(f'   平台: {test_results.get("platform")}')
            self.logger.log(f'   语言: {test_results.get("language")}')
            self.logger.log(f'   屏幕: {test_results.get("screen", {}).get("width")}x{test_results.get("screen", {}).get("height")}')
            self.logger.log(f'   硬件并发: {test_results.get("hardwareConcurrency")}')
            self.logger.log(f'   Chrome 对象: {test_results.get("chrome")}')
            
            return test_results
            
        except Exception as e:
            self.logger.log(f'[ERROR] 指纹测试失败: {e}')
            return None
