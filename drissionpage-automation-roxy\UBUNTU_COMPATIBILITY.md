# Ubuntu 兼容性指南

## 概述
本文档详细说明了 `drissionpage-automation-enchanced` 在 Ubuntu 环境下的兼容性改进和使用方法。

## 主要改进

### 1. 动态 Chrome 路径检测
系统会自动检测以下 Chrome 安装路径（按优先级排序）：

```bash
/usr/bin/google-chrome              # 标准 APT 安装
/opt/google/chrome/google-chrome    # 手动下载安装
/usr/bin/google-chrome-stable       # 稳定版
/usr/bin/chromium-browser           # Chromium 浏览器
/snap/bin/chromium                  # Snap 包安装
/usr/bin/chromium                   # 其他 Chromium 安装
```

### 2. 智能端口管理
- **自动检测**: 从端口 9222 开始检测可用端口
- **冲突解决**: 如果默认端口被占用，自动分配新端口
- **同步配置**: 确保 `--remote-debugging-port` 和 `set_local_port()` 一致

### 3. 解决的问题
- ✅ Chrome 路径自动检测
- ✅ 端口冲突自动解决
- ✅ DrissionPage 连接稳定性
- ✅ 跨平台兼容性

## Ubuntu 安装 Chrome

### 方法 1: APT 安装（推荐）
```bash
# 更新包列表
sudo apt update

# 安装 Google Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
sudo apt update
sudo apt install google-chrome-stable
```

### 方法 2: Snap 安装
```bash
sudo snap install chromium
```

### 方法 3: 手动下载
```bash
# 下载 .deb 包
wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb

# 安装
sudo dpkg -i google-chrome-stable_current_amd64.deb
sudo apt-get install -f  # 修复依赖
```

## SSH X11 转发设置

如果通过 SSH 连接到 Ubuntu 服务器，需要启用 X11 转发来显示浏览器：

### 客户端设置
```bash
# 启用 X11 转发连接
ssh -X username@hostname

# 或者启用受信任的 X11 转发
ssh -Y username@hostname
```

### 服务器设置
编辑 `/etc/ssh/sshd_config`：
```bash
X11Forwarding yes
X11DisplayOffset 10
X11UseLocalhost yes
```

重启 SSH 服务：
```bash
sudo systemctl restart sshd
```

### 验证 X11 转发
```bash
# 检查 DISPLAY 环境变量
echo $DISPLAY

# 测试 X11 应用
xeyes  # 如果显示眼睛窗口，说明 X11 转发正常
```

## 依赖包安装

### 必需包
```bash
sudo apt update
sudo apt install -y \
    python3 \
    python3-pip \
    xvfb \
    xauth \
    x11-utils \
    libxss1 \
    libgconf-2-4 \
    libxrandr2 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0
```

### Python 依赖
```bash
pip3 install -r requirements.txt
```

## 使用方法

### 1. 基本使用
```python
from drissionpage_automation import DrissionPageAutomation

# 创建实例
automation = DrissionPageAutomation()

# 初始化浏览器（会自动检测 Chrome 路径和端口）
automation.init_browser()

# 使用浏览器
automation.page.get('https://www.google.com')
```

### 2. 手动指定 Chrome 路径（可选）
```python
from DrissionPage import ChromiumOptions
from drissionpage_automation import find_chrome_path

# 检测 Chrome 路径
chrome_path = find_chrome_path()
print(f"检测到 Chrome: {chrome_path}")

# 手动设置（如果自动检测失败）
options = ChromiumOptions()
options.set_paths(browser_path='/usr/bin/google-chrome')
```

### 3. 端口配置
```python
from drissionpage_automation import find_available_port

# 查找可用端口
port = find_available_port()
print(f"可用端口: {port}")
```

## 测试工具

### 1. 基本配置测试
```bash
cd drissionpage-automation-enchanced
python test_enhanced_config.py
```

### 2. Ubuntu 兼容性测试
```bash
python test_ubuntu_compatibility.py
```

## 故障排除

### 问题 1: Chrome 未找到
**症状**: 提示 "未找到 Chrome 路径"
**解决**: 
1. 安装 Chrome（见上述安装方法）
2. 检查安装路径：`which google-chrome`
3. 确保可执行权限：`chmod +x /path/to/chrome`

### 问题 2: 端口冲突
**症状**: "端口被占用" 或连接失败
**解决**: 
1. 系统会自动分配新端口
2. 手动检查端口：`netstat -tlnp | grep 9222`
3. 杀死占用进程：`sudo kill -9 <PID>`

### 问题 3: X11 显示问题
**症状**: 浏览器无法显示或 "cannot open display"
**解决**:
1. 检查 DISPLAY：`echo $DISPLAY`
2. 启用 X11 转发：`ssh -X`
3. 安装 X11 包：`sudo apt install xauth x11-utils`

### 问题 4: 权限问题
**症状**: Chrome 启动失败或权限错误
**解决**:
1. 添加用户到 video 组：`sudo usermod -a -G video $USER`
2. 重新登录或：`newgrp video`
3. 检查 /tmp 权限：`ls -la /tmp`

## 性能优化

### 1. 无头模式（服务器环境）
如果不需要显示浏览器，可以使用无头模式：
```bash
# 在 .env 文件中设置
DRISSIONPAGE_HEADFULL=false
```

### 2. 虚拟显示（Xvfb）
在没有物理显示器的服务器上：
```bash
# 启动虚拟显示
Xvfb :99 -screen 0 1920x1080x24 &
export DISPLAY=:99

# 运行程序
python drissionpage_automation.py
```

### 3. 资源限制
```python
# 在 Chrome 参数中添加资源限制
options.set_argument('--memory-pressure-off')
options.set_argument('--max_old_space_size=4096')
```

## 最佳实践

1. **总是使用最新版本的 Chrome**
2. **在生产环境中使用无头模式**
3. **定期清理临时文件和用户数据目录**
4. **监控端口使用情况**
5. **使用虚拟环境隔离 Python 依赖**

## 支持的 Ubuntu 版本

- Ubuntu 18.04 LTS
- Ubuntu 20.04 LTS  
- Ubuntu 22.04 LTS
- Ubuntu 24.04 LTS

## 更新日期
2025-01-18
