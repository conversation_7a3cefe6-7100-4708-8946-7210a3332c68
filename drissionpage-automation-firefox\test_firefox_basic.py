#!/usr/bin/env python3
"""
Firefox Basic Test
测试 Firefox 基本功能，不涉及复杂的自动化流程
"""

import sys
import time
from pathlib import Path

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import FirefoxAutomation

def test_firefox_basic():
    """测试 Firefox 基本功能"""
    print('🦊 Firefox 基本功能测试')
    print('=' * 40)
    
    automation = None
    
    try:
        # 初始化自动化
        print('🔧 初始化 Firefox 自动化...')
        automation = FirefoxAutomation()
        
        # 初始化浏览器
        print('🚀 启动 Firefox 浏览器...')
        automation.init_browser()
        
        # 测试1: 访问简单页面
        print('🌐 测试1: 访问 Google...')
        automation.driver.get('https://www.google.com')
        time.sleep(3)
        
        title = automation.driver.title
        print(f'页面标题: {title}')
        
        # 测试2: 访问 Augment 授权页面
        print('🔗 测试2: 访问 Augment 授权页面...')
        
        # 生成一个简单的授权 URL
        from handlers import AugmentAuth
        augment_auth = AugmentAuth()
        auth_url = augment_auth.generate_auth_url()
        
        print(f'授权 URL: {auth_url}')
        
        automation.driver.get(auth_url)
        time.sleep(5)
        
        current_url = automation.driver.current_url
        print(f'当前 URL: {current_url}')
        
        # 测试3: 查找邮箱输入框
        print('📧 测试3: 查找邮箱输入框...')
        
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        try:
            # 尝试找到邮箱输入框
            email_input = WebDriverWait(automation.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="username"], input[id="username"], input[type="email"]'))
            )
            
            if email_input:
                print('✅ 找到邮箱输入框')
                
                # 测试输入
                test_email = "<EMAIL>"
                email_input.clear()
                email_input.send_keys(test_email)
                
                # 验证输入
                value = email_input.get_attribute('value')
                print(f'输入的邮箱: {value}')
                
                if value == test_email:
                    print('✅ 邮箱输入测试成功')
                else:
                    print(f'⚠️ 邮箱输入不匹配: 期望 {test_email}, 实际 {value}')
            else:
                print('❌ 未找到邮箱输入框')
                
        except Exception as e:
            print(f'⚠️ 邮箱输入框测试失败: {e}')
        
        # 测试4: JavaScript 执行
        print('🔧 测试4: JavaScript 执行...')
        
        try:
            result = automation.driver.execute_script("return document.readyState")
            print(f'页面状态: {result}')
            
            # 测试获取页面信息
            page_info = automation.driver.execute_script("""
                return {
                    title: document.title,
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                    readyState: document.readyState
                };
            """)
            
            print('页面信息:')
            for key, value in page_info.items():
                print(f'  {key}: {value}')
                
            print('✅ JavaScript 执行测试成功')
            
        except Exception as e:
            print(f'❌ JavaScript 执行测试失败: {e}')
        
        print('\n🎉 Firefox 基本功能测试完成！')
        print('🔍 请检查浏览器窗口确认一切正常')
        
        # 保持浏览器打开
        input('\n按 Enter 键关闭浏览器...')
        
    except KeyboardInterrupt:
        print('\n⚠️ 用户中断操作')
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        if automation and automation.driver:
            try:
                print('🧹 关闭浏览器...')
                automation.driver.quit()
            except Exception as e:
                print(f'⚠️ 关闭浏览器时出错: {e}')
        
        print('👋 测试结束')

if __name__ == '__main__':
    test_firefox_basic()
