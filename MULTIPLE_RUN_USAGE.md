# 多次运行验证脚本使用说明

## 📋 概述

这些脚本用于在根目录运行多次 `drissionpage-automation-roxy/run_drissionpage_verification.py` 验证脚本。

## 🚀 使用方法

### Python 版本 (推荐)

```bash
# 运行 1 次 (默认)
python run_multiple_verification.py

# 运行 5 次
python run_multiple_verification.py 5

# 运行 10 次
python run_multiple_verification.py 10
```

### Windows 批处理版本

```cmd
# 运行 1 次 (默认)
run_multiple_verification.bat

# 运行 5 次
run_multiple_verification.bat 5

# 运行 10 次
run_multiple_verification.bat 10
```

## 📊 功能特性

### ✅ 自动化功能
- **自动运行**: 按指定次数顺序运行验证脚本
- **间隔等待**: 每次运行之间等待 3 秒，避免冲突
- **超时保护**: Python版本有 5 分钟超时保护
- **错误处理**: 捕获并记录运行错误

### 📈 统计报告
- **实时日志**: 显示每次运行的状态和时间戳
- **成功率统计**: 计算总体成功率
- **耗时统计**: 显示总运行时间
- **详细总结**: 运行结束后显示完整统计信息

### 🛡️ 安全特性
- **参数验证**: 验证运行次数参数的有效性
- **路径检查**: 检查脚本文件和目录是否存在
- **中断处理**: 支持 Ctrl+C 优雅中断

## 📝 输出示例

```
[2025-01-19 15:30:00] 📋 准备运行 DrissionPage 验证脚本 3 次
[2025-01-19 15:30:00] 🚀 开始运行第 1/3 次验证...
[2025-01-19 15:30:45] ✅ 第 1/3 次运行成功
[2025-01-19 15:30:45] ⏳ 等待 3 秒后继续下一次运行...
[2025-01-19 15:30:48] 🚀 开始运行第 2/3 次验证...
[2025-01-19 15:31:30] ✅ 第 2/3 次运行成功
[2025-01-19 15:31:30] ⏳ 等待 3 秒后继续下一次运行...
[2025-01-19 15:31:33] 🚀 开始运行第 3/3 次验证...
[2025-01-19 15:32:15] ✅ 第 3/3 次运行成功
==================================================
[2025-01-19 15:32:15] 📊 运行总结:
   总运行次数: 3
   成功次数: 3
   失败次数: 0
   成功率: 100.0%
   总耗时: 2分15秒
==================================================
[2025-01-19 15:32:15] 🎉 所有运行都成功完成！
```

## 🔧 退出码说明

- **0**: 所有运行都成功
- **1**: 部分运行成功，部分失败
- **2**: 所有运行都失败
- **130**: 用户中断 (Ctrl+C)

## ⚠️ 注意事项

1. **脚本路径**: 确保 `drissionpage-automation-roxy/run_drissionpage_verification.py` 文件存在
2. **Python 环境**: 确保 Python 环境正确配置
3. **运行权限**: 确保有足够的权限运行脚本
4. **网络连接**: 验证脚本可能需要网络连接
5. **资源占用**: 多次运行可能消耗较多系统资源

## 🛠️ 故障排除

### 常见问题

**Q: 提示脚本文件不存在**
A: 检查 `drissionpage-automation-roxy` 目录和脚本文件是否存在

**Q: Python 命令不识别**
A: 确保 Python 已正确安装并添加到 PATH 环境变量

**Q: 运行超时**
A: 检查网络连接和目标服务是否正常

**Q: 权限错误**
A: 以管理员身份运行或检查文件权限

## 📞 技术支持

如遇到问题，请检查：
1. Python 环境是否正确配置
2. 脚本文件路径是否正确
3. 网络连接是否正常
4. 系统资源是否充足
