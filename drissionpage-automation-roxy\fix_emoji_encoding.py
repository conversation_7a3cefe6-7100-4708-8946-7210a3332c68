#!/usr/bin/env python3
"""
修复 emoji 编码问题
"""

import re
import os

def fix_emoji_in_file(file_path):
    """修复文件中的 emoji 字符"""
    print(f"修复文件: {file_path}")
    
    # emoji 替换映射
    emoji_replacements = {
        '[BOT]': '[BOT]',
        '[START]': '[START]',
        '[SHIELD]': '[SHIELD]',
        '[SCREEN]': '[SCREEN]',
        '[WARN]': '[WARN]',
        '[TIP]': '[TIP]',
        '[TARGET]': '[TARGET]',
        '[CAPTURE]': '[CAPTURE]',
        '[WEB]': '[WEB]',
        '[ERROR]': '[ERROR]',
        '[OK]': '[OK]',
        '[CONFIG]': '[CONFIG]',
        '[COPY]': '[COPY]',
        '[SEARCH]': '[SEARCH]',
        '[CONNECT]': '[CONNECT]',
        '[FOLDER]': '[FOLDER]',
        '[GHOST]': '[GHOST]',
        '[PLUGIN]': '[PLUGIN]',
        '[KEY]': '[KEY]',
        '[REFRESH]': '[REFRESH]',
        '[PAGE]': '[PAGE]',
        '[EMAIL]': '[EMAIL]',
        '[CODE]': '[CODE]',
        '[THINK]': '[THINK]',
        '[FIND]': '[FIND]',
        '[EYE]': '[EYE]',
        '[MOUSE]': '[MOUSE]',
        '[LINK]': '[LINK]',
        '[DELETE]': '[DELETE]',
        '[CLEAN]': '[CLEAN]',
        '[ALERT]': '[ALERT]',
        '[FIRE]': '[FIRE]',
        '[SUCCESS]': '[SUCCESS]',
        '[LOCK]': '[LOCK]',
        '[ART]': '[ART]',
        '[OCTOPUS]': '[OCTOPUS]',
        '[STOP]': '[STOP]',
        '[WAIT]': '[WAIT]',
        '[TIMER]': '[TIMER]',
        '[PAUSE]': '[PAUSE]',
        '[MASK]': '[MASK]',
        '[SOUND]': '[SOUND]',
        '[COMPUTER]': '[COMPUTER]',
        '[SAVE]': '[SAVE]',
        '[WORLD]': '[WORLD]',
        '[GREEN]': '[GREEN]',
        '[YELLOW]': '[YELLOW]',
        '[RED]': '[RED]',
        '[STAR]': '[STAR]',
        '[STRONG]': '[STRONG]',
        '[SPARKLE]': '[SPARKLE]',
        '[CIRCUS]': '[CIRCUS]',
        '[MOVIE]': '[MOVIE]',
        '[GAME]': '[GAME]',
        '[DICE]': '[DICE]',
        '[MUSIC_NOTE]': '[MUSIC]',
        '[MUSICAL_NOTES]': '[NOTE]',
        '[GUITAR2]': '[GUITAR]',
        '[KEYBOARD]': '[PIANO]',
        '[TRUMPET2]': '[TRUMPET]',
        '[VIOLIN2]': '[VIOLIN]',
        '[DRUMS]': '[DRUM]',
        '[MICROPHONE2]': '[MIC]',
        '[HEADPHONES]': '[HEADPHONE]',
        '[PHONE]': '[PHONE]',
        '[CALL]': '[CALL]',
        '[PAGER]': '[PAGER]',
        '[FAX]': '[FAX]',
        '[TV]': '[TV]',
        '[RADIO2]': '[RADIO]',
        '[SPEAKER]': '[SPEAKER]',
        '[VOLUME]': '[VOLUME]',
        '[MUTE]': '[MUTE]',
        '[ANNOUNCE]': '[ANNOUNCE]',
        '[MEGAPHONE]': '[MEGAPHONE]',
        '[HORN]': '[HORN]',
        '[BELL]': '[BELL]',
        '[NO_BELL]': '[NO_BELL]',
        '[SCORE]': '[SCORE]',
        '[MUSIC_NOTE]': '[MUSIC_NOTE]',
        '[MUSICAL_NOTES]': '[MUSICAL_NOTES]',
        '[MICROPHONE]': '[MICROPHONE]',
        '[LEVEL_SLIDER]': '[LEVEL_SLIDER]',
        '[CONTROL_KNOBS]': '[CONTROL_KNOBS]',
        '[MICROPHONE2]': '[MICROPHONE2]',
        '[HEADPHONES]': '[HEADPHONES]',
        '[RADIO2]': '[RADIO2]',
        '[SAX]': '[SAX]',
        '[TRUMPET2]': '[TRUMPET2]',
        '[GUITAR2]': '[GUITAR2]',
        '[KEYBOARD]': '[KEYBOARD]',
        '[DRUMS]': '[DRUMS]',
        '[VIOLIN2]': '[VIOLIN2]',
        '[LOCK]': '[LOCK]',
        '[BOOM]': '[BOOM]',
        '[OFFICE]': '[OFFICE]',
        '[WINDOW]': '[WINDOW]',
        '[CHART]': '[CHART]'
    }
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换 emoji
        modified = False
        for emoji, replacement in emoji_replacements.items():
            if emoji in content:
                content = content.replace(emoji, replacement)
                modified = True
                print(f"  替换: {emoji} -> {replacement}")
        
        # 如果有修改，写回文件
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  文件已更新")
        else:
            print(f"  无需修改")
            
        return modified
        
    except Exception as e:
        print(f"  错误: {e}")
        return False

def scan_and_fix_all_files():
    """扫描并修复所有Python文件中的emoji"""
    print("扫描所有Python文件...")

    # 获取当前目录下所有Python文件
    python_files = []
    for file in os.listdir('.'):
        if file.endswith('.py'):
            python_files.append(file)

    print(f"找到 {len(python_files)} 个Python文件")

    total_modified = 0

    for file_name in python_files:
        if fix_emoji_in_file(file_name):
            total_modified += 1

    return total_modified

def main():
    """主函数"""
    print("开始修复 emoji 编码问题...")

    # 扫描并修复所有Python文件
    total_modified = scan_and_fix_all_files()

    print(f"\n修复完成! 共修改了 {total_modified} 个文件")
    print("现在可以重新运行程序了")

if __name__ == '__main__':
    main()
