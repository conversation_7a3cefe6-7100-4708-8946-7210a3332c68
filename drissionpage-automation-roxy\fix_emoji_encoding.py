#!/usr/bin/env python3
"""
修复 emoji 编码问题
"""

import re
import os

def fix_emoji_in_file(file_path):
    """修复文件中的 emoji 字符"""
    print(f"修复文件: {file_path}")
    
    # emoji 替换映射
    emoji_replacements = {
        '🤖': '[BOT]',
        '🚀': '[START]',
        '🛡️': '[SHIELD]',
        '🖥️': '[SCREEN]',
        '⚠️': '[WARN]',
        '💡': '[TIP]',
        '🎯': '[TARGET]',
        '📸': '[CAPTURE]',
        '🌐': '[WEB]',
        '❌': '[ERROR]',
        '✅': '[OK]',
        '🔧': '[CONFIG]',
        '📋': '[COPY]',
        '🔍': '[SEARCH]',
        '🔌': '[CONNECT]',
        '📁': '[FOLDER]',
        '👻': '[GHOST]',
        '🧩': '[PLUGIN]',
        '🔑': '[KEY]',
        '🔄': '[REFRESH]',
        '📄': '[PAGE]',
        '📧': '[EMAIL]',
        '🔢': '[CODE]',
        '🤔': '[THINK]',
        '🔎': '[FIND]',
        '👁️': '[EYE]',
        '🖱️': '[MOUSE]',
        '🔗': '[LINK]',
        '🗑️': '[DELETE]',
        '🧹': '[CLEAN]',
        '🚨': '[ALERT]',
        '🔥': '[FIRE]',
        '🎉': '[SUCCESS]',
        '🔒': '[LOCK]',
        '🎨': '[ART]',
        '🐙': '[OCTOPUS]',
        '🛑': '[STOP]',
        '⏳': '[WAIT]',
        '⏱️': '[TIMER]',
        '⏸️': '[PAUSE]',
        '🎭': '[MASK]',
        '🔊': '[SOUND]',
        '💻': '[COMPUTER]',
        '💾': '[SAVE]',
        '🌍': '[WORLD]',
        '🟢': '[GREEN]',
        '🟡': '[YELLOW]',
        '🔴': '[RED]',
        '⭐': '[STAR]',
        '💪': '[STRONG]',
        '✨': '[SPARKLE]',
        '🎪': '[CIRCUS]',
        '🎬': '[MOVIE]',
        '🎮': '[GAME]',
        '🎲': '[DICE]',
        '🎵': '[MUSIC]',
        '🎶': '[NOTE]',
        '🎸': '[GUITAR]',
        '🎹': '[PIANO]',
        '🎺': '[TRUMPET]',
        '🎻': '[VIOLIN]',
        '🥁': '[DRUM]',
        '🎤': '[MIC]',
        '🎧': '[HEADPHONE]',
        '📱': '[PHONE]',
        '📞': '[CALL]',
        '📟': '[PAGER]',
        '📠': '[FAX]',
        '📺': '[TV]',
        '📻': '[RADIO]',
        '🔈': '[SPEAKER]',
        '🔉': '[VOLUME]',
        '🔇': '[MUTE]',
        '📢': '[ANNOUNCE]',
        '📣': '[MEGAPHONE]',
        '📯': '[HORN]',
        '🔔': '[BELL]',
        '🔕': '[NO_BELL]',
        '🎼': '[SCORE]',
        '🎵': '[MUSIC_NOTE]',
        '🎶': '[MUSICAL_NOTES]',
        '🎙️': '[MICROPHONE]',
        '🎚️': '[LEVEL_SLIDER]',
        '🎛️': '[CONTROL_KNOBS]',
        '🎤': '[MICROPHONE2]',
        '🎧': '[HEADPHONES]',
        '📻': '[RADIO2]',
        '🎷': '[SAX]',
        '🎺': '[TRUMPET2]',
        '🎸': '[GUITAR2]',
        '🎹': '[KEYBOARD]',
        '🥁': '[DRUMS]',
        '🎻': '[VIOLIN2]'
    }
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换 emoji
        modified = False
        for emoji, replacement in emoji_replacements.items():
            if emoji in content:
                content = content.replace(emoji, replacement)
                modified = True
                print(f"  替换: {emoji} -> {replacement}")
        
        # 如果有修改，写回文件
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  文件已更新")
        else:
            print(f"  无需修改")
            
        return modified
        
    except Exception as e:
        print(f"  错误: {e}")
        return False

def main():
    """主函数"""
    print("开始修复 emoji 编码问题...")
    
    # 需要修复的文件列表
    files_to_fix = [
        'drissionpage_automation.py',
        'drissionpage_logger.py',
        'run_drissionpage_verification.py'
    ]
    
    total_modified = 0
    
    for file_name in files_to_fix:
        if os.path.exists(file_name):
            if fix_emoji_in_file(file_name):
                total_modified += 1
        else:
            print(f"文件不存在: {file_name}")
    
    print(f"\n修复完成! 共修改了 {total_modified} 个文件")
    print("现在可以重新运行程序了")

if __name__ == '__main__':
    main()
