#!/usr/bin/env python3
"""
CthulhuJS Anti-Fingerprint 插件演示脚本
展示如何使用集成的 CthulhuJS 插件进行反指纹保护
"""

import time
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import DrissionPageAutomation
from drissionpage_logger import DrissionPageLogger

def demo_fingerprint_protection():
    """演示指纹防护功能"""
    logger = DrissionPageLogger()
    logger.log('🎭 CthulhuJS Anti-Fingerprint 插件演示')
    logger.log('=' * 60)
    
    try:
        # 创建自动化实例
        automation = DrissionPageAutomation()
        
        # 初始化浏览器（会自动加载 CthulhuJS 插件）
        logger.log('🚀 启动浏览器并加载 CthulhuJS 插件...')
        automation.init_browser()
        
        # 等待插件加载
        time.sleep(2)
        
        # 创建测试页面
        test_html = '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>CthulhuJS Anti-Fingerprint Demo</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .result { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 3px; }
                .protected { color: #28a745; font-weight: bold; }
                .warning { color: #dc3545; font-weight: bold; }
            </style>
        </head>
        <body>
            <h1>🐙 CthulhuJS Anti-Fingerprint 演示</h1>
            
            <div class="test-section">
                <h2>🖥️ 屏幕信息</h2>
                <div class="result">
                    屏幕尺寸: <span id="screen-size"></span><br>
                    可用尺寸: <span id="screen-avail"></span><br>
                    颜色深度: <span id="screen-color"></span>
                </div>
            </div>
            
            <div class="test-section">
                <h2>🌍 语言和地区</h2>
                <div class="result">
                    主语言: <span id="language"></span><br>
                    语言列表: <span id="languages"></span><br>
                    时区偏移: <span id="timezone"></span>
                </div>
            </div>
            
            <div class="test-section">
                <h2>💻 硬件信息</h2>
                <div class="result">
                    CPU 核心数: <span id="cpu-cores"></span><br>
                    设备内存: <span id="device-memory"></span><br>
                    平台信息: <span id="platform"></span>
                </div>
            </div>
            
            <div class="test-section">
                <h2>🎨 Canvas 指纹</h2>
                <canvas id="test-canvas" width="200" height="50" style="border: 1px solid #ccc;"></canvas>
                <div class="result">
                    Canvas 指纹: <span id="canvas-fingerprint"></span>
                </div>
            </div>
            
            <div class="test-section">
                <h2>🔊 AudioContext 指纹</h2>
                <div class="result">
                    AudioContext 支持: <span id="audio-support"></span><br>
                    采样率: <span id="sample-rate"></span><br>
                    状态: <span id="audio-state"></span>
                </div>
            </div>
            
            <div class="test-section">
                <h2>🛡️ 保护状态</h2>
                <div class="result">
                    CthulhuJS 插件: <span id="plugin-status"></span><br>
                    保护级别: <span id="protection-level"></span>
                </div>
            </div>
            
            <script>
                // 检测 CthulhuJS 插件状态
                const pluginLoaded = !!window.cthulhuJSAntiFingerprint;
                document.getElementById('plugin-status').innerHTML = pluginLoaded ? 
                    '<span class="protected">✅ 已激活</span>' : 
                    '<span class="warning">⚠️ 基本版本</span>';
                
                // 屏幕信息
                document.getElementById('screen-size').textContent = screen.width + 'x' + screen.height;
                document.getElementById('screen-avail').textContent = screen.availWidth + 'x' + screen.availHeight;
                document.getElementById('screen-color').textContent = screen.colorDepth + ' 位';
                
                // 语言和地区
                document.getElementById('language').textContent = navigator.language;
                document.getElementById('languages').textContent = navigator.languages.join(', ');
                document.getElementById('timezone').textContent = new Date().getTimezoneOffset() + ' 分钟';
                
                // 硬件信息
                document.getElementById('cpu-cores').textContent = navigator.hardwareConcurrency || '未知';
                document.getElementById('device-memory').textContent = (navigator.deviceMemory || '未知') + ' GB';
                document.getElementById('platform').textContent = navigator.platform;
                
                // Canvas 指纹测试
                const canvas = document.getElementById('test-canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillStyle = '#FF0000';
                ctx.fillRect(10, 10, 50, 20);
                ctx.fillStyle = '#000000';
                ctx.fillText('CthulhuJS Test', 15, 15);
                
                const canvasFingerprint = canvas.toDataURL();
                document.getElementById('canvas-fingerprint').textContent = canvasFingerprint.substring(0, 50) + '...';
                
                // AudioContext 测试
                try {
                    const AudioContext = window.AudioContext || window.webkitAudioContext;
                    if (AudioContext) {
                        const audioCtx = new AudioContext();
                        document.getElementById('audio-support').innerHTML = '<span class="protected">✅ 支持</span>';
                        document.getElementById('sample-rate').textContent = audioCtx.sampleRate + ' Hz';
                        document.getElementById('audio-state').textContent = audioCtx.state;
                        audioCtx.close();
                    } else {
                        document.getElementById('audio-support').innerHTML = '<span class="warning">❌ 不支持</span>';
                        document.getElementById('sample-rate').textContent = '不可用';
                        document.getElementById('audio-state').textContent = '不可用';
                    }
                } catch (e) {
                    document.getElementById('audio-support').innerHTML = '<span class="warning">❌ 错误</span>';
                    document.getElementById('sample-rate').textContent = '错误';
                    document.getElementById('audio-state').textContent = '错误';
                }
                
                // 保护级别评估
                let protectionScore = 0;
                if (pluginLoaded) protectionScore += 40;
                if (screen.width !== 1920) protectionScore += 15;
                if (navigator.language !== 'en-US') protectionScore += 15;
                if (navigator.hardwareConcurrency !== 8) protectionScore += 15;
                if (new Date().getTimezoneOffset() !== -480) protectionScore += 15;
                
                let protectionLevel = '';
                if (protectionScore >= 80) {
                    protectionLevel = '<span class="protected">🛡️ 高级保护</span>';
                } else if (protectionScore >= 50) {
                    protectionLevel = '<span style="color: #ffc107;">🔒 中级保护</span>';
                } else {
                    protectionLevel = '<span class="warning">⚠️ 基础保护</span>';
                }
                
                document.getElementById('protection-level').innerHTML = protectionLevel;
                
                // 定期更新动态信息
                setInterval(() => {
                    document.getElementById('screen-size').textContent = screen.width + 'x' + screen.height;
                    document.getElementById('language').textContent = navigator.language;
                    document.getElementById('cpu-cores').textContent = navigator.hardwareConcurrency || '未知';
                    document.getElementById('timezone').textContent = new Date().getTimezoneOffset() + ' 分钟';
                }, 2000);
                
                console.log('CthulhuJS Demo page loaded');
                console.log('Plugin status:', pluginLoaded ? 'Active' : 'Basic version');
                console.log('Protection score:', protectionScore + '/100');
            </script>
        </body>
        </html>
        '''
        
        # 导航到演示页面
        logger.log('🌐 加载演示页面...')
        data_url = 'data:text/html;charset=utf-8,' + test_html.replace('\n', '').replace(' ' * 8, '')
        automation.navigate_to_page(data_url)
        
        # 等待页面加载和脚本执行
        time.sleep(3)
        
        # 获取页面信息
        logger.log('📊 获取指纹信息...')
        fingerprint_info = automation.page.run_js('''
        return {
            pluginLoaded: !!window.cthulhuJSAntiFingerprint,
            screenSize: screen.width + 'x' + screen.height,
            language: navigator.language,
            hardwareConcurrency: navigator.hardwareConcurrency,
            deviceMemory: navigator.deviceMemory,
            timezoneOffset: new Date().getTimezoneOffset(),
            userAgent: navigator.userAgent.substring(0, 100) + '...'
        };
        ''')
        
        # 显示结果
        logger.log('🎯 指纹信息分析:')
        logger.log(f'   🐙 CthulhuJS 插件: {"✅ 已激活" if fingerprint_info.get("pluginLoaded") else "⚠️ 基本版本"}')
        logger.log(f'   🖥️ 屏幕尺寸: {fingerprint_info.get("screenSize")}')
        logger.log(f'   🌍 语言设置: {fingerprint_info.get("language")}')
        logger.log(f'   💻 CPU 核心: {fingerprint_info.get("hardwareConcurrency")}')
        logger.log(f'   💾 设备内存: {fingerprint_info.get("deviceMemory")} GB')
        logger.log(f'   🕐 时区偏移: {fingerprint_info.get("timezoneOffset")} 分钟')
        logger.log(f'   🤖 用户代理: {fingerprint_info.get("userAgent")}')
        
        # 保持页面打开一段时间供用户查看
        logger.log('👀 演示页面已加载，请在浏览器中查看效果')
        logger.log('💡 页面会每2秒更新一次动态信息，观察随机化效果')
        logger.log('⏰ 将在30秒后自动关闭...')
        
        # 等待用户观察
        for i in range(30, 0, -5):
            logger.log(f'⏱️ 剩余时间: {i} 秒')
            time.sleep(5)
        
        # 关闭浏览器
        automation.cleanup()
        logger.log('✅ 演示完成')
        
        return True
        
    except Exception as e:
        logger.log(f'❌ 演示失败: {e}')
        try:
            automation.cleanup()
        except:
            pass
        return False

def main():
    """主函数"""
    logger = DrissionPageLogger()
    
    logger.log('🎭 欢迎使用 CthulhuJS Anti-Fingerprint 插件演示')
    logger.log('📖 本演示将展示插件的反指纹保护功能')
    logger.log('')
    
    # 运行演示
    success = demo_fingerprint_protection()
    
    if success:
        logger.log('')
        logger.log('🎉 演示成功完成！')
        logger.log('💡 提示:')
        logger.log('   - 如果看到 "✅ 已激活"，说明 CthulhuJS 插件正常工作')
        logger.log('   - 如果看到 "⚠️ 基本版本"，说明使用的是内置基本版本')
        logger.log('   - 要使用完整版本，请从 Chrome Web Store 下载真实插件')
        logger.log('')
        logger.log('📚 更多信息请参考: CTHULHUJS_PLUGIN_GUIDE.md')
    else:
        logger.log('')
        logger.log('❌ 演示失败，请检查配置和日志')
        logger.log('🔧 故障排除:')
        logger.log('   1. 确保 .env 文件中 DRISSON_CTHULHUJS_PLUGIN=true')
        logger.log('   2. 检查 Chrome 浏览器是否正确安装')
        logger.log('   3. 查看详细错误日志')
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
