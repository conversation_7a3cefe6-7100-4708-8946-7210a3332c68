#!/usr/bin/env python3
"""
DrissionPage Browser Diagnosis Tool
诊断DrissionPage浏览器连接问题
"""

import sys
import os
import subprocess
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

def test_chrome_directly():
    """直接测试Chrome是否能启动"""
    print("🔧 测试Chrome直接启动...")
    
    chrome_commands = [
        ['google-chrome', '--version'],
        ['chromium-browser', '--version'],
        ['/usr/bin/google-chrome', '--version']
    ]
    
    for cmd in chrome_commands:
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ {cmd[0]} 版本: {result.stdout.strip()}")
                return cmd[0]
        except Exception as e:
            print(f"❌ {cmd[0]} 测试失败: {e}")
    
    return None

def test_chrome_with_display():
    """测试Chrome在当前DISPLAY下是否能启动"""
    print("🖥️ 测试Chrome在当前DISPLAY下启动...")
    
    try:
        # 尝试启动Chrome并立即关闭
        cmd = [
            'google-chrome',
            '--headless',
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--version'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            print("✅ Chrome在headless模式下启动正常")
            return True
        else:
            print(f"❌ Chrome启动失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Chrome测试异常: {e}")
        return False

def test_drissionpage_import():
    """测试DrissionPage导入"""
    print("📦 测试DrissionPage导入...")
    
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage导入成功")
        return True
    except ImportError as e:
        print(f"❌ DrissionPage导入失败: {e}")
        return False

def test_drissionpage_basic():
    """测试DrissionPage基础功能"""
    print("🚀 测试DrissionPage基础功能...")
    
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        
        # 创建选项
        options = ChromiumOptions()
        
        # 添加安全启动参数
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-setuid-sandbox')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--headless')  # 使用headless模式进行测试
        options.set_argument('--no-first-run')
        options.set_argument('--disable-background-timer-throttling')
        
        print("   🔧 创建ChromiumPage实例...")
        page = ChromiumPage(addr_or_opts=options)
        
        print("   ✅ ChromiumPage创建成功")
        
        print("   🌐 测试页面导航...")
        page.get('https://www.google.com')
        
        title = page.title
        print(f"   📄 页面标题: {title}")
        
        page.quit()
        print("   ✅ 浏览器关闭成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ DrissionPage测试失败: {e}")
        print(f"   🔍 错误类型: {type(e).__name__}")
        
        # 打印详细错误信息
        import traceback
        print("   📋 详细错误信息:")
        traceback.print_exc()
        
        return False

def test_drissionpage_with_display():
    """测试DrissionPage在有显示器的情况下"""
    print("🖥️ 测试DrissionPage非headless模式...")
    
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        
        options = ChromiumOptions()
        
        # 不使用headless，但添加其他安全参数
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-setuid-sandbox')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--no-first-run')
        options.set_argument('--start-maximized')
        
        print("   🔧 创建非headless ChromiumPage...")
        page = ChromiumPage(addr_or_opts=options)
        
        print("   ✅ 非headless模式启动成功")
        
        # 快速测试
        page.get('https://www.google.com')
        print(f"   📄 页面标题: {page.title}")
        
        page.quit()
        print("   ✅ 非headless测试完成")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 非headless模式失败: {e}")
        return False

def main():
    """主诊断流程"""
    print("🔍 DrissionPage浏览器连接诊断工具")
    print("=" * 60)
    
    print(f"🖥️ 当前DISPLAY: {os.environ.get('DISPLAY', '未设置')}")
    print(f"👤 当前用户: {os.environ.get('USER', '未知')}")
    print()
    
    # 1. 测试Chrome直接启动
    chrome_cmd = test_chrome_directly()
    if not chrome_cmd:
        print("❌ Chrome无法直接启动，请检查安装")
        return
    print()
    
    # 2. 测试Chrome在当前环境下启动
    if not test_chrome_with_display():
        print("❌ Chrome在当前环境下无法启动")
        return
    print()
    
    # 3. 测试DrissionPage导入
    if not test_drissionpage_import():
        print("❌ DrissionPage导入失败")
        return
    print()
    
    # 4. 测试DrissionPage基础功能（headless）
    if not test_drissionpage_basic():
        print("❌ DrissionPage基础功能测试失败")
        print("\n🔧 建议的解决方案:")
        print("1. 重新安装DrissionPage: pip3 install --upgrade DrissionPage")
        print("2. 检查Chrome权限: ls -la /usr/bin/google-chrome")
        print("3. 清理Chrome配置: rm -rf ~/.config/google-chrome/")
        return
    print()
    
    # 5. 测试DrissionPage非headless模式
    if test_drissionpage_with_display():
        print("🎉 所有测试通过！DrissionPage应该可以正常工作")
        print("\n💡 建议:")
        print("1. 您的环境配置正常，可以运行原始脚本")
        print("2. 如果仍有问题，可能是脚本中的特定配置导致")
    else:
        print("⚠️ headless模式正常，但非headless模式失败")
        print("💡 建议在脚本中添加 --headless 参数")

if __name__ == '__main__':
    main()
