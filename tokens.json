[{"id": "daf1d7e5-78bd-4d31-bce8-9e03e6d64d76", "createdTime": "2025-08-16T00:13:31.355Z", "createdTimestamp": 1755303211355, "access_token": "ccdc3411afa9b74c5c27788ae6f40dfd61f6c3947c0d6c3c83741cafd079e2c2", "tenant_url": "https://d8.api.augmentcode.com/", "oauth_state": {"codeVerifier": "IPqetTcs3KgDb4Ib6Uxik1RixrwOEtrDn6Tt565Y0fg", "codeChallenge": "4dn2rpW_91xZAKrRchtPO9zlUNN257E8BMiXiJ68Fvc", "state": "InPcG4vuX1s", "creationTime": 1755303131783}, "parsed_code": {"code": "_920e56913fa7a4dbae039c0a90e95883", "state": "InPcG4vuX1s", "tenant_url": "https://d8.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755303211352"}}, {"id": "475060e9-6b95-4a63-ad62-5f2ad117866d", "createdTime": "2025-08-16T00:15:27.653Z", "createdTimestamp": 1755303327653, "access_token": "8af5f930aaf2e9df97e56cf93f3992efab4383da5971f7729150d11433a39693", "tenant_url": "https://d1.api.augmentcode.com/", "oauth_state": {"codeVerifier": "BG79SKPPNklViVuvl4wZ4ydMuczKLsmiXtDKd8B68a4", "codeChallenge": "yDDGwE74lPWyzlFiwjoI7c5uwdDHctMsMvHC8Fcrfls", "state": "dHhpjtWLDjo", "creationTime": 1755303249774}, "parsed_code": {"code": "_40e4eb8166bc85a2b80835a507af78db", "state": "dHhpjtWLDjo", "tenant_url": "https://d1.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755303327652"}}, {"id": "3fd1d251-f689-4a14-ac38-89c60c692b28", "createdTime": "2025-08-16T00:28:13.374Z", "createdTimestamp": 1755304093374, "access_token": "46d4a89651abb10f6ccca2d0fbeb0e4f605304823669e00fdd8c4b2277ff7b95", "tenant_url": "https://d7.api.augmentcode.com/", "oauth_state": {"codeVerifier": "TN2tGmyRUBnVIn0Q_G-zkD1SdIS1fR8eAP65LWdwm1Q", "codeChallenge": "UVQptIN0Bzr5mmZJkUKPvqwbBDP7gB7JZVOktZvX_Fc", "state": "O0S6qbbe1EM", "creationTime": 1755304012405}, "parsed_code": {"code": "_b87cc0a12582e254da7aac8e9fe407ad", "state": "O0S6qbbe1EM", "tenant_url": "https://d7.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755304093372"}}, {"id": "c9e6e361-04fc-4188-a7a8-33288b951310", "createdTime": "2025-08-16T02:12:21.409Z", "createdTimestamp": 1755310341409, "access_token": "2f5fc57ade4ab267275a497d6594c1d853ad6421a007bba0cae70a23f968fb85", "tenant_url": "https://d20.api.augmentcode.com/", "oauth_state": {"codeVerifier": "9js65wFE_HZlnQwcQ0U62l8OJ4fxAtuBz8VmhlocwAw", "codeChallenge": "4aNFAIxTDP9G3F7iQUL6IFiodGVsmS9cCgETY0SfSeA", "state": "FXJQUDlwpJo", "creationTime": 1755310255283}, "parsed_code": {"code": "_73e6c0249eb75e782ca851bfb363e2c3", "state": "FXJQUDlwpJo", "tenant_url": "https://d20.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755310341406"}}, {"id": "ae6550fa-7b74-4cf3-8f9a-35fc30a8a8e5", "createdTime": "2025-08-16T02:55:12.108Z", "createdTimestamp": 1755312912108, "access_token": "4e472695199b219fb23903ed4903864037840afe8c012703df8ce62e874d3682", "tenant_url": "https://d9.api.augmentcode.com/", "oauth_state": {"codeVerifier": "euvnes5lrO5O9k2Zhzoz7TnhGzjZNL6epkh54xOP33M", "codeChallenge": "lmsCya3KA2RMmdTk8XDFrWlXRin6SdWEpGITrV4E08M", "state": "yMxdMrrT0IA", "creationTime": 1755312804877}, "parsed_code": {"code": "_3acf42b7297ede409124af3e02506434", "state": "yMxdMrrT0IA", "tenant_url": "https://d9.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755312912107"}}, {"id": "2ce0d452-5f3b-4841-b415-1761e554c833", "createdTime": "2025-08-16T03:38:13.794Z", "createdTimestamp": 1755315493794, "access_token": "01212c30a6cb75c82de92c4a45ccb162d9f58217adb29b009a9d2a8b69ad37f1", "tenant_url": "https://d20.api.augmentcode.com/", "oauth_state": {"codeVerifier": "MA0nX6hBOmSbCaUIoYDUPHrGUbkQ_CJshEMUJcDTxHk", "codeChallenge": "Lx6vUNhbMiWD60NoZYEQmevoZkw9E_EtgDKTWUOo7oc", "state": "NVetQVZ1BSs", "creationTime": 1755315407631}, "parsed_code": {"code": "_1b455b5a49aef2ad77eed69d3f7b4b55", "state": "NVetQVZ1BSs", "tenant_url": "https://d20.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755315493791"}}, {"id": "aad489a1-b381-4db8-a70c-46eb07e9b00d", "createdTime": "2025-08-16T03:43:28.277Z", "createdTimestamp": 1755315808277, "access_token": "df63c26f3515697567357b458f88c7741e8e50d016806e00c84b478f9030b1f0", "tenant_url": "https://d9.api.augmentcode.com/", "oauth_state": {"codeVerifier": "jErOasr48zri99uDZ7D00C97W1oCzUV-yJoYecmtZXY", "codeChallenge": "QB2Kggx32vjq_EjbeBvAR3Phran7tGT2Mj65Zy8HV1I", "state": "q5l1lwQ5i7g", "creationTime": 1755315720275}, "parsed_code": {"code": "_3b386a80bf310a33a7fd9b178c397824", "state": "q5l1lwQ5i7g", "tenant_url": "https://d9.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755315808276"}}, {"id": "42198fea-173c-4a11-8ea8-455ed7f325e9", "createdTime": "2025-08-16T03:45:00.061Z", "createdTimestamp": 1755315900061, "access_token": "4e82e2bbe47eb50fc3b1fe9ba45a9047f31642c05018ed3f288a61d4ee7fc4ec", "tenant_url": "https://d20.api.augmentcode.com/", "oauth_state": {"codeVerifier": "-POibPkqasum2-JnfCIfN9_9ikRpiVK6K8b_mdsVxj8", "codeChallenge": "XbrpxzA54XldG9hi5fUSz9caCJa3PtU9sVL5RcCcNyg", "state": "Aj2Tih7wMKE", "creationTime": 1755315812255}, "parsed_code": {"code": "_1fc9113901e8487a58c48d720bbfdf7a", "state": "Aj2Tih7wMKE", "tenant_url": "https://d20.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755315900060"}}, {"id": "b7733cc6-f051-420c-a7ef-2c24e1f26557", "createdTime": "2025-08-16T04:01:49.932Z", "createdTimestamp": 1755316909932, "access_token": "be4ddb808dec1bc12da4a6833c3cb5d2d39e626ada8d598853fd7fb40df2e5ac", "tenant_url": "https://d5.api.augmentcode.com/", "oauth_state": {"codeVerifier": "ApXSdHUqTbf_pyuNOWJRKKS8qoopiwhxqO7Oceyuvio", "codeChallenge": "4a0SJFyY_a-GOVjXmJ0FInI4ZQYiXH7_uY9L_wJon9M", "state": "XlKiqHADjIc", "creationTime": 1755316819345}, "parsed_code": {"code": "_128c1c1ce908c3ba99e3760caea980be", "state": "XlKiqHADjIc", "tenant_url": "https://d5.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755316909929"}}, {"id": "74240549-b9ed-4a66-806e-4b9ecb7a2a52", "createdTime": "2025-08-16T04:18:12.048Z", "createdTimestamp": 1755317892048, "access_token": "e223663b8ba4b19bc4dcab78de4ab542e0a19c362b386615453aaf86e5b2511f", "tenant_url": "https://d6.api.augmentcode.com/", "oauth_state": {"codeVerifier": "QCuEpcUCxt93oi_6DRQHTNo5OitAK1fwwTuYvB0C5wQ", "codeChallenge": "I-dsQCU_eCMaS1s6UaGhf-wc_16MjIuapqCcqYPNYXY", "state": "ofhUqCkh6yE", "creationTime": 1755317766174}, "parsed_code": {"code": "_4b1ee430a482b604c1001105e17607cd", "state": "ofhUqCkh6yE", "tenant_url": "https://d6.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755317892046"}}, {"id": "02bbffd1-068f-4d7a-a074-4cb7a0549b26", "createdTime": "2025-08-16T05:03:44.526Z", "createdTimestamp": 1755320624526, "access_token": "8ef7b3d8ec445411eae39def1f73f97fb9b8d0692eed1606b160c8056d02d6f8", "tenant_url": "https://d12.api.augmentcode.com/", "oauth_state": {"codeVerifier": "dz7iTsK5TMzk2-L3w9d98QW8fow5lIXdGUE-g2Sz-JM", "codeChallenge": "BY3huLV5ogGI-pmfUiMnuVD0w622ykfVBXG2eWwukpQ", "state": "wS6r9WbaY78", "creationTime": 1755320524977}, "parsed_code": {"code": "_9787447c5e9c223997175656de6ac3cb", "state": "wS6r9WbaY78", "tenant_url": "https://d12.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755320624523"}}, {"id": "f481d0af-8fee-46ce-a367-502afbf5aeab", "createdTime": "2025-08-16T07:51:45.004Z", "createdTimestamp": 1755330705004, "access_token": "960f9a6594ef9fbd427299b65ba698045b56ad674cb26aa20f2a6c8bdf0bd616", "tenant_url": "https://d12.api.augmentcode.com/", "oauth_state": {"codeVerifier": "Sub48uKJbalFUT_6Z8rxcmJ0KxETdnwBA2lhvwqdqJo", "codeChallenge": "0XscihQsXyLpKuydED4tivg1iU_8zliIcnvsdykenUo", "state": "kWaTXhzUBbU", "creationTime": 1755330601952}, "parsed_code": {"code": "_3c8430ad595014ff5d013911ad806a31", "state": "kWaTXhzUBbU", "tenant_url": "https://d12.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755330705002"}}, {"id": "75af6042-10a1-4a81-be88-9dd84d6656c9", "createdTime": "2025-08-16T10:10:01.187Z", "createdTimestamp": 1755339001187, "access_token": "84c720395c0fcdf5bac40f71b485b499b071e5513e0c44e122916f20210356a4", "tenant_url": "https://d20.api.augmentcode.com/", "oauth_state": {"codeVerifier": "vEaKEQ-IxQvNTcAv06nTdUBy24dTIM8AaGXjXEWBsZE", "codeChallenge": "_fh-neL5QA-V0o50TuTF8CClpX2U8yEutwsUG2cB4lE", "state": "rBwsSR6s-aw", "creationTime": 1755338840066}, "parsed_code": {"code": "_76dab726f8f9f085665b3b514cd1f8c7", "state": "rBwsSR6s-aw", "tenant_url": "https://d20.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755339001184"}}, {"id": "c4b5e65d-6ea3-473b-adf3-7e80c6126b06", "createdTime": "2025-08-16T10:14:25.427Z", "createdTimestamp": 1755339265427, "access_token": "d347f7387d998cbae771d0fc0a04aa85e39a271ecb2db6076220f75213e6e380", "tenant_url": "https://d15.api.augmentcode.com/", "oauth_state": {"codeVerifier": "Z3TRe6Q6Booz883AfMDPzNfqxMlbzSVzTcRiK1MYwTg", "codeChallenge": "6SPt3DmonIAJmK0poMYET4H2JXv40T_WnGLYffmBVHo", "state": "C7PsHjRAI8c", "creationTime": 1755339117144}, "parsed_code": {"code": "_1632b631ab3981880b81a25ec9b63b90", "state": "C7PsHjRAI8c", "tenant_url": "https://d15.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real token from Augment API via email verification", "metadata": {"user_agent": "augment-auto-email-verification", "ip_address": null, "session_id": "session_1755339265426"}}, {"id": "977d7313-6a82-4361-9502-f52b3e5956a7", "createdTime": "2025-08-16T13:04:24.861Z", "createdTimestamp": 1755349464861, "access_token": "1535f7be3c7807398e41c22e8df3c065ec5546ab5ca7cb4d00d51cdf44f26ccf", "tenant_url": "https://d12.api.augmentcode.com/", "oauth_state": {"codeVerifier": "ILkTh1OT4JtUAulK9V5XnGs_0T7MqtdJmdGt5Ar2X7w", "codeChallenge": "lYrdQ0zyhrcDKnB3Wsa74Uxw3oz6yLQtx4doCEA_Rxw", "state": "sOT8itbc8GY", "creationTime": 1755349376317}, "parsed_code": {"code": "_d5487abef1894304118bb230f625e14f", "state": "sOT8itbc8GY", "tenant_url": "https://d12.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real Browser token from Augment API via email verification", "metadata": {"user_agent": "real-browser-email-verification", "ip_address": null, "session_id": "real_browser_session_1755349464857"}}, {"id": "87524e22-cebb-4eb9-b6a7-4cbb693056f2", "createdTime": "2025-08-16T13:10:03.068Z", "createdTimestamp": 1755349803068, "access_token": "3f0ce937df5040fb9bd92a31e740867b839473f01b264f03b5dc7659c352afc3", "tenant_url": "https://d14.api.augmentcode.com/", "oauth_state": {"codeVerifier": "cMvCERsC8OGpB1YtrYAgJXzBpULcWRiku-0q7G5ekYA", "codeChallenge": "WkQTpbqq95G8OgSr1klWYEf9nLlA0elXKIhaQrmv_Cc", "state": "E-XEpvunuGk", "creationTime": 1755349707315}, "parsed_code": {"code": "_804b4961811fda3624d5b4eafb1215bf", "state": "E-XEpvunuGk", "tenant_url": "https://d14.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real Browser token from Augment API via email verification", "metadata": {"user_agent": "real-browser-email-verification", "ip_address": null, "session_id": "real_browser_session_1755349803067"}}, {"id": "5a1b5bc7-5442-4dbe-be81-808d38985c2c", "createdTime": "2025-08-16T21:20:40.775Z", "createdTimestamp": 1755350440775, "access_token": "94d7a8a76a18dffe7e470f9ec3f294a17729e508f156cfdba8de09dbd56a682b", "tenant_url": "https://d1.api.augmentcode.com/", "oauth_state": {"codeVerifier": "emgP9vHtLYKptZQnOKkrYb6B_vrFpDVDTjYy8I3IkVE", "codeChallenge": "x9hWe4WLEdyocdoj84qcrrpbORf9tTm3e6-F35OakQM", "state": "ufR5YhKwNH4", "creationTime": 1755350353674}, "parsed_code": {"code": "_391bbfad4cdf1dd2fe8fd29532cef6ad", "state": "ufR5YhKwNH4", "tenant_url": "https://d1.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real Browser token from Augment API via email verification", "metadata": {"user_agent": "real-browser-email-verification", "ip_address": null, "session_id": "real_browser_session_1755350440774"}}, {"id": "94b0fa3c-dcb2-46ed-9776-c03f9b8372b7", "createdTime": "2025-08-17T13:45:09.444Z", "createdTimestamp": 1755380709444, "access_token": "mock_token_1755409509", "tenant_url": "https://augmentcode.com", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755409509", "email": "<EMAIL>"}}, {"id": "268f558e-44db-4ff3-a654-4276cef6810e", "createdTime": "2025-08-17T14:52:10.569Z", "createdTimestamp": 1755413530569, "access_token": "076547b7b0de0cc5cd1bb362ca56c9ad2a992e94329707ef3d83ed111a93b675", "tenant_url": "https://d15.api.augmentcode.com/", "oauth_state": {"codeVerifier": "HKmYThD_3QFl1KgauFq6AWnDxo5tq1KLk2k8GymlQlU", "codeChallenge": "Tj2o7HmiT8jI9mzh9cV7OK6LuXiD_Yo5qHy82uf3tUM", "state": "Pp3ix-AK7ls", "creationTime": 1755413454495}, "parsed_code": {"code": "_4f2bcf3768784992854eb249c625ccc2", "state": "Pp3ix-AK7ls", "tenant_url": "https://d15.api.augmentcode.com/"}, "portal_url": null, "email_note": null, "description": "Real Browser token from Augment API via email verification", "metadata": {"user_agent": "real-browser-email-verification", "ip_address": null, "session_id": "real_browser_session_1755413530568"}}, {"id": "2d70bc54-8111-4650-9738-6d8572a6fd80", "createdTime": "2025-08-17T15:10:41.374Z", "createdTimestamp": 1755385841374, "access_token": "25d89ecac15f309adf3a6ba6474e5f978f36f889bbbfd6fef639d7821ee0241c", "tenant_url": "https://d7.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755414641", "email": "<EMAIL>"}}, {"id": "8d392223-02fd-4c17-963d-99d0397b4ce1", "createdTime": "2025-08-17T17:54:04.163Z", "createdTimestamp": 1755395644163, "access_token": "5ede788b890b776af9bd877ce89c95c2b5f893050aae02334bf9d517c6d828c9", "tenant_url": "https://d2.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755424444", "email": "<EMAIL>"}}, {"id": "1a8c9698-958e-4658-8ef5-04b1c269164f", "createdTime": "2025-08-17T18:21:24.260Z", "createdTimestamp": 1755397284260, "access_token": "1462d5ec33482a64e83a00cedb2f2258f762c14438ea7287c426bfe1973bb077", "tenant_url": "https://d3.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755426084", "email": "<EMAIL>"}}, {"id": "d45f782d-dd61-486f-8ddb-69c180bdd4a3", "createdTime": "2025-08-17T18:41:26.972Z", "createdTimestamp": 1755398486972, "access_token": "dcd8a9c10db04e0be7518963a8f5366d2a651edb1c49170ce9e9a2015d4f6db4", "tenant_url": "https://d11.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755427286", "email": "<EMAIL>"}}, {"id": "51b0326c-f25a-4de2-a6a2-04530b0f03c0", "createdTime": "2025-08-17T18:55:29.167Z", "createdTimestamp": 1755399329167, "access_token": "1f471831d9fb81d47ce46cfbb951e020f04087faf8a84f6998fea67da33a1872", "tenant_url": "https://d15.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755428129", "email": "<EMAIL>"}}, {"id": "124aa900-1800-4179-8db4-72cb49282460", "createdTime": "2025-08-18T08:57:43.434Z", "createdTimestamp": 1755449863434, "access_token": "3c2aeb20fcf2afee49e67339e579b7805119f94ed466d42c9a0d0f36675a29dc", "tenant_url": "https://d19.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755478663", "email": "<EMAIL>"}}, {"id": "4c3456d2-bdba-4acb-8b65-77bfcb2e17ff", "createdTime": "2025-08-18T09:34:30.315Z", "createdTimestamp": 1755452070315, "access_token": "e4aa24fb9c82519c2ea0d794a47b1093341a8ad5b020a7d45d64ae0de87d9a08", "tenant_url": "https://d3.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755480870", "email": "<EMAIL>"}}, {"id": "db3ab35e-8fb7-4b59-8534-bb1a458c4b52", "createdTime": "2025-08-18T11:00:10.890Z", "createdTimestamp": 1755457210890, "access_token": "61fbc32a813ac3286e9e94b315d3fe022237f38e93d6bc15299e3d46868437ba", "tenant_url": "https://d19.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755486010", "email": "<EMAIL>"}}, {"id": "54c5ffeb-7a41-47e0-832a-36fa522b55e5", "createdTime": "2025-08-18T22:17:55.159Z", "createdTimestamp": 1755497875159, "access_token": "a3ddd9ea8b013ae3461311bf21710160687f25ac5ba75adbbcee7dd951956661", "tenant_url": "https://d17.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "Firefox token from fixed automation", "metadata": {"user_agent": "firefox-fixed-automation", "ip_address": null, "session_id": "firefox_fixed_1755526675", "email": "<EMAIL>"}}, {"id": "16d053b2-47c7-427b-be03-b4703d081637", "createdTime": "2025-08-18T22:21:47.739Z", "createdTimestamp": 1755498107739, "access_token": "ea6cc1b8c3bf6e3afde00054aaeab4b96c976a5d92ff7f21ff85d8536134b8f9", "tenant_url": "https://d19.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "Firefox token from fixed automation", "metadata": {"user_agent": "firefox-fixed-automation", "ip_address": null, "session_id": "firefox_fixed_1755526907", "email": "<EMAIL>"}}, {"id": "8919a8da-a645-489d-a60b-ad85e4e88e38", "createdTime": "2025-08-18T22:26:12.902Z", "createdTimestamp": 1755498372902, "access_token": "c708cb4816ab3a3f751e149363b5276f8865a7405fa20675292a3c6fd04247c2", "tenant_url": "https://d5.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "david<PERSON><EMAIL>", "description": "Firefox token from fixed automation", "metadata": {"user_agent": "firefox-fixed-automation", "ip_address": null, "session_id": "firefox_fixed_1755527172", "email": "david<PERSON><EMAIL>"}}, {"id": "7b213eed-19cb-432a-bbec-66f3f9a38fe4", "createdTime": "2025-08-18T22:33:05.287Z", "createdTimestamp": 1755498785287, "access_token": "74a11124111ee21cd6b5c9dba976426c43a7a4bb7874f926ee5bc9b41a6fe2e3", "tenant_url": "https://d4.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "Firefox token from fixed automation", "metadata": {"user_agent": "firefox-fixed-automation", "ip_address": null, "session_id": "firefox_fixed_1755527585", "email": "<EMAIL>"}}, {"id": "cbdf4fcd-e6db-447c-908a-646dba1fcb34", "createdTime": "2025-08-18T22:55:38.583Z", "createdTimestamp": 1755500138583, "access_token": "fcb787f57fc44e1bb3bc082012948e15b8275b53aaa5f90ce0ecaccb125b26bf", "tenant_url": "https://d11.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "Firefox token from fixed automation", "metadata": {"user_agent": "firefox-fixed-automation", "ip_address": null, "session_id": "firefox_fixed_1755528938", "email": "<EMAIL>"}}, {"id": "cb631726-2c33-49d4-8bde-ae5653f4db86", "createdTime": "2025-08-19T10:27:44.678Z", "createdTimestamp": 1755541664678, "access_token": "2ece07abb7bdb139b66bd59e791813c68dd84c65db28f0cbfecb15fcb6cb4bfa", "tenant_url": "https://d2.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "Firefox token from fixed automation", "metadata": {"user_agent": "firefox-fixed-automation", "ip_address": null, "session_id": "firefox_fixed_1755570464", "email": "<EMAIL>"}}, {"id": "5a1ebdb9-bf77-497a-83d2-c653c5fa2a32", "createdTime": "2025-08-19T10:29:43.634Z", "createdTimestamp": 1755541783634, "access_token": "5c03fbc079760c5ecb7d978e24bb2ef6866a16ae3fe7c49dd879830bc413772d", "tenant_url": "https://d18.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "Firefox token from fixed automation", "metadata": {"user_agent": "firefox-fixed-automation", "ip_address": null, "session_id": "firefox_fixed_1755570583", "email": "<EMAIL>"}}, {"id": "30d70c2f-4517-4148-bd29-d7ffd6356152", "createdTime": "2025-08-19T18:17:15.560Z", "createdTimestamp": 1755569835560, "access_token": "04c0c19a21ca8a07e1beb68e7e2c3e86ab4274ac9033fdc1953b411eca6ef022", "tenant_url": "https://d14.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "2879<PERSON><PERSON><EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755598635", "email": "2879<PERSON><PERSON><EMAIL>"}}, {"id": "b8195d51-a026-43e7-ab61-4ad110e69d74", "createdTime": "2025-08-19T18:44:25.631Z", "createdTimestamp": 1755571465631, "access_token": "44709af0942a5e4589220bce3799184638067151e4fbac53c76fe75aa92bf203", "tenant_url": "https://d19.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755600261", "email": "<EMAIL>"}}, {"id": "a78d769f-78af-4672-9b37-2b891b4a7d89", "createdTime": "2025-08-19T18:58:04.087Z", "createdTimestamp": 1755572284087, "access_token": "ba8091d8c1088f8873ddf7cbe13eb6d8f557c08ded2ee37756549aa0884685c1", "tenant_url": "https://d4.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "3373bettym6221<PERSON><EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755601080", "email": "3373bettym6221<PERSON><EMAIL>"}}, {"id": "e5787f82-ecd7-4601-9d28-df5f193b472c", "createdTime": "2025-08-19T19:02:34.757+00:00Z", "createdTimestamp": 1755601354757, "access_token": "218a5ac4d3b060fda16f1d58808cebf2bf2d697ec039e10d330b87f7c47f34a3", "tenant_url": "https://d13.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755601354", "email": "<EMAIL>"}}, {"id": "200cf388-392c-41c8-88d6-8e0dc81dee95", "createdTime": "2025-08-19T19:21:59.784+00:00Z", "createdTimestamp": 1755602519784, "access_token": "fb80cf519359949f12c1140cbc8d369b09c89de9b793a3f0b71f2c3d00c5812e", "tenant_url": "https://d4.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755602519", "email": "<EMAIL>"}}, {"id": "04c3d241-a62c-4d88-9a04-7ea9ae394a60", "createdTime": "2025-08-19T21:15:23.825+00:00Z", "createdTimestamp": 1755609323825, "access_token": "f95f632715edc6cd78f5b0430fe38ae9017efda8490e947d5c261f3592f82a4b", "tenant_url": "https://d12.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755609323", "email": "<EMAIL>"}}, {"id": "9d708004-9dc1-4452-9463-caf7ec0d1662", "createdTime": "2025-08-19T21:21:45.243+00:00Z", "createdTimestamp": 1755609705243, "access_token": "ba61dc09857f449ae4554d0024e490de03482a8b0270d6a7490759cabc052b84", "tenant_url": "https://d13.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "<EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755609705", "email": "<EMAIL>"}}, {"id": "aa46e6ef-ca52-4920-9490-8b5d97fbca69", "createdTime": "2025-08-19T21:27:41.075+00:00Z", "createdTimestamp": 1755610061075, "access_token": "ff92ff76e7f6124763ca52a2d4032e9afba8b22f0051246f4648c2ce97e7c454", "tenant_url": "https://d3.api.augmentcode.com/", "oauth_state": null, "parsed_code": null, "portal_url": null, "email_note": "kimberl3899<PERSON><PERSON><PERSON><EMAIL>", "description": "DrissionPage token from Augment API via email verification", "metadata": {"user_agent": "drissionpage-email-verification", "ip_address": null, "session_id": "drissionpage_session_1755610060", "email": "kimberl3899<PERSON><PERSON><PERSON><EMAIL>"}}]