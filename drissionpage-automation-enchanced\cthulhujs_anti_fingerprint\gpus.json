{"ARM": ["Mali-T880", "Mali-G76", "Mali-G72", "Mali-G52"], "Qualcomm": ["Adreno(TM) 618", "Adreno(TM) 616", "Adreno(TM) 405", "Adreno(TM) 640", "Adreno(TM) 308", "Adreno(TM) 505"], "Apple Inc.": ["Apple GPU"], "Google Inc.": {"Amd": ["ANGLE (AMD, RENOIR(renoir LLVM 15.0.7), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.6), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.5), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.4), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.3), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.2), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.1), OpenGL 4.6)", "ANGLE (AMD, RENOIR(renoir LLVM 15.0.0), OpenGL 4.6)"], "Intel": ["ANGLE (Intel, Mesa Intel(R) Graphics (RPL-S), OpenGL 4.6)", "ANGLE (Intel, Mesa Intel(R) Graphics (RPL-P), OpenGL 4.6)", "ANGLE (Intel, Intel(R) Iris(R) Plus Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)", "ANGLE (Intel, Intel(R) Iris(R) Plus Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)", "ANGLE (Intel, Intel(R) HD Graphics 4000 Direct3D11 vs_5_0 ps_5_0, D3D11-10.18.10.5059)", "ANGLE (Intel, Intel(R) HD Graphics 4000 Direct3D11 vs_5_0 ps_5_0, D3D11-10.18.13.5582)", "ANGLE (Intel, Intel(R) HD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11-10.18.10.4885)"], "Apple": ["ANGLE (Apple, Apple M1, OpenGL 4.1)", "ANGLE (Apple, Apple M1 Max, OpenGL 4.1)", "ANGLE (Apple, Apple M2 Max, OpenGL 4.1)", "ANGLE (Apple, Apple M2, OpenGL 4.1)", "ANGLE (Apple, Apple M1 Pro, OpenGL 4.1)", "ANGLE (Apple, Apple M2 Pro, OpenGL 4.1)"], "Nvidia": ["ANGLE (NVIDIA, NVIDIA GeForce GTX 960 Direct3D11 vs_5_0 ps_5_0, D3D11-26.21.14.4575)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 750 Ti Direct3D11 vs_5_0 ps_5_0, D3D11-25.21.14.1735)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 980 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.6089)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-23.21.13.9135)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 650 Direct3D11 vs_5_0 ps_5_0, D3D11)", "ANGLE (NVIDIA, NVIDIA GeForce RTX 2060 Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5185)", "ANGLE (NVIDIA, NVIDIA GeForce GTX 1050 Direct3D11 vs_5_0 ps_5_0, D3D11-22.21.13.8476)"]}}