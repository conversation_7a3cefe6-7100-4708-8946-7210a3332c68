#!/usr/bin/env node

const axios = require('axios');

async function simpleTriggerTest() {
  try {
    console.log('Testing health endpoint...');
    const healthResponse = await axios.get('http://localhost:9043/health');
    console.log('Health:', healthResponse.data.message);

    console.log('\nTesting trigger automation...');
    const response = await axios.post(
      'http://localhost:9043/api/tokens/trigger-automation',
      { count: 1 },
      {
        headers: {
          'Authorization': 'Bearer your_secret_password_here_change_this',
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('Trigger response:', response.data);
    
  } catch (error) {
    console.error('Error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

simpleTriggerTest();
