# Firefox Automation - Augment 反欺诈系统对抗

这是基于 **Selenium WebDriver + Firefox** 的自动化版本，保留了 Enhanced 版本的核心逻辑，但使用 Firefox 浏览器替代 Chrome。

## 🦊 Firefox 版本特点

### 优势
- **更好的隐私保护**: Firefox 内置 `privacy.resistFingerprinting` 功能
- **更少的检测特征**: Firefox 的自动化检测相对较少
- **稳定的指纹防护**: 不依赖扩展，使用浏览器内置功能
- **跨平台兼容**: 支持 Windows、Linux、macOS

### 与 Enhanced 版本的区别
- 使用 **Selenium WebDriver** 替代 DrissionPage
- 使用 **Firefox** 替代 Chrome
- 使用 **Firefox Profile** 配置替代 Chrome 扩展
- 保留所有核心自动化逻辑

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装 Python 依赖
pip install selenium webdriver-manager

# 安装 Firefox (如果尚未安装)
# Ubuntu/Debian:
sudo apt update && sudo apt install firefox

# Windows: 从 https://www.mozilla.org/firefox/ 下载
# macOS: brew install firefox
```

### 2. 安装 GeckoDriver

```bash
# 使用 webdriver-manager 自动管理 (推荐)
pip install webdriver-manager

# 或手动下载 GeckoDriver
# 从 https://github.com/mozilla/geckodriver/releases 下载
# 并添加到 PATH
```

### 3. 配置环境变量

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置
nano .env
```

### 4. 运行测试

```bash
# 基础测试
python run_firefox_verification.py

# 完整自动化 (需要配置邮箱等)
python drissionpage_automation.py
```

## ⚙️ 配置说明

### Firefox 专用环境变量

```bash
# Firefox 显示模式
DRISSON_FIREFOX_HEADFULL=true    # 显示浏览器窗口
DRISSON_FIREFOX_HEADFULL=false   # 无头模式

# Firefox 代理
DRISSON_FIREFOX_PROXY=true       # 启用代理
DRISSON_FIREFOX_PROXY=false      # 禁用代理
```

### 配置优先级

1. **Firefox 专用配置** (`DRISSON_FIREFOX_*`) - 最高优先级
2. **通用配置** (`DRISSIONPAGE_*`) - 回退配置

### 指纹防护配置

Firefox 版本使用内置的指纹防护功能：

```python
# 自动启用的防护功能
privacy.resistFingerprinting = True
webgl.disabled = True
device.sensors.enabled = False
dom.battery.enabled = False
```

## 🛡️ 指纹防护特性

### 内置防护
- **Canvas 指纹**: `privacy.resistFingerprinting`
- **WebGL 指纹**: `webgl.disabled`
- **设备传感器**: `device.sensors.enabled = False`
- **电池信息**: `dom.battery.enabled = False`
- **媒体设备**: `media.navigator.enabled = False`

### 用户代理
根据操作系统自动设置合适的 Firefox User Agent：
- **Linux**: `Mozilla/5.0 (X11; Linux x86_64; rv:133.0) Gecko/20100101 Firefox/133.0`
- **Windows**: `Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0`
- **macOS**: `Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:133.0) Gecko/20100101 Firefox/133.0`

## 🔧 代理配置

Firefox 版本支持 HTTP/HTTPS 代理：

```bash
PROXY_URL=proxy-host:port
PROXY_USER=username
PROXY_PASS=password
```

**注意**: Firefox 的代理认证配置比 Chrome 复杂，建议使用无认证代理或配置系统级代理。

## 📁 文件结构

```
drissionpage-automation-firefox/
├── drissionpage_automation.py     # 主自动化脚本 (Firefox 版本)
├── config.py                      # 配置管理 (支持 Firefox 专用配置)
├── run_firefox_verification.py    # Firefox 测试脚本
├── .env.example                   # 环境变量示例
├── README_FIREFOX.md              # 本文档
└── ... (其他共享文件)
```

## 🚨 注意事项

### 1. GeckoDriver 版本
确保 GeckoDriver 版本与 Firefox 版本兼容。

### 2. 代理认证
Firefox 的代理认证比 Chrome 复杂，如遇问题建议：
- 使用无认证代理
- 配置系统级代理
- 使用代理扩展

### 3. 性能差异
Firefox 的启动和页面加载可能比 Chrome 稍慢，这是正常现象。

### 4. 扩展支持
Firefox 版本不支持 Chrome 扩展，指纹防护完全依赖浏览器内置功能。

## 🔍 调试技巧

### 1. 启用显示模式
```bash
DRISSON_FIREFOX_HEADFULL=true
```

### 2. 查看日志
所有操作都会记录详细日志，包括截图和 HTML 保存。

### 3. 检查 Firefox 配置
可以在 Firefox 地址栏输入 `about:config` 查看所有配置项。

## 🆚 版本对比

| 特性 | Enhanced (Chrome) | Firefox |
|------|------------------|---------|
| 浏览器 | Chrome/Chromium | Firefox |
| 驱动 | DrissionPage | Selenium |
| 指纹防护 | 扩展 + JS | 内置功能 |
| 代理支持 | 扩展认证 | 配置文件 |
| 启动速度 | 快 | 中等 |
| 检测难度 | 中等 | 较低 |

## 📞 支持

如果遇到问题：
1. 检查 Firefox 和 GeckoDriver 版本兼容性
2. 查看详细日志输出
3. 尝试禁用代理测试
4. 使用显示模式调试
