#!/usr/bin/env python3
"""
测试DrissionPage GUI模式配置
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import DrissionPageAutomation
from config import DrissionPageConfig

def test_gui_mode():
    """测试GUI模式配置"""
    print("🧪 测试DrissionPage GUI模式配置")
    print("=" * 50)
    
    # 创建配置实例
    config = DrissionPageConfig()
    config.print_config()
    print()
    
    # 创建自动化实例
    automation = DrissionPageAutomation()
    
    try:
        print("🚀 尝试启动浏览器...")
        automation.init_browser()
        
        print("✅ 浏览器启动成功！")
        
        if config.gui_mode and not config.headless:
            print("🖥️ GUI模式启动成功，您应该能看到浏览器窗口")
        else:
            print("👻 Headless模式启动成功")
        
        # 测试导航
        print("🌐 测试页面导航...")
        automation.navigate_to_page('https://www.google.com')
        
        print(f"📄 页面标题: {automation.page.title}")
        
        # 保持浏览器打开一段时间以便观察
        if config.gui_mode and not config.headless:
            print("⏳ 浏览器将保持打开10秒以便观察...")
            import time
            time.sleep(10)
        
        print("✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        
        # 提供故障排除建议
        print("\n🔧 故障排除建议:")
        print("1. 确保DISPLAY环境变量正确设置")
        print("2. 尝试运行: xhost +local:")
        print("3. 检查是否有其他Chrome进程占用端口9222")
        print("4. 尝试切换回headless模式: DRISSIONPAGE_HEADLESS=true")
        
    finally:
        # 清理资源
        automation.cleanup()

if __name__ == '__main__':
    test_gui_mode()
