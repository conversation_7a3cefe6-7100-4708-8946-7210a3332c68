#!/usr/bin/env python3
"""
测试 CRX 文件提取
"""

import os
import zipfile
from pathlib import Path

def test_crx_extraction():
    """测试 CRX 文件提取"""
    crx_path = Path(__file__).parent / 'cthulhujs_anti_fingerprint.crx'
    
    if not crx_path.exists():
        print('❌ CRX 文件不存在')
        return False
    
    print(f'📦 CRX 文件大小: {crx_path.stat().st_size} 字节')
    
    try:
        with open(crx_path, 'rb') as f:
            # 读取前16字节检查格式
            header = f.read(16)
            print(f'📄 文件头: {header[:8]}')
            
            # 检查是否是 CRX 格式
            if header[:4] == b'Cr24':
                print('✅ 检测到标准 CRX 格式')
                
                # 解析 CRX 头部
                version = int.from_bytes(header[4:8], 'little')
                pub_key_len = int.from_bytes(header[8:12], 'little')
                sig_len = int.from_bytes(header[12:16], 'little')
                
                print(f'   版本: {version}')
                print(f'   公钥长度: {pub_key_len}')
                print(f'   签名长度: {sig_len}')
                
                # 跳过公钥和签名，获取 ZIP 数据
                f.seek(16 + pub_key_len + sig_len)
                zip_data = f.read()
                
                print(f'📦 ZIP 数据大小: {len(zip_data)} 字节')
                
                # 尝试解压 ZIP 数据
                import io
                try:
                    with zipfile.ZipFile(io.BytesIO(zip_data)) as zip_file:
                        file_list = zip_file.namelist()
                        print(f'📁 包含文件: {len(file_list)} 个')
                        
                        for file_name in file_list[:10]:  # 只显示前10个文件
                            print(f'   - {file_name}')
                        
                        if len(file_list) > 10:
                            print(f'   ... 还有 {len(file_list) - 10} 个文件')
                        
                        # 检查是否有 manifest.json
                        if 'manifest.json' in file_list:
                            manifest_content = zip_file.read('manifest.json').decode('utf-8')
                            import json
                            manifest = json.loads(manifest_content)
                            print(f'✅ 插件名称: {manifest.get("name", "未知")}')
                            print(f'✅ 插件版本: {manifest.get("version", "未知")}')
                            print(f'✅ 描述: {manifest.get("description", "无描述")[:100]}...')
                            
                            # 检查是否包含 "Basic" 字样
                            if "Basic" in manifest.get("name", ""):
                                print('⚠️ 这似乎是基本版本，不是完整版本')
                                return False
                            else:
                                print('🎉 这看起来是完整版本！')
                                return True
                        else:
                            print('❌ 未找到 manifest.json')
                            return False
                            
                except zipfile.BadZipFile:
                    print('❌ ZIP 数据损坏')
                    return False
                    
            elif header[:2] == b'PK':
                print('✅ 检测到 ZIP 格式（可能是重命名的 ZIP 文件）')
                
                # 直接作为 ZIP 文件处理
                f.seek(0)
                zip_data = f.read()
                
                try:
                    with zipfile.ZipFile(io.BytesIO(zip_data)) as zip_file:
                        file_list = zip_file.namelist()
                        print(f'📁 包含文件: {len(file_list)} 个')
                        
                        if 'manifest.json' in file_list:
                            manifest_content = zip_file.read('manifest.json').decode('utf-8')
                            import json
                            manifest = json.loads(manifest_content)
                            print(f'✅ 插件名称: {manifest.get("name", "未知")}')
                            print(f'✅ 插件版本: {manifest.get("version", "未知")}')
                            return True
                        else:
                            print('❌ 未找到 manifest.json')
                            return False
                            
                except zipfile.BadZipFile:
                    print('❌ ZIP 数据损坏')
                    return False
            else:
                print('❌ 未知文件格式')
                print(f'   文件头: {header}')
                return False
                
    except Exception as e:
        print(f'❌ 提取失败: {e}')
        return False

if __name__ == '__main__':
    print('🔍 测试 CRX 文件提取...')
    print('=' * 50)
    
    success = test_crx_extraction()
    
    print('')
    if success:
        print('✅ CRX 文件是有效的完整版本插件')
        print('💡 如果系统仍显示 Basic，可能是解压逻辑需要修复')
    else:
        print('❌ CRX 文件可能不是完整版本或已损坏')
        print('💡 建议重新下载真正的 CthulhuJS Anti-Fingerprint 插件')
