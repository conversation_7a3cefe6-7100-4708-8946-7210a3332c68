#!/usr/bin/env python3
"""
使用 CthulhuJS Anti-Fingerprint 插件运行 DrissionPage 自动化
这是一个简单的启动脚本，展示如何启用 CthulhuJS 插件
"""

import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import DrissionPageAutomation
from drissionpage_logger import DrissionPageLogger

def main():
    """主函数"""
    logger = DrissionPageLogger()
    
    logger.log('🚀 启动 DrissionPage 自动化 (集成 CthulhuJS 插件)')
    logger.log('=' * 60)
    
    # 确保启用 CthulhuJS 插件
    os.environ['DRISSON_CTHULHUJS_PLUGIN'] = 'true'
    os.environ['DRISSON_FINGERPRINT_PROTECTION'] = 'true'
    
    logger.log('🔧 配置检查:')
    logger.log(f'   🐙 CthulhuJS 插件: {"启用" if os.getenv("DRISSON_CTHULHUJS_PLUGIN", "false").lower() == "true" else "禁用"}')
    logger.log(f'   🛡️ 指纹防护: {"启用" if os.getenv("DRISSON_FINGERPRINT_PROTECTION", "false").lower() == "true" else "禁用"}')
    logger.log(f'   🖥️ 显示模式: {"有界面" if os.getenv("DRISSIONPAGE_HEADFULL", "false").lower() == "true" else "无界面"}')
    
    try:
        # 创建自动化实例
        automation = DrissionPageAutomation()
        
        # 初始化浏览器
        logger.log('\n🚀 初始化浏览器...')
        automation.init_browser()
        
        # 导航到测试页面
        logger.log('🌐 导航到测试页面...')
        test_url = 'https://browserleaks.com/canvas'
        automation.navigate_to_page(test_url)
        
        logger.log('✅ 浏览器启动成功！')
        logger.log('💡 现在可以在浏览器中查看指纹保护效果')
        logger.log('🔍 建议访问以下网站测试指纹保护:')
        logger.log('   - https://browserleaks.com/canvas (Canvas 指纹)')
        logger.log('   - https://browserleaks.com/webgl (WebGL 指纹)')
        logger.log('   - https://audiofingerprint.openwpm.com/ (音频指纹)')
        
        # 保持浏览器打开
        input('\n按 Enter 键关闭浏览器...')
        
        # 清理
        automation.cleanup()
        logger.log('✅ 程序正常结束')
        
    except KeyboardInterrupt:
        logger.log('\n⚠️ 用户中断程序')
        try:
            automation.cleanup()
        except:
            pass
    except Exception as e:
        logger.log(f'❌ 程序执行失败: {e}')
        try:
            automation.cleanup()
        except:
            pass
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
