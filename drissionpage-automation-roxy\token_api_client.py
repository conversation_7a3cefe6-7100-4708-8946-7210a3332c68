"""
Token API Client
用于将获取的令牌保存到 token-api 服务
"""

import os
import requests
import json
import time
from datetime import datetime, timedelta
from pathlib import Path

# Load environment variables from .env file
env_path = Path(__file__).parent.parent / '.env'
if env_path.exists():
    with open(env_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key] = value


class TokenAPIClient:
    """Token API 客户端"""
    
    def __init__(self):
        self.api_base_url = os.getenv('TOKEN_API_BASE_URL', 'http://localhost:3000')
        self.api_secret = os.getenv('AUTH_PASSWORD', 'your_secret_password_here_change_this')
        
        # 验证配置
        if not self.api_secret or self.api_secret == 'your_secret_password_here_change_this':
            print('[WARN] 警告: AUTH_PASSWORD 未正确配置')
            print('[TIP] 请在 .env 文件中设置: AUTH_PASSWORD')
    
    def save_token(self, token_response, metadata):
        """
        保存令牌到 Token API 服务
        
        Args:
            token_response: 包含 access_token, tenant_url 等的响应对象
            metadata: 包含邮箱、描述等元数据的字典
        
        Returns:
            dict: API 响应结果
        """
        try:
            print('[TOKEN_API] [START] 开始保存令牌到 Token API 服务...')
            
            # 准备请求数据
            request_data = {
                "access_token": getattr(token_response, 'access_token', ''),
                "tenant_url": getattr(token_response, 'tenant_url', ''),
                "description": metadata.get('description', 'RoxyBrowser + DrissionPage token from automation'),
                "email_note": metadata.get('email', ''),
                "user_agent": metadata.get('user_agent', 'roxybrowser-drissionpage-automation'),
                "session_id": metadata.get('session_id', f'roxy_drissionpage_{int(time.time())}'),
                "created_timestamp": int(time.time() * 1000)
            }
            
            # 准备请求头
            headers = {
                'Authorization': f'Bearer {self.api_secret}',
                'Content-Type': 'application/json'
            }
            
            # 发送请求
            url = f"{self.api_base_url}/api/tokens/save"
            print(f'[TOKEN_API] 📡 发送请求到: {url}')
            print(f'[TOKEN_API] 📝 请求数据: {json.dumps(request_data, indent=2)}')
            
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                timeout=30
            )
            
            # 检查响应
            if response.status_code == 200 or response.status_code == 201:
                result = response.json()
                print(f'[TOKEN_API] [OK] 令牌保存成功!')
                print(f'[TOKEN_API] [COPY] 响应: {json.dumps(result, indent=2)}')
                return {
                    'success': True,
                    'data': result,
                    'token_id': result.get('id', 'unknown')
                }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                print(f'[TOKEN_API] [ERROR] 令牌保存失败: {error_msg}')
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            print(f'[TOKEN_API] [ERROR] {error_msg}')
            return {
                'success': False,
                'error': error_msg
            }
        except Exception as e:
            error_msg = f"保存令牌时出错: {str(e)}"
            print(f'[TOKEN_API] [ERROR] {error_msg}')
            return {
                'success': False,
                'error': error_msg
            }
    
    def test_connection(self):
        """测试 API 连接"""
        try:
            print('[TOKEN_API] [SEARCH] 测试 API 连接...')
            
            # 尝试访问健康检查端点
            health_url = f"{self.api_base_url}/health"
            response = requests.get(health_url, timeout=10)
            
            if response.status_code == 200:
                print('[TOKEN_API] [OK] API 服务连接正常')
                return True
            else:
                print(f'[TOKEN_API] [WARN] API 服务响应异常: HTTP {response.status_code}')
                return False
                
        except requests.exceptions.RequestException as e:
            print(f'[TOKEN_API] [ERROR] API 服务连接失败: {str(e)}')
            print(f'[TOKEN_API] [TIP] 请确认 Token API 服务是否启动: {self.api_base_url}')
            return False
    
    def get_config_info(self):
        """获取配置信息"""
        return {
            'api_base_url': self.api_base_url,
            'api_secret_configured': bool(self.api_secret and self.api_secret != 'your_secret_password_here_change_this'),
            'api_secret_preview': f"{self.api_secret[:10]}..." if self.api_secret else None
        }


class EnhancedTokenStorage:
    """
    增强版令牌存储器
    支持 Token API 和本地文件两种存储方式
    """
    
    def __init__(self):
        self.api_client = TokenAPIClient()
        self.use_api = os.getenv('USE_TOKEN_API', 'true').lower() == 'true'
        
        # 本地存储备份
        self.tokens_file = Path(__file__).parent.parent / 'tokens.json'
        
        print(f'[TOKEN_STORAGE] [CONFIG] 令牌存储模式: {"Token API" if self.use_api else "本地文件"}')
        
        if self.use_api:
            config = self.api_client.get_config_info()
            print(f'[TOKEN_STORAGE] [WEB] API 地址: {config["api_base_url"]}')
            print(f'[TOKEN_STORAGE] [KEY] API 密钥: {"已配置" if config["api_secret_configured"] else "未配置"}')
    
    def save_token(self, token_response, metadata):
        """
        保存令牌（优先使用 API，失败时回退到本地文件）
        
        Args:
            token_response: 令牌响应对象
            metadata: 元数据字典
        
        Returns:
            dict: 保存结果
        """
        if self.use_api:
            # 尝试使用 Token API
            api_result = self.api_client.save_token(token_response, metadata)
            
            if api_result['success']:
                print(f'[TOKEN_STORAGE] [OK] 令牌已保存到 Token API，ID: {api_result.get("token_id", "unknown")}')
                
                # 同时保存本地备份
                try:
                    self._save_local_backup(token_response, metadata)
                    print('[TOKEN_STORAGE] [SAVE] 本地备份已保存')
                except Exception as e:
                    print(f'[TOKEN_STORAGE] [WARN] 本地备份保存失败: {str(e)}')
                
                return api_result
            else:
                print('[TOKEN_STORAGE] [WARN] Token API 保存失败，回退到本地文件')
                return self._save_to_local_file(token_response, metadata)
        else:
            # 直接使用本地文件
            return self._save_to_local_file(token_response, metadata)
    
    def _save_local_backup(self, token_response, metadata):
        """保存本地备份"""
        return self._save_to_local_file(token_response, metadata, is_backup=True)
    
    def _save_to_local_file(self, token_response, metadata, is_backup=False):
        """保存到本地文件"""
        try:
            import uuid
            import secrets
            
            # 生成令牌 ID
            try:
                token_id = str(uuid.uuid4())
            except Exception:
                token_id = f"drissionpage_{int(time.time())}_{secrets.token_hex(8)}"
            
            # 生成时间字段
            import datetime as dt
            now = dt.datetime.now(dt.timezone.utc)
            utc8 = now + dt.timedelta(hours=8)
            created_time_iso = utc8.isoformat(timespec='milliseconds') + 'Z'
            created_timestamp = int(now.timestamp() * 1000)
            
            # 组装令牌数据
            token_data = {
                'id': token_id,
                'createdTime': created_time_iso,
                'createdTimestamp': created_timestamp,
                'access_token': getattr(token_response, 'access_token', ''),
                'tenant_url': getattr(token_response, 'tenant_url', ''),
                'oauth_state': getattr(token_response, 'oauth_state', None),
                'parsed_code': getattr(token_response, 'parsed_code', None),
                'portal_url': metadata.get('portal_url'),
                'email_note': metadata.get('email'),
                'description': metadata.get('description', 'RoxyBrowser + DrissionPage token from automation'),
                'metadata': {
                    'user_agent': metadata.get('user_agent', 'roxybrowser-drissionpage-automation'),
                    'ip_address': metadata.get('ip_address'),
                    'session_id': metadata.get('session_id', f'roxy_drissionpage_{int(time.time())}'),
                    'email': metadata.get('email'),
                }
            }
            
            # 读取现有令牌
            tokens = []
            if self.tokens_file.exists():
                try:
                    with open(self.tokens_file, 'r', encoding='utf-8') as f:
                        tokens = json.load(f)
                except Exception:
                    tokens = []
            
            # 添加新令牌
            tokens.append(token_data)
            
            # 保存到文件
            with open(self.tokens_file, 'w', encoding='utf-8') as f:
                json.dump(tokens, f, indent=2, ensure_ascii=False)
            
            action = "本地备份" if is_backup else "本地文件"
            print(f'[TOKEN_STORAGE] [OK] 令牌已保存到{action}，ID: {token_id}')
            
            return {
                'success': True,
                'token_id': token_id,
                'file_path': str(self.tokens_file)
            }
            
        except Exception as e:
            error_msg = f"保存到本地文件失败: {str(e)}"
            print(f'[TOKEN_STORAGE] [ERROR] {error_msg}')
            return {
                'success': False,
                'error': error_msg
            }


def test_token_api():
    """测试 Token API 功能"""
    print("🧪 测试 Token API 功能...")
    
    client = TokenAPIClient()
    
    # 测试连接
    if client.test_connection():
        print("[OK] Token API 连接测试通过")
    else:
        print("[ERROR] Token API 连接测试失败")
        return False
    
    # 测试配置
    config = client.get_config_info()
    print(f"[COPY] 配置信息: {json.dumps(config, indent=2)}")
    
    return True


if __name__ == '__main__':
    test_token_api()
