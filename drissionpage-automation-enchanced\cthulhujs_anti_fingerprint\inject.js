
// CthulhuJS Anti-Fingerprint Injection Script
(function() {
    'use strict';

    console.log('CthulhuJS Anti-Fingerprint injection script loaded');

    // 防止重复注入
    if (window.cthulhuJSAntiFingerprint) {
        return;
    }
    window.cthulhuJSAntiFingerprint = true;

    // WebGL 指纹防护
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
        if (contextType === 'webgl' || contextType === 'webgl2' || contextType === 'experimental-webgl') {
            const context = originalGetContext.call(this, contextType, contextAttributes);
            if (context) {
                // 随机化 WebGL 参数
                const originalGetParameter = context.getParameter;
                context.getParameter = function(parameter) {
                    const result = originalGetParameter.call(this, parameter);

                    // 随机化一些关键参数
                    if (parameter === context.RENDERER) {
                        return 'Intel Iris OpenGL Engine';
                    }
                    if (parameter === context.VENDOR) {
                        return 'Intel Inc.';
                    }
                    if (parameter === context.VERSION) {
                        return 'OpenGL ES 2.0';
                    }

                    return result;
                };
            }
            return context;
        }
        return originalGetContext.call(this, contextType, contextAttributes);
    };

    // Canvas 指纹防护
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function() {
        // 添加微小的随机噪声
        const context = this.getContext('2d');
        if (context) {
            const imageData = context.getImageData(0, 0, this.width, this.height);
            const data = imageData.data;

            // 随机修改少量像素
            for (let i = 0; i < data.length; i += 4) {
                if (Math.random() < 0.001) { // 0.1% 的像素
                    data[i] = Math.min(255, data[i] + Math.floor(Math.random() * 3) - 1);
                    data[i + 1] = Math.min(255, data[i + 1] + Math.floor(Math.random() * 3) - 1);
                    data[i + 2] = Math.min(255, data[i + 2] + Math.floor(Math.random() * 3) - 1);
                }
            }

            context.putImageData(imageData, 0, 0);
        }

        return originalToDataURL.apply(this, arguments);
    };

    // AudioContext 指纹防护
    if (window.AudioContext || window.webkitAudioContext) {
        const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
        const audioContexts = new WeakMap();

        function AudioContextWrapper() {
            const context = new OriginalAudioContext();
            audioContexts.set(this, context);
            return new Proxy(this, {
                get(target, prop) {
                    const realContext = audioContexts.get(target);
                    if (prop === 'createAnalyser') {
                        return function() {
                            const analyser = realContext.createAnalyser();
                            const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                            analyser.getFloatFrequencyData = function(array) {
                                originalGetFloatFrequencyData.call(this, array);
                                // 添加微小的随机噪声
                                for (let i = 0; i < array.length; i++) {
                                    array[i] += (Math.random() - 0.5) * 0.0001;
                                }
                            };
                            return analyser;
                        };
                    }
                    return realContext[prop];
                }
            });
        }

        AudioContextWrapper.prototype = OriginalAudioContext.prototype;
        window.AudioContext = AudioContextWrapper;
        if (window.webkitAudioContext) {
            window.webkitAudioContext = AudioContextWrapper;
        }
    }

    // 屏幕分辨率随机化
    Object.defineProperty(screen, 'width', {
        get: function() { return 1920 + Math.floor(Math.random() * 100); }
    });
    Object.defineProperty(screen, 'height', {
        get: function() { return 1080 + Math.floor(Math.random() * 100); }
    });
    Object.defineProperty(screen, 'availWidth', {
        get: function() { return 1920 + Math.floor(Math.random() * 100); }
    });
    Object.defineProperty(screen, 'availHeight', {
        get: function() { return 1040 + Math.floor(Math.random() * 60); }
    });

    // 时区随机化
    const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
    Date.prototype.getTimezoneOffset = function() {
        // 返回一些常见的时区偏移
        const commonOffsets = [-480, -420, -360, -300, -240, -180, 0, 60, 120, 180, 240, 300, 360, 420, 480, 540];
        return commonOffsets[Math.floor(Math.random() * commonOffsets.length)];
    };

    // 语言随机化
    Object.defineProperty(navigator, 'language', {
        get: function() {
            const languages = ['en-US', 'en-GB', 'zh-CN', 'zh-TW', 'ja-JP', 'ko-KR', 'fr-FR', 'de-DE', 'es-ES', 'it-IT'];
            return languages[Math.floor(Math.random() * languages.length)];
        }
    });

    Object.defineProperty(navigator, 'languages', {
        get: function() {
            const allLanguages = [
                ['en-US', 'en'],
                ['en-GB', 'en'],
                ['zh-CN', 'zh'],
                ['zh-TW', 'zh'],
                ['ja-JP', 'ja'],
                ['ko-KR', 'ko'],
                ['fr-FR', 'fr'],
                ['de-DE', 'de'],
                ['es-ES', 'es'],
                ['it-IT', 'it']
            ];
            return allLanguages[Math.floor(Math.random() * allLanguages.length)];
        }
    });

    // 硬件并发数随机化
    Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: function() {
            const cores = [2, 4, 6, 8, 12, 16];
            return cores[Math.floor(Math.random() * cores.length)];
        }
    });

    // 内存大小随机化
    Object.defineProperty(navigator, 'deviceMemory', {
        get: function() {
            const memories = [2, 4, 8, 16, 32];
            return memories[Math.floor(Math.random() * memories.length)];
        }
    });

    console.log('CthulhuJS Anti-Fingerprint protection activated');
})();
