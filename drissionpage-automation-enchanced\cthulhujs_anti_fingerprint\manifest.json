{"manifest_version": 3, "name": "CthulhuJS Anti-Fingerprint (Basic)", "version": "1.0.0", "description": "Basic anti-fingerprinting protection inspired by CthulhuJS", "permissions": ["activeTab", "scripting", "storage"], "host_permissions": ["<all_urls>"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start", "all_frames": true}], "background": {"service_worker": "background.js"}, "web_accessible_resources": [{"resources": ["inject.js"], "matches": ["<all_urls>"]}]}