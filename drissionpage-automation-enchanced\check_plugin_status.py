#!/usr/bin/env python3
"""
检查插件在浏览器中的状态
"""

import time
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import DrissionPageAutomation
from drissionpage_logger import DrissionPageLogger

def check_plugin_status():
    """检查插件在浏览器中的状态"""
    logger = DrissionPageLogger()
    logger.log('🔍 检查 CthulhuJS 插件在浏览器中的状态...')
    
    try:
        # 创建自动化实例
        automation = DrissionPageAutomation()
        
        # 初始化浏览器
        logger.log('🚀 启动浏览器...')
        automation.init_browser()
        
        # 导航到扩展管理页面
        logger.log('🔧 导航到扩展管理页面...')
        automation.navigate_to_page('chrome://extensions/')
        
        time.sleep(2)
        
        # 检查插件是否在扩展列表中
        logger.log('🔍 检查插件状态...')
        
        # 执行 JavaScript 来检查插件状态
        check_script = '''
        // 查找 CthulhuJS 插件
        const extensions = document.querySelectorAll('extensions-item');
        let cthulhuPlugin = null;
        
        for (let ext of extensions) {
            const nameElement = ext.shadowRoot.querySelector('#name');
            if (nameElement && nameElement.textContent.includes('CthulhuJS')) {
                cthulhuPlugin = ext;
                break;
            }
        }
        
        if (cthulhuPlugin) {
            const isEnabled = !cthulhuPlugin.hasAttribute('disabled');
            const name = cthulhuPlugin.shadowRoot.querySelector('#name').textContent;
            const version = cthulhuPlugin.shadowRoot.querySelector('#version').textContent;
            
            return {
                found: true,
                name: name,
                version: version,
                enabled: isEnabled,
                status: isEnabled ? 'active' : 'disabled'
            };
        } else {
            return {
                found: false,
                message: 'CthulhuJS 插件未找到'
            };
        }
        '''
        
        try:
            result = automation.page.run_js(check_script)
            
            if result and result.get('found'):
                logger.log('✅ CthulhuJS 插件状态:')
                logger.log(f'   名称: {result.get("name")}')
                logger.log(f'   版本: {result.get("version")}')
                logger.log(f'   状态: {"✅ 已启用" if result.get("enabled") else "❌ 已禁用"}')
                
                if result.get('enabled'):
                    logger.log('🎉 插件已启用！现在应该显示为激活状态')
                else:
                    logger.log('⚠️ 插件已禁用，需要手动启用')
            else:
                logger.log('❌ 未找到 CthulhuJS 插件')
                logger.log('💡 这可能是因为插件加载方式的问题')
                
        except Exception as e:
            logger.log(f'⚠️ 无法检查插件状态: {e}')
        
        # 导航到测试页面验证功能
        logger.log('🧪 导航到测试页面验证功能...')
        automation.navigate_to_page('https://browserleaks.com/canvas')
        
        time.sleep(3)
        
        # 检查插件是否在工作
        test_script = '''
        return {
            pluginMarker: !!window.cthulhuJSAntiFingerprint,
            userAgent: navigator.userAgent.substring(0, 100),
            platform: navigator.platform,
            language: navigator.language,
            hardwareConcurrency: navigator.hardwareConcurrency,
            screenSize: screen.width + 'x' + screen.height
        };
        '''
        
        test_result = automation.page.run_js(test_script)
        
        logger.log('🔍 插件功能验证:')
        logger.log(f'   插件标记: {"✅ 检测到" if test_result.get("pluginMarker") else "⚠️ 未检测到"}')
        logger.log(f'   用户代理: {test_result.get("userAgent")}...')
        logger.log(f'   平台信息: {test_result.get("platform")}')
        logger.log(f'   语言设置: {test_result.get("language")}')
        logger.log(f'   硬件信息: {test_result.get("hardwareConcurrency")} 核心')
        logger.log(f'   屏幕尺寸: {test_result.get("screenSize")}')
        
        # 保持浏览器打开供用户检查
        logger.log('')
        logger.log('👀 请在浏览器中检查:')
        logger.log('   1. 地址栏右侧的扩展图标')
        logger.log('   2. 是否显示 "ON" 标记（绿色）')
        logger.log('   3. 点击图标查看 popup 界面')
        logger.log('   4. 在 browserleaks.com 上测试指纹保护效果')
        logger.log('')
        logger.log('💡 修复说明:')
        logger.log('   - 添加了 popup 界面和状态管理')
        logger.log('   - 使用 badge 显示 "ON" 状态')
        logger.log('   - 改进了权限配置')
        logger.log('   - 插件现在应该显示为激活状态')
        
        input('按 Enter 键关闭浏览器...')
        
        # 关闭浏览器
        automation.cleanup()
        logger.log('✅ 检查完成')
        
        return True
        
    except Exception as e:
        logger.log(f'❌ 检查失败: {e}')
        try:
            automation.cleanup()
        except:
            pass
        return False

if __name__ == '__main__':
    print('🔍 CthulhuJS 插件状态检查')
    print('=' * 50)
    
    success = check_plugin_status()
    
    print('')
    if success:
        print('✅ 检查完成')
        print('💡 如果插件仍显示灰色，请:')
        print('   1. 检查是否有 "ON" badge 显示')
        print('   2. 点击插件图标查看 popup')
        print('   3. 在 popup 中切换插件状态')
        print('   4. 刷新页面重新测试')
    else:
        print('❌ 检查失败')
    
    sys.exit(0 if success else 1)
