# RoxyBrowser Automation Makefile

.PHONY: help install test quick-start api-test basic-test run clean

# Default target
help:
	@echo "🔥 RoxyBrowser Automation 工具"
	@echo "================================"
	@echo ""
	@echo "可用命令:"
	@echo "  make install      - 安装依赖"
	@echo "  make quick-start  - 快速配置检查"
	@echo "  make api-test     - 测试 API 功能"
	@echo "  make basic-test   - 测试基本功能"
	@echo "  make test         - 运行所有测试"
	@echo "  make run          - 运行完整验证流程"
	@echo "  make clean        - 清理生成的文件"
	@echo ""
	@echo "使用前请确保:"
	@echo "  1. RoxyBrowser 软件已启动"
	@echo "  2. .env 文件中已配置 ROXYBROWSER_API_TOKEN"
	@echo "  3. API 功能已在 RoxyBrowser 中启用"

# Install dependencies
install:
	@echo "📦 安装依赖..."
	pip install -r requirements.txt
	@echo "✅ 依赖安装完成"

# Quick configuration check
quick-start:
	@echo "🚀 快速配置检查..."
	python quick_start.py

# Test API functionality
api-test:
	@echo "🔍 测试 API 功能..."
	python test_roxybrowser_api.py

# Test basic functionality
basic-test:
	@echo "🧪 测试基本功能..."
	python test_basic_functionality.py

# Run all tests
test: api-test basic-test
	@echo "✅ 所有测试完成"

# Run full verification process
run:
	@echo "🔥 运行完整验证流程..."
	python run_roxybrowser_verification.py

# Clean generated files
clean:
	@echo "🧹 清理生成的文件..."
	@if exist screenshots rmdir /s /q screenshots
	@if exist html rmdir /s /q html
	@if exist logs rmdir /s /q logs
	@if exist __pycache__ rmdir /s /q __pycache__
	@mkdir screenshots html logs
	@echo "✅ 清理完成"

# Development helpers
dev-setup: install
	@echo "🔧 开发环境设置..."
	@echo "请确保在 .env 文件中设置以下变量:"
	@echo "  ROXYBROWSER_API_TOKEN=your_token_here"
	@echo "  ROXYBROWSER_WORKSPACE_ID=1"
	@echo "  PROXY_URL=your_proxy_host:port"
	@echo "  PROXY_USER=your_proxy_username"
	@echo "  PROXY_PASS=your_proxy_password"

# Show configuration
config:
	@echo "📋 当前配置:"
	python -c "from config import RoxyBrowserConfig; RoxyBrowserConfig().print_config()"
