
// CthulhuJS Anti-Fingerprint Background Script
console.log('CthulhuJS Anti-Fingerprint extension loaded');

// 监听扩展安装
chrome.runtime.onInstalled.addListener(() => {
    console.log('CthulhuJS Anti-Fingerprint extension installed');
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'loading' && tab.url) {
        // 注入反指纹脚本
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['inject.js']
        }).catch(err => {
            // 忽略错误，某些页面可能无法注入
        });
    }
});
