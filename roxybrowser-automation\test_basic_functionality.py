#!/usr/bin/env python3
"""
RoxyBrowser 基本功能测试
测试浏览器启动、导航等基本功能
"""

import sys
import time
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from roxybrowser_automation import RoxyBrowserAutomation

def test_browser_startup():
    """测试浏览器启动"""
    print("🚀 测试 RoxyBrowser 启动...")
    
    automation = RoxyBrowserAutomation()
    
    try:
        # 启动浏览器
        if automation.start_browser():
            print("✅ 浏览器启动成功")
            
            # 导航到测试页面
            test_url = "https://httpbin.org/get"
            print(f"🌐 导航到测试页面: {test_url}")
            
            if automation.navigate_to_url(test_url):
                print("✅ 页面导航成功")
                
                # 获取页面信息
                current_url = automation.get_current_url()
                page_title = automation.get_page_title()
                
                print(f"📄 当前URL: {current_url}")
                print(f"📝 页面标题: {page_title}")
                
                # 等待一下让用户看到结果
                print("⏳ 等待 5 秒...")
                time.sleep(5)
                
                return True
            else:
                print("❌ 页面导航失败")
                return False
        else:
            print("❌ 浏览器启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False
        
    finally:
        # 清理资源
        automation.cleanup()

def test_proxy_functionality():
    """测试代理功能"""
    print("\n🌐 测试代理功能...")
    
    automation = RoxyBrowserAutomation()
    
    try:
        # 检查代理配置
        proxy_config = automation.config.get_proxy_config()
        if proxy_config:
            print(f"📡 代理配置: {proxy_config['host']}:{proxy_config['port']}")
        else:
            print("📡 未配置代理")
        
        # 启动浏览器
        if automation.start_browser():
            print("✅ 浏览器启动成功")
            
            # 检查IP地址
            ip_check_url = "https://httpbin.org/ip"
            print(f"🔍 检查IP地址: {ip_check_url}")
            
            if automation.navigate_to_url(ip_check_url):
                print("✅ IP检查页面加载成功")
                
                # 等待页面加载完成
                time.sleep(3)
                
                # 获取页面内容（简单检查）
                page_source = automation.driver.page_source
                if '"origin"' in page_source:
                    print("✅ IP信息获取成功")
                    # 可以进一步解析IP信息
                else:
                    print("⚠️ IP信息格式异常")
                
                # 等待一下
                print("⏳ 等待 5 秒...")
                time.sleep(5)
                
                return True
            else:
                print("❌ IP检查页面加载失败")
                return False
        else:
            print("❌ 浏览器启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 代理测试异常: {str(e)}")
        return False
        
    finally:
        # 清理资源
        automation.cleanup()

def test_fingerprint_randomization():
    """测试指纹随机化"""
    print("\n🎲 测试指纹随机化...")
    
    automation = RoxyBrowserAutomation()
    
    try:
        # 启动浏览器
        if automation.start_browser():
            print("✅ 浏览器启动成功")
            
            # 导航到指纹检测页面
            fingerprint_url = "https://httpbin.org/headers"
            print(f"🔍 检查浏览器指纹: {fingerprint_url}")
            
            if automation.navigate_to_url(fingerprint_url):
                print("✅ 指纹检测页面加载成功")
                
                # 等待页面加载
                time.sleep(3)
                
                # 检查User-Agent
                page_source = automation.driver.page_source
                if "User-Agent" in page_source:
                    print("✅ User-Agent信息获取成功")
                else:
                    print("⚠️ User-Agent信息未找到")
                
                # 等待一下
                print("⏳ 等待 5 秒...")
                time.sleep(5)
                
                return True
            else:
                print("❌ 指纹检测页面加载失败")
                return False
        else:
            print("❌ 浏览器启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 指纹测试异常: {str(e)}")
        return False
        
    finally:
        # 清理资源
        automation.cleanup()

def main():
    """主测试函数"""
    print("🔥 RoxyBrowser 基本功能测试")
    print("=" * 50)
    
    tests = [
        ("浏览器启动", test_browser_startup),
        ("代理功能", test_proxy_functionality),
        ("指纹随机化", test_fingerprint_randomization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"💥 {test_name} 测试异常: {str(e)}")
        
        # 等待一下再进行下一个测试
        if test_name != tests[-1][0]:  # 不是最后一个测试
            print("⏳ 等待 3 秒后进行下一个测试...")
            time.sleep(3)
    
    print("\n" + "=" * 50)
    print(f"🏁 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试异常: {str(e)}")
        sys.exit(1)
