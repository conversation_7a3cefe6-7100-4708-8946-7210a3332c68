var Ti=Object.defineProperty;var _t=t=>{throw TypeError(t)};var ki=(t,e,i)=>e in t?Ti(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i;var j=(t,e,i)=>ki(t,typeof e!="symbol"?e+"":e,i),Ve=(t,e,i)=>e.has(t)||_t("Cannot "+i);var b=(t,e,i)=>(Ve(t,e,"read from private field"),i?i.call(t):e.get(t)),q=(t,e,i)=>e.has(t)?_t("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,i),L=(t,e,i,s)=>(Ve(t,e,"write to private field"),s?s.call(t,i):e.set(t,i),i),ne=(t,e,i)=>(Ve(t,e,"access private method"),i);const Kt=function(t,e,i){return t=(t||0)+9,t=Math.sin(t)*(i-e)*2,t|=0,Math.abs(t)%(i-e+1)+e},qt=function(t,e){let i=Kt(t,0,e.length-1);return e[i]};function Ht(){function t(){return((1+Math.random())*65536|0).toString(16).substring(1)}return t()+t()+"-"+t()+"-"+t()+"-"+t()+"-"+t()+t()+t()}function Ai(t){return/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/.test(t)}function Mi(t={}){const e=(g,x)=>`Linux; Android ${g}; ${x}`,i=(g,x="")=>(x=x.replace(".","_"),g==="Macintosh"?`Macintosh; Intel Mac OS X ${x}`:`${g}; CPU ${g!=="iPad"?"iPhone ":""}OS ${x} like Mac OS X`),s=g=>`Windows NT ${g}; Win64; x64`,n=()=>"X11; Linux x86_64",r=(g="",x,m)=>(m=m||"537.36",g=g.toLowerCase(),g==="safari"?`AppleWebKit/${m} (KHTML, like Gecko) Version/${x} Safari/${m}`:g==="chrome"?`AppleWebKit/${m} (KHTML, like Gecko) Chrome/${x} Safari/${m}`:g==="firefox"?(m=m||x,`Gecko/${m} Firefox/${x}`):`AppleWebKit/${m} (KHTML, like Gecko) Chrome/${m} Safari/${m} ${g}/${x}`),l=(g,x,m)=>(m=m||"534.46",g=g.toLowerCase(),g==="safari"?`AppleWebKit/${m} (KHTML, like Gecko) Version/5.1 Mobile/9A334 Safari/${m}`:g==="chrome"?`AppleWebKit/${m} (KHTML, like Gecko) CriOS/${x} Mobile/10B350 Safari/${m}`:g==="firefox"?(m=m||x,`Gecko/${m} Mobile Firefox/${x}`):`AppleWebKit/${m} (KHTML, like Gecko) Chrome/${m} Mobile/10B351 Safari/${m} ${g}/${x}`),h=(g,x,m="")=>(g=g.toLowerCase(),g==="windows"?s(x):g==="linux"?n():g==="macos"?i("Macintosh",x):g==="ios"?(m=m.toLowerCase(),i(m==="tablet"?"iPad":"iPhone",x)):g==="android"?e(x,m):`${g} ${x}; ${m} `),f=(g,x,m,v)=>(g=g.toLowerCase(),g==="windows"||g==="linux"||g==="macos"?r(x,m,v):g==="ios"||g==="android"?l(x,m,v):r(x,m,v));let{osName:w="unknown",deviceName:d="unknown",browserName:y="unknown",osVersion:a="1.0.0",browserVersion:u="1.0.0",engineVersion:c=""}=t,o=h(w,a,d),p=f(w,y,u,c);return`Mozilla/5.0 (${o}) ${p}`}function Oi(t={},e=new lt(Math.random())){const i=()=>e.item(["10.0","5.1","6.1","6.2","6.3"]),s=()=>{let d=e.int(6,15),y=e.int(0,15),a=e.int(0,9),u=d+"_"+y;return a!==0&&(u+="_"+a),u},n=()=>{let d=e.int(4,16);if(d<9){let y=e.item(["0","1","0.0","1.0","1.1","0.1","1.2"]);return d+"."+y}return d+""},r=(d=1,y=10)=>{let a=e.int(d,y),u=e.int(0,10),c=e.int(0,20);return[a,u,c].join(".")};let{osName:l,osVersion:h,browserVersion:f,browserName:w}=t;if(l=l.toLowerCase(),h||(l==="windows"?h=i():l==="mac"||l==="ios"?h=s():l==="android"?h=n():l==="linux"&&(h="")),!f){let d=w.toLowerCase();d==="chrome"||d==="edge"?f=r(76,123):d==="safari"?f=r(1,15):d==="firefox"?f=r(71,112):f=r()}return Mi({...t,osName:l,osVersion:h,browserVersion:f,browserName:w})}class lt{constructor(e=0){this.seed=e}int(e,i){let s=Kt(this.seed,e,i);return this.seed=s+this.seed,Math.abs(this.seed)>99999999&&(this.seed/=2),s}item(e=[]){let i=qt(this.seed,e);return this.seed=this.seed+e.length,Math.abs(this.seed)>99999999&&(this.seed/=2),i}}const re=self.chrome||self.browser,ge={languages:void 0,regions:void 0,devices:void 0,setting:void 0,webglInfos:void 0,defaultConfig:void 0};async function Ci(){if(!(re!=null&&re.runtime))return;const t={languages:re.runtime.getURL("languages.json"),regions:re.runtime.getURL("regions.json"),devices:re.runtime.getURL("devices.json"),webglInfos:re.runtime.getURL("webglInfos.json"),defaultConfig:re.runtime.getURL("defaultConfig.json")},e=Object.entries(t).map(([i,s])=>fetch(s,{method:"get"}).then(async n=>{ge[i]=await n.json()}));await Promise.all(e),console.log("configs loaded",ge)}const Ft=(self.chrome||{}).storage?[["canvas","audio","webgl","webgpu"],["voice","plugins","fonts","clientRect"]]:[["canvas","audio","webgpu","webgl","voice"],["clientRect","plugins","fonts","ja3","h2"]];[{label:"Canvas(绘图)",key:"canvas"},{label:"Audio(声音)",key:"audio"},{label:"Webgl(3D绘图)",key:"webgl"},{label:"WebGPU(GPU)",key:"webgpu"},{label:"Plugins(插件)",key:"plugins"},{label:"Fonts(字体)",key:"fonts"},{label:"ClientRect(视口)",key:"clientRect"},{label:"Voice(语音)",key:"voice"},{label:"Ja3(Tls)",key:"ja3"},{label:"h2(Http2)",key:"h2"}].filter(t=>Ft.flat().includes(t.key));const Ri=["audio","canvas","webgl","fonts","webgpu","clientRect","voice","plugin","native","webrtc","date","screen","location","navigator","feature","media","worker","iframe","trace","driver"].sort();let Ee;try{Ee="1_7_6_2_0_1_2_7_9_9_0_0_0".split("_").join("")/1,Ee=Ee||1/0}catch{Ee=1/0}const st=Ee,xe=location.host.startsWith("localhost")?"dev":"",et=self.chrome||self.browser;class ct{constructor(e,i=!1){this.name=e,this.indexKey="__index__",this.isSession=i,this.storage=i?et.storage.session:xe==="dev"?{}:et.storage.local||et.storage.sync}keys(){return this.get(this.indexKey).then(e=>(e=new Set(e||[]),e.delete(this.indexKey),e))}get(e){return new Promise(async(i,s)=>{if(!e||new Date>st)return i();if(e=this.name+"-"+e,xe==="dev"){let h=(this.isSession?sessionStorage:localStorage).getItem(e)||"",f=JSON.parse(h);return i(f)}let l=(await this.storage.get([e]))[e];i(l)})}set(e,i){return new Promise((s,n)=>{if(!e||new Date>st)return s(!1);let l=this.name+"-"+e;if(xe==="dev")return(this.isSession?sessionStorage:localStorage).setItem(l,JSON.stringify(i)),s(!0);e!==this.indexKey&&this.keys().then(f=>((f||new Set).add(e),this.set(this.indexKey,[...f])));let h={};return h[l]=typeof i>"u"?"":i,this.storage.set(h,()=>s(!0))})}remove(e){return new Promise(async(i,s)=>{if(!e)return i();let n=this.name+"-"+e;return xe==="dev"?((this.isSession?sessionStorage:localStorage).removeItem(n),i()):(await this.keys().then(r=>((r||new Set).delete(e),this.set(this.indexKey,[...r]))),this.storage.remove(n,i))})}clear(){return new Promise(async(e,i)=>{let s=await this.keys();if(xe==="dev"){for(let r of s)(this.isSession?sessionStorage:localStorage).removeItem(this.name+"-"+r);return e()}let n=[this.name+"-"+this.indexKey];return s.forEach(r=>{n.push(this.name+"-"+r)}),this.storage.remove(n).then(e)})}iterWith(e,i){return this.keys().then(async s=>{for(let n of s){if(!(n+"").startsWith(e))continue;if(await this.get(n).then(l=>i(n,l))===!1)return}})}}const ce=new ct("browser"),P=new ct("config"),We=new ct("scope",!1);self.browserTree=ce;self.configTree=P;self.scopeTree=We;const ee=self.chrome||self.browser,_i=new Set;async function Ni(t){let e=await ee.windows.getCurrent(),i={id:t,host:"",port:0,scheme:"",tabId:t,windowId:e.id,frameId:0,email:"",ua:navigator.userAgent,ip:"127.0.0.1"};if(t===0)return i;let s=await ee.tabs.get(t);if(!s)return;let{url:n,windowId:r}=s,{host:l,port:h=0,protocol:f}=new URL(n);return{...i,host:l,port:h,scheme:f,tabId:t,windowId:r}}async function nt(t,e){if(new Date().getTime()>st)return;let i=await Oe();i&&(await Li(t,i),await Pi(t,i),console.log("injected: "+t))}async function Nt(t){}async function Ii(){let t=await ee.tabs.query({url:["*://*/*"]})||[],e=[];t.forEach(i=>{if(!i.url.startsWith("http")||_i.has(i.id))return;let s=nt(i.id);e.push(s)}),await Promise.all(e)}async function Li(t,e){e&&(console.log(`tab:${t} injectedTabJs name=${e.name}`),await ee.scripting.executeScript({target:{tabId:t,allFrames:!0},args:[e],func:i=>{const s="SCOPE_BROWSER";if(self[s]=i,sessionStorage){const r=sessionStorage.getItem(s)||"{}";sessionStorage.setItem(s,JSON.stringify(i));const l=JSON.parse(r);if(!(l.id===i.id&&l.seed===i.seed&&l.name===i.name&&l.updateTime===i.updateTime)){window.location.reload();return}}let n=self.SCOPE_CHEATER;n&&n.run()},world:"MAIN",injectImmediately:!0}))}async function Pi(t,e){e?(await ee.action.setTitle({title:e.name,tabId:t}),await ee.action.setBadgeBackgroundColor({color:"#84fc7a",tabId:t}),await ee.action.setBadgeText({text:"ON",tabId:t})):(await ee.action.setTitle({title:"",tabId:t}),await ee.action.setBadgeBackgroundColor({color:"rgba(255,255,255,0)",tabId:t}),await ee.action.setBadgeText({text:"",tabId:t}))}async function T(t){const e=await P.get(t);return e==null||e==null?ge.defaultConfig[t]:e}async function Jt({key:t,value:e}){return t==="randomSeed"&&(Ke.seed=e),await P.set(t,e)}async function Di(t){return await P.remove(t)}async function ji(){let t=await ze(),e=await Me(),i=await Qt(),s=await ut(),n=await T("os"),r=await se("safeMode",!0),l=await se("webglSafeMode",!1),h=await se("functionTrace"),f=await se("disableWorker"),w=await se("antiDebugger"),d=await Bi("randomSeed"),y=await ft(),a=await Xt(),u=await T("fixedBrowserId"),c=await T("fixedUA"),o=await T("fixedLanguage"),p=await T("fixedTimezone");return{fixedBrowserId:u,fixedUA:c,fixedLanguage:o,fixedTimezone:p,enables:a,option:t,hostTable:e,hostEnable:i,deviceType:s,os:n,safeMode:r,webglSafeMode:l,disableWorker:f,functionTrace:h,iceServers:y,antiDebugger:w,randomSeed:d}}async function Zt(t,e){return await Ii(),await P.set("deviceType",t)}async function ut(){return await T("deviceType")||""}async function se(t,e){const i=await T(t);return i===void 0||i===""||i==null?e||!1:JSON.parse(i+"")}async function Bi(t=""){return JSON.parse(await T(t)||"0")}async function Xt(){let t=await T("enables")||{};for(let e of Ri)e in t||(t[e]=1);return t}async function ft(){let t=await T("iceServers")||[];return t.length||t.push({urls:"stun:stun.l.google.com:19302"}),t}async function Yt(t,e){return await P.set("option",t)}async function ze(){return await T("option")||"随机生成"}async function Ui({host:t,isWhite:e},i){e==null&&(e=!0);let s=await Me(),n=s[t];if(n===0||n===1)throw new Error(`域名 ${t} 在${n===1?"白":"黑"}名单中已存在`);return s[t]=e?1:0,await P.set("hostTable",s)}async function $i(t,e){let i=await Me();return delete i[t],await P.set("hostTable",i)}async function Qt(){return await T("hostEnable")||0}async function Wi(t,e){if(t=t||"",!(t in["white","black",""]))throw new Error("值错误");return await P.set("hostEnable",t)}async function Me(){return await T("hostTable")||{}}async function zi(t,e){let i=await T("proxies")||{};return i[t]=1,await P.set("proxies",i)}async function Gi(t,e){let i=await T("proxies")||{};return delete i[t],P.set("proxies",i)}async function Ki({page:t,size:e},i){let s=await T("proxies")||{},n=Object.keys(s),r=[];return Vt(n,t,e,(l,h,f)=>{r.push({proxy:l,index:f+1})}),{data:r,total:n.length}}async function qi({regex:t,browserId:e},i){let s=await T("matches")||{};if(s[t])throw new Error(t+" 已定义");return s[t]=e,P.set("matches",s)}async function Hi(t,e){let i=await T("matches")||{};return delete i[t],P.set("matches",i)}async function Fi({page:t,size:e},i){let s=await T("matches")||{},n=[],r=Object.keys(s);return Vt(r,t,e,(l,h,f)=>{n.push({regex:l,browserId:s[l],index:f+1})}),{data:n,total:r.length}}function Vt(t=[],e=1,i=5,s){if(i<=0)throw new Error("size必须大于0");let n=((e||1)-1)*i;for(let r=n;r<n+i;r++){if(r>=t.length)return;let l=t[r];s(l,r,r-n)}}async function Ji(){let t=await ze(),e=await T("hostTable")||{},i=await ut(),s=await T("os"),n=await T("matches"),r=await T("fixedBrowserId"),l=await T("fixedUA"),h=await T("fixedLanguage"),f=await T("fixedTimezone"),w=await ft(),d=[];return await ce.iterWith("",(y,a)=>{d.push(a)}),{browsers:d,matches:n,hostTable:e,deviceType:i,os:s,option:t,fixedBrowserId:r,fixedUA:l,fixedLanguage:h,fixedTimezone:f,iceServers:w}}async function Zi(t={},e){let i=t.matches||{},s=t.hostTable||{},n=t.deviceType||"",r=t.os||"",l=t.option||"无",h=t.fixedBrowserId||"",f=t.fixedUA||"",w=t.fixedLanguage||"",d=t.fixedTimezone||"",y=t.browsers||[],a=t.iceServers||[],u=await T("matches")||{},c=await T("hostTable")||{};Object.keys(i).forEach(o=>{u[o]=i[o]}),Object.keys(s).forEach(o=>{c[o]=s[o]}),await Promise.all([Zt(n),Yt(l),P.set("os",r),P.set("fixedBrowserId",h),P.set("fixedUA",f),P.set("fixedLanguage",w),P.set("fixedTimezone",d),P.set("matches",i),P.set("hostTable",s),P.set("iceServers",a)]),y.forEach(o=>(o.id||(o.id=Ht()),ce.set(o.id,o)))}var Xi=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Yi(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Qi=ei;function ei(t,e,i){t instanceof RegExp&&(t=It(t,i)),e instanceof RegExp&&(e=It(e,i));var s=ti(t,e,i);return s&&{start:s[0],end:s[1],pre:i.slice(0,s[0]),body:i.slice(s[0]+t.length,s[1]),post:i.slice(s[1]+e.length)}}function It(t,e){var i=e.match(t);return i?i[0]:null}ei.range=ti;function ti(t,e,i){var s,n,r,l,h,f=i.indexOf(t),w=i.indexOf(e,f+1),d=f;if(f>=0&&w>0){if(t===e)return[f,w];for(s=[],r=i.length;d>=0&&!h;)d==f?(s.push(d),f=i.indexOf(t,d+1)):s.length==1?h=[s.pop(),w]:(n=s.pop(),n<r&&(r=n,l=w),w=i.indexOf(e,d+1)),d=f<w&&f>=0?f:w;s.length&&(h=[r,l])}return h}var ii=Qi,Vi=is,si="\0SLASH"+Math.random()+"\0",ni="\0OPEN"+Math.random()+"\0",ht="\0CLOSE"+Math.random()+"\0",ri="\0COMMA"+Math.random()+"\0",ai="\0PERIOD"+Math.random()+"\0";function tt(t){return parseInt(t,10)==t?parseInt(t,10):t.charCodeAt(0)}function es(t){return t.split("\\\\").join(si).split("\\{").join(ni).split("\\}").join(ht).split("\\,").join(ri).split("\\.").join(ai)}function ts(t){return t.split(si).join("\\").split(ni).join("{").split(ht).join("}").split(ri).join(",").split(ai).join(".")}function oi(t){if(!t)return[""];var e=[],i=ii("{","}",t);if(!i)return t.split(",");var s=i.pre,n=i.body,r=i.post,l=s.split(",");l[l.length-1]+="{"+n+"}";var h=oi(r);return r.length&&(l[l.length-1]+=h.shift(),l.push.apply(l,h)),e.push.apply(e,l),e}function is(t){return t?(t.substr(0,2)==="{}"&&(t="\\{\\}"+t.substr(2)),Te(es(t),!0).map(ts)):[]}function ss(t){return"{"+t+"}"}function ns(t){return/^-?0\d/.test(t)}function rs(t,e){return t<=e}function as(t,e){return t>=e}function Te(t,e){var i=[],s=ii("{","}",t);if(!s)return[t];var n=s.pre,r=s.post.length?Te(s.post,!1):[""];if(/\$$/.test(s.pre))for(var l=0;l<r.length;l++){var h=n+"{"+s.body+"}"+r[l];i.push(h)}else{var f=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(s.body),w=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(s.body),d=f||w,y=s.body.indexOf(",")>=0;if(!d&&!y)return s.post.match(/,.*\}/)?(t=s.pre+"{"+s.body+ht+s.post,Te(t)):[t];var a;if(d)a=s.body.split(/\.\./);else if(a=oi(s.body),a.length===1&&(a=Te(a[0],!1).map(ss),a.length===1))return r.map(function(J){return s.pre+a[0]+J});var u;if(d){var c=tt(a[0]),o=tt(a[1]),p=Math.max(a[0].length,a[1].length),g=a.length==3?Math.abs(tt(a[2])):1,x=rs,m=o<c;m&&(g*=-1,x=as);var v=a.some(ns);u=[];for(var M=c;x(M,o);M+=g){var _;if(w)_=String.fromCharCode(M),_==="\\"&&(_="");else if(_=String(M),v){var A=p-_.length;if(A>0){var C=new Array(A+1).join("0");M<0?_="-"+C+_.slice(1):_=C+_}}u.push(_)}}else{u=[];for(var $=0;$<a.length;$++)u.push.apply(u,Te(a[$],!1))}for(var $=0;$<u.length;$++)for(var l=0;l<r.length;l++){var h=n+u[$]+r[l];(!e||d||h)&&i.push(h)}}return i}const os=Yi(Vi),ls=1024*64,Be=t=>{if(typeof t!="string")throw new TypeError("invalid pattern");if(t.length>ls)throw new TypeError("pattern is too long")},cs={"[:alnum:]":["\\p{L}\\p{Nl}\\p{Nd}",!0],"[:alpha:]":["\\p{L}\\p{Nl}",!0],"[:ascii:]":["\\x00-\\x7f",!1],"[:blank:]":["\\p{Zs}\\t",!0],"[:cntrl:]":["\\p{Cc}",!0],"[:digit:]":["\\p{Nd}",!0],"[:graph:]":["\\p{Z}\\p{C}",!0,!0],"[:lower:]":["\\p{Ll}",!0],"[:print:]":["\\p{C}",!0],"[:punct:]":["\\p{P}",!0],"[:space:]":["\\p{Z}\\t\\r\\n\\v\\f",!0],"[:upper:]":["\\p{Lu}",!0],"[:word:]":["\\p{L}\\p{Nl}\\p{Nd}\\p{Pc}",!0],"[:xdigit:]":["A-Fa-f0-9",!1]},Se=t=>t.replace(/[[\]\\-]/g,"\\$&"),us=t=>t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),Lt=t=>t.join(""),fs=(t,e)=>{const i=e;if(t.charAt(i)!=="[")throw new Error("not in a brace expression");const s=[],n=[];let r=i+1,l=!1,h=!1,f=!1,w=!1,d=i,y="";e:for(;r<t.length;){const o=t.charAt(r);if((o==="!"||o==="^")&&r===i+1){w=!0,r++;continue}if(o==="]"&&l&&!f){d=r+1;break}if(l=!0,o==="\\"&&!f){f=!0,r++;continue}if(o==="["&&!f){for(const[p,[g,x,m]]of Object.entries(cs))if(t.startsWith(p,r)){if(y)return["$.",!1,t.length-i,!0];r+=p.length,m?n.push(g):s.push(g),h=h||x;continue e}}if(f=!1,y){o>y?s.push(Se(y)+"-"+Se(o)):o===y&&s.push(Se(o)),y="",r++;continue}if(t.startsWith("-]",r+1)){s.push(Se(o+"-")),r+=2;continue}if(t.startsWith("-",r+1)){y=o,r+=2;continue}s.push(Se(o)),r++}if(d<r)return["",!1,0,!1];if(!s.length&&!n.length)return["$.",!1,t.length-i,!0];if(n.length===0&&s.length===1&&/^\\?.$/.test(s[0])&&!w){const o=s[0].length===2?s[0].slice(-1):s[0];return[us(o),!1,d-i,!1]}const a="["+(w?"^":"")+Lt(s)+"]",u="["+(w?"":"^")+Lt(n)+"]";return[s.length&&n.length?"("+a+"|"+u+")":s.length?a:u,h,d-i,!0]},ke=(t,{windowsPathsNoEscape:e=!1}={})=>e?t.replace(/\[([^\/\\])\]/g,"$1"):t.replace(/((?!\\).|^)\[([^\/\\])\]/g,"$1$2").replace(/\\([^\/])/g,"$1"),hs=new Set(["!","?","+","*","@"]),Pt=t=>hs.has(t),ds="(?!(?:^|/)\\.\\.?(?:$|/))",De="(?!\\.)",ps=new Set(["[","."]),ws=new Set(["..","."]),gs=new Set("().*{}+?[]^$\\!"),ms=t=>t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),dt="[^/]",Dt=dt+"*?",jt=dt+"+?";var B,W,ie,I,D,ae,ue,oe,V,fe,Ae,he,li,le,je,rt,ci;const G=class G{constructor(e,i,s={}){q(this,he);j(this,"type");q(this,B);q(this,W);q(this,ie,!1);q(this,I,[]);q(this,D);q(this,ae);q(this,ue);q(this,oe,!1);q(this,V);q(this,fe);q(this,Ae,!1);this.type=e,e&&L(this,W,!0),L(this,D,i),L(this,B,b(this,D)?b(b(this,D),B):this),L(this,V,b(this,B)===this?s:b(b(this,B),V)),L(this,ue,b(this,B)===this?[]:b(b(this,B),ue)),e==="!"&&!b(b(this,B),oe)&&b(this,ue).push(this),L(this,ae,b(this,D)?b(b(this,D),I).length:0)}get hasMagic(){if(b(this,W)!==void 0)return b(this,W);for(const e of b(this,I))if(typeof e!="string"&&(e.type||e.hasMagic))return L(this,W,!0);return b(this,W)}toString(){return b(this,fe)!==void 0?b(this,fe):this.type?L(this,fe,this.type+"("+b(this,I).map(e=>String(e)).join("|")+")"):L(this,fe,b(this,I).map(e=>String(e)).join(""))}push(...e){for(const i of e)if(i!==""){if(typeof i!="string"&&!(i instanceof G&&b(i,D)===this))throw new Error("invalid part: "+i);b(this,I).push(i)}}toJSON(){var i;const e=this.type===null?b(this,I).slice().map(s=>typeof s=="string"?s:s.toJSON()):[this.type,...b(this,I).map(s=>s.toJSON())];return this.isStart()&&!this.type&&e.unshift([]),this.isEnd()&&(this===b(this,B)||b(b(this,B),oe)&&((i=b(this,D))==null?void 0:i.type)==="!")&&e.push({}),e}isStart(){var i;if(b(this,B)===this)return!0;if(!((i=b(this,D))!=null&&i.isStart()))return!1;if(b(this,ae)===0)return!0;const e=b(this,D);for(let s=0;s<b(this,ae);s++){const n=b(e,I)[s];if(!(n instanceof G&&n.type==="!"))return!1}return!0}isEnd(){var i,s,n;if(b(this,B)===this||((i=b(this,D))==null?void 0:i.type)==="!")return!0;if(!((s=b(this,D))!=null&&s.isEnd()))return!1;if(!this.type)return(n=b(this,D))==null?void 0:n.isEnd();const e=b(this,D)?b(b(this,D),I).length:0;return b(this,ae)===e-1}copyIn(e){typeof e=="string"?this.push(e):this.push(e.clone(this))}clone(e){const i=new G(this.type,e);for(const s of b(this,I))i.copyIn(s);return i}static fromGlob(e,i={}){var n;const s=new G(null,void 0,i);return ne(n=G,le,je).call(n,e,s,0,i),s}toMMPattern(){if(this!==b(this,B))return b(this,B).toMMPattern();const e=this.toString(),[i,s,n,r]=this.toRegExpSource();if(!(n||b(this,W)||b(this,V).nocase&&!b(this,V).nocaseMagicOnly&&e.toUpperCase()!==e.toLowerCase()))return s;const h=(b(this,V).nocase?"i":"")+(r?"u":"");return Object.assign(new RegExp(`^${i}$`,h),{_src:i,_glob:e})}get options(){return b(this,V)}toRegExpSource(e){var f;const i=e??!!b(this,V).dot;if(b(this,B)===this&&ne(this,he,li).call(this),!this.type){const w=this.isStart()&&this.isEnd(),d=b(this,I).map(c=>{var m;const[o,p,g,x]=typeof c=="string"?ne(m=G,le,ci).call(m,c,b(this,W),w):c.toRegExpSource(e);return L(this,W,b(this,W)||g),L(this,ie,b(this,ie)||x),o}).join("");let y="";if(this.isStart()&&typeof b(this,I)[0]=="string"&&!(b(this,I).length===1&&ws.has(b(this,I)[0]))){const o=ps,p=i&&o.has(d.charAt(0))||d.startsWith("\\.")&&o.has(d.charAt(2))||d.startsWith("\\.\\.")&&o.has(d.charAt(4)),g=!i&&!e&&o.has(d.charAt(0));y=p?ds:g?De:""}let a="";return this.isEnd()&&b(b(this,B),oe)&&((f=b(this,D))==null?void 0:f.type)==="!"&&(a="(?:$|\\/)"),[y+d+a,ke(d),L(this,W,!!b(this,W)),b(this,ie)]}const s=this.type==="*"||this.type==="+",n=this.type==="!"?"(?:(?!(?:":"(?:";let r=ne(this,he,rt).call(this,i);if(this.isStart()&&this.isEnd()&&!r&&this.type!=="!"){const w=this.toString();return L(this,I,[w]),this.type=null,L(this,W,void 0),[w,ke(this.toString()),!1,!1]}let l=!s||e||i||!De?"":ne(this,he,rt).call(this,!0);l===r&&(l=""),l&&(r=`(?:${r})(?:${l})*?`);let h="";if(this.type==="!"&&b(this,Ae))h=(this.isStart()&&!i?De:"")+jt;else{const w=this.type==="!"?"))"+(this.isStart()&&!i&&!e?De:"")+Dt+")":this.type==="@"?")":this.type==="?"?")?":this.type==="+"&&l?")":this.type==="*"&&l?")?":`)${this.type}`;h=n+r+w}return[h,ke(r),L(this,W,!!b(this,W)),b(this,ie)]}};B=new WeakMap,W=new WeakMap,ie=new WeakMap,I=new WeakMap,D=new WeakMap,ae=new WeakMap,ue=new WeakMap,oe=new WeakMap,V=new WeakMap,fe=new WeakMap,Ae=new WeakMap,he=new WeakSet,li=function(){if(this!==b(this,B))throw new Error("should only call on root");if(b(this,oe))return this;this.toString(),L(this,oe,!0);let e;for(;e=b(this,ue).pop();){if(e.type!=="!")continue;let i=e,s=b(i,D);for(;s;){for(let n=b(i,ae)+1;!s.type&&n<b(s,I).length;n++)for(const r of b(e,I)){if(typeof r=="string")throw new Error("string part in extglob AST??");r.copyIn(b(s,I)[n])}i=s,s=b(i,D)}}return this},le=new WeakSet,je=function(e,i,s,n){var u,c;let r=!1,l=!1,h=-1,f=!1;if(i.type===null){let o=s,p="";for(;o<e.length;){const g=e.charAt(o++);if(r||g==="\\"){r=!r,p+=g;continue}if(l){o===h+1?(g==="^"||g==="!")&&(f=!0):g==="]"&&!(o===h+2&&f)&&(l=!1),p+=g;continue}else if(g==="["){l=!0,h=o,f=!1,p+=g;continue}if(!n.noext&&Pt(g)&&e.charAt(o)==="("){i.push(p),p="";const x=new G(g,i);o=ne(u=G,le,je).call(u,e,x,o,n),i.push(x);continue}p+=g}return i.push(p),o}let w=s+1,d=new G(null,i);const y=[];let a="";for(;w<e.length;){const o=e.charAt(w++);if(r||o==="\\"){r=!r,a+=o;continue}if(l){w===h+1?(o==="^"||o==="!")&&(f=!0):o==="]"&&!(w===h+2&&f)&&(l=!1),a+=o;continue}else if(o==="["){l=!0,h=w,f=!1,a+=o;continue}if(Pt(o)&&e.charAt(w)==="("){d.push(a),a="";const p=new G(o,d);d.push(p),w=ne(c=G,le,je).call(c,e,p,w,n);continue}if(o==="|"){d.push(a),a="",y.push(d),d=new G(null,i);continue}if(o===")")return a===""&&b(i,I).length===0&&L(i,Ae,!0),d.push(a),a="",i.push(...y,d),w;a+=o}return i.type=null,L(i,W,void 0),L(i,I,[e.substring(s-1)]),w},rt=function(e){return b(this,I).map(i=>{if(typeof i=="string")throw new Error("string type in extglob ast??");const[s,n,r,l]=i.toRegExpSource(e);return L(this,ie,b(this,ie)||l),s}).filter(i=>!(this.isStart()&&this.isEnd())||!!i).join("|")},ci=function(e,i,s=!1){let n=!1,r="",l=!1;for(let h=0;h<e.length;h++){const f=e.charAt(h);if(n){n=!1,r+=(gs.has(f)?"\\":"")+f;continue}if(f==="\\"){h===e.length-1?r+="\\\\":n=!0;continue}if(f==="["){const[w,d,y,a]=fs(e,h);if(y){r+=w,l=l||d,h+=y-1,i=i||a;continue}}if(f==="*"){s&&e==="*"?r+=jt:r+=Dt,i=!0;continue}if(f==="?"){r+=dt,i=!0;continue}r+=ms(f)}return[r,ke(e),!!i,l]},q(G,le);let Ue=G;const bs=(t,{windowsPathsNoEscape:e=!1}={})=>e?t.replace(/[?*()[\]]/g,"[$&]"):t.replace(/[?*()[\]\\]/g,"\\$&");var it={};const K=(t,e,i={})=>(Be(e),!i.nocomment&&e.charAt(0)==="#"?!1:new Ge(e,i).match(t)),vs=/^\*+([^+@!?\*\[\(]*)$/,ys=t=>e=>!e.startsWith(".")&&e.endsWith(t),xs=t=>e=>e.endsWith(t),Ss=t=>(t=t.toLowerCase(),e=>!e.startsWith(".")&&e.toLowerCase().endsWith(t)),Es=t=>(t=t.toLowerCase(),e=>e.toLowerCase().endsWith(t)),Ts=/^\*+\.\*+$/,ks=t=>!t.startsWith(".")&&t.includes("."),As=t=>t!=="."&&t!==".."&&t.includes("."),Ms=/^\.\*+$/,Os=t=>t!=="."&&t!==".."&&t.startsWith("."),Cs=/^\*+$/,Rs=t=>t.length!==0&&!t.startsWith("."),_s=t=>t.length!==0&&t!=="."&&t!=="..",Ns=/^\?+([^+@!?\*\[\(]*)?$/,Is=([t,e=""])=>{const i=ui([t]);return e?(e=e.toLowerCase(),s=>i(s)&&s.toLowerCase().endsWith(e)):i},Ls=([t,e=""])=>{const i=fi([t]);return e?(e=e.toLowerCase(),s=>i(s)&&s.toLowerCase().endsWith(e)):i},Ps=([t,e=""])=>{const i=fi([t]);return e?s=>i(s)&&s.endsWith(e):i},Ds=([t,e=""])=>{const i=ui([t]);return e?s=>i(s)&&s.endsWith(e):i},ui=([t])=>{const e=t.length;return i=>i.length===e&&!i.startsWith(".")},fi=([t])=>{const e=t.length;return i=>i.length===e&&i!=="."&&i!==".."},hi=typeof process=="object"&&process?typeof it=="object"&&it&&it.__MINIMATCH_TESTING_PLATFORM__||process.platform:"posix",Bt={win32:{sep:"\\"},posix:{sep:"/"}},js=hi==="win32"?Bt.win32.sep:Bt.posix.sep;K.sep=js;const F=Symbol("globstar **");K.GLOBSTAR=F;const Bs="[^/]",Us=Bs+"*?",$s="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?",Ws="(?:(?!(?:\\/|^)\\.).)*?",zs=(t,e={})=>i=>K(i,t,e);K.filter=zs;const H=(t,e={})=>Object.assign({},t,e),Gs=t=>{if(!t||typeof t!="object"||!Object.keys(t).length)return K;const e=K;return Object.assign((s,n,r={})=>e(s,n,H(t,r)),{Minimatch:class extends e.Minimatch{constructor(n,r={}){super(n,H(t,r))}static defaults(n){return e.defaults(H(t,n)).Minimatch}},AST:class extends e.AST{constructor(n,r,l={}){super(n,r,H(t,l))}static fromGlob(n,r={}){return e.AST.fromGlob(n,H(t,r))}},unescape:(s,n={})=>e.unescape(s,H(t,n)),escape:(s,n={})=>e.escape(s,H(t,n)),filter:(s,n={})=>e.filter(s,H(t,n)),defaults:s=>e.defaults(H(t,s)),makeRe:(s,n={})=>e.makeRe(s,H(t,n)),braceExpand:(s,n={})=>e.braceExpand(s,H(t,n)),match:(s,n,r={})=>e.match(s,n,H(t,r)),sep:e.sep,GLOBSTAR:F})};K.defaults=Gs;const di=(t,e={})=>(Be(t),e.nobrace||!/\{(?:(?!\{).)*\}/.test(t)?[t]:os(t));K.braceExpand=di;const Ks=(t,e={})=>new Ge(t,e).makeRe();K.makeRe=Ks;const qs=(t,e,i={})=>{const s=new Ge(e,i);return t=t.filter(n=>s.match(n)),s.options.nonull&&!t.length&&t.push(e),t};K.match=qs;const Ut=/[?*]|[+@!]\(.*?\)|\[|\]/,Hs=t=>t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&");class Ge{constructor(e,i={}){j(this,"options");j(this,"set");j(this,"pattern");j(this,"windowsPathsNoEscape");j(this,"nonegate");j(this,"negate");j(this,"comment");j(this,"empty");j(this,"preserveMultipleSlashes");j(this,"partial");j(this,"globSet");j(this,"globParts");j(this,"nocase");j(this,"isWindows");j(this,"platform");j(this,"windowsNoMagicRoot");j(this,"regexp");Be(e),i=i||{},this.options=i,this.pattern=e,this.platform=i.platform||hi,this.isWindows=this.platform==="win32",this.windowsPathsNoEscape=!!i.windowsPathsNoEscape||i.allowWindowsEscape===!1,this.windowsPathsNoEscape&&(this.pattern=this.pattern.replace(/\\/g,"/")),this.preserveMultipleSlashes=!!i.preserveMultipleSlashes,this.regexp=null,this.negate=!1,this.nonegate=!!i.nonegate,this.comment=!1,this.empty=!1,this.partial=!!i.partial,this.nocase=!!this.options.nocase,this.windowsNoMagicRoot=i.windowsNoMagicRoot!==void 0?i.windowsNoMagicRoot:!!(this.isWindows&&this.nocase),this.globSet=[],this.globParts=[],this.set=[],this.make()}hasMagic(){if(this.options.magicalBraces&&this.set.length>1)return!0;for(const e of this.set)for(const i of e)if(typeof i!="string")return!0;return!1}debug(...e){}make(){const e=this.pattern,i=this.options;if(!i.nocomment&&e.charAt(0)==="#"){this.comment=!0;return}if(!e){this.empty=!0;return}this.parseNegate(),this.globSet=[...new Set(this.braceExpand())],i.debug&&(this.debug=(...r)=>console.error(...r)),this.debug(this.pattern,this.globSet);const s=this.globSet.map(r=>this.slashSplit(r));this.globParts=this.preprocess(s),this.debug(this.pattern,this.globParts);let n=this.globParts.map((r,l,h)=>{if(this.isWindows&&this.windowsNoMagicRoot){const f=r[0]===""&&r[1]===""&&(r[2]==="?"||!Ut.test(r[2]))&&!Ut.test(r[3]),w=/^[a-z]:/i.test(r[0]);if(f)return[...r.slice(0,4),...r.slice(4).map(d=>this.parse(d))];if(w)return[r[0],...r.slice(1).map(d=>this.parse(d))]}return r.map(f=>this.parse(f))});if(this.debug(this.pattern,n),this.set=n.filter(r=>r.indexOf(!1)===-1),this.isWindows)for(let r=0;r<this.set.length;r++){const l=this.set[r];l[0]===""&&l[1]===""&&this.globParts[r][2]==="?"&&typeof l[3]=="string"&&/^[a-z]:$/i.test(l[3])&&(l[2]="?")}this.debug(this.pattern,this.set)}preprocess(e){if(this.options.noglobstar)for(let s=0;s<e.length;s++)for(let n=0;n<e[s].length;n++)e[s][n]==="**"&&(e[s][n]="*");const{optimizationLevel:i=1}=this.options;return i>=2?(e=this.firstPhasePreProcess(e),e=this.secondPhasePreProcess(e)):i>=1?e=this.levelOneOptimize(e):e=this.adjascentGlobstarOptimize(e),e}adjascentGlobstarOptimize(e){return e.map(i=>{let s=-1;for(;(s=i.indexOf("**",s+1))!==-1;){let n=s;for(;i[n+1]==="**";)n++;n!==s&&i.splice(s,n-s)}return i})}levelOneOptimize(e){return e.map(i=>(i=i.reduce((s,n)=>{const r=s[s.length-1];return n==="**"&&r==="**"?s:n===".."&&r&&r!==".."&&r!=="."&&r!=="**"?(s.pop(),s):(s.push(n),s)},[]),i.length===0?[""]:i))}levelTwoFileOptimize(e){Array.isArray(e)||(e=this.slashSplit(e));let i=!1;do{if(i=!1,!this.preserveMultipleSlashes){for(let n=1;n<e.length-1;n++){const r=e[n];n===1&&r===""&&e[0]===""||(r==="."||r==="")&&(i=!0,e.splice(n,1),n--)}e[0]==="."&&e.length===2&&(e[1]==="."||e[1]==="")&&(i=!0,e.pop())}let s=0;for(;(s=e.indexOf("..",s+1))!==-1;){const n=e[s-1];n&&n!=="."&&n!==".."&&n!=="**"&&(i=!0,e.splice(s-1,2),s-=2)}}while(i);return e.length===0?[""]:e}firstPhasePreProcess(e){let i=!1;do{i=!1;for(let s of e){let n=-1;for(;(n=s.indexOf("**",n+1))!==-1;){let l=n;for(;s[l+1]==="**";)l++;l>n&&s.splice(n+1,l-n);let h=s[n+1];const f=s[n+2],w=s[n+3];if(h!==".."||!f||f==="."||f===".."||!w||w==="."||w==="..")continue;i=!0,s.splice(n,1);const d=s.slice(0);d[n]="**",e.push(d),n--}if(!this.preserveMultipleSlashes){for(let l=1;l<s.length-1;l++){const h=s[l];l===1&&h===""&&s[0]===""||(h==="."||h==="")&&(i=!0,s.splice(l,1),l--)}s[0]==="."&&s.length===2&&(s[1]==="."||s[1]==="")&&(i=!0,s.pop())}let r=0;for(;(r=s.indexOf("..",r+1))!==-1;){const l=s[r-1];if(l&&l!=="."&&l!==".."&&l!=="**"){i=!0;const f=r===1&&s[r+1]==="**"?["."]:[];s.splice(r-1,2,...f),s.length===0&&s.push(""),r-=2}}}}while(i);return e}secondPhasePreProcess(e){for(let i=0;i<e.length-1;i++)for(let s=i+1;s<e.length;s++){const n=this.partsMatch(e[i],e[s],!this.preserveMultipleSlashes);if(n){e[i]=[],e[s]=n;break}}return e.filter(i=>i.length)}partsMatch(e,i,s=!1){let n=0,r=0,l=[],h="";for(;n<e.length&&r<i.length;)if(e[n]===i[r])l.push(h==="b"?i[r]:e[n]),n++,r++;else if(s&&e[n]==="**"&&i[r]===e[n+1])l.push(e[n]),n++;else if(s&&i[r]==="**"&&e[n]===i[r+1])l.push(i[r]),r++;else if(e[n]==="*"&&i[r]&&(this.options.dot||!i[r].startsWith("."))&&i[r]!=="**"){if(h==="b")return!1;h="a",l.push(e[n]),n++,r++}else if(i[r]==="*"&&e[n]&&(this.options.dot||!e[n].startsWith("."))&&e[n]!=="**"){if(h==="a")return!1;h="b",l.push(i[r]),n++,r++}else return!1;return e.length===i.length&&l}parseNegate(){if(this.nonegate)return;const e=this.pattern;let i=!1,s=0;for(let n=0;n<e.length&&e.charAt(n)==="!";n++)i=!i,s++;s&&(this.pattern=e.slice(s)),this.negate=i}matchOne(e,i,s=!1){const n=this.options;if(this.isWindows){const o=typeof e[0]=="string"&&/^[a-z]:$/i.test(e[0]),p=!o&&e[0]===""&&e[1]===""&&e[2]==="?"&&/^[a-z]:$/i.test(e[3]),g=typeof i[0]=="string"&&/^[a-z]:$/i.test(i[0]),x=!g&&i[0]===""&&i[1]===""&&i[2]==="?"&&typeof i[3]=="string"&&/^[a-z]:$/i.test(i[3]),m=p?3:o?0:void 0,v=x?3:g?0:void 0;if(typeof m=="number"&&typeof v=="number"){const[M,_]=[e[m],i[v]];M.toLowerCase()===_.toLowerCase()&&(i[v]=M,v>m?i=i.slice(v):m>v&&(e=e.slice(m)))}}const{optimizationLevel:r=1}=this.options;r>=2&&(e=this.levelTwoFileOptimize(e)),this.debug("matchOne",this,{file:e,pattern:i}),this.debug("matchOne",e.length,i.length);for(var l=0,h=0,f=e.length,w=i.length;l<f&&h<w;l++,h++){this.debug("matchOne loop");var d=i[h],y=e[l];if(this.debug(i,d,y),d===!1)return!1;if(d===F){this.debug("GLOBSTAR",[i,d,y]);var a=l,u=h+1;if(u===w){for(this.debug("** at the end");l<f;l++)if(e[l]==="."||e[l]===".."||!n.dot&&e[l].charAt(0)===".")return!1;return!0}for(;a<f;){var c=e[a];if(this.debug(`
globstar while`,e,a,i,u,c),this.matchOne(e.slice(a),i.slice(u),s))return this.debug("globstar found match!",a,f,c),!0;if(c==="."||c===".."||!n.dot&&c.charAt(0)==="."){this.debug("dot detected!",e,a,i,u);break}this.debug("globstar swallow a segment, and continue"),a++}return!!(s&&(this.debug(`
>>> no match, partial?`,e,a,i,u),a===f))}let o;if(typeof d=="string"?(o=y===d,this.debug("string match",d,y,o)):(o=d.test(y),this.debug("pattern match",d,y,o)),!o)return!1}if(l===f&&h===w)return!0;if(l===f)return s;if(h===w)return l===f-1&&e[l]==="";throw new Error("wtf?")}braceExpand(){return di(this.pattern,this.options)}parse(e){Be(e);const i=this.options;if(e==="**")return F;if(e==="")return"";let s,n=null;(s=e.match(Cs))?n=i.dot?_s:Rs:(s=e.match(vs))?n=(i.nocase?i.dot?Es:Ss:i.dot?xs:ys)(s[1]):(s=e.match(Ns))?n=(i.nocase?i.dot?Ls:Is:i.dot?Ps:Ds)(s):(s=e.match(Ts))?n=i.dot?As:ks:(s=e.match(Ms))&&(n=Os);const r=Ue.fromGlob(e,this.options).toMMPattern();return n&&typeof r=="object"&&Reflect.defineProperty(r,"test",{value:n}),r}makeRe(){if(this.regexp||this.regexp===!1)return this.regexp;const e=this.set;if(!e.length)return this.regexp=!1,this.regexp;const i=this.options,s=i.noglobstar?Us:i.dot?$s:Ws,n=new Set(i.nocase?["i"]:[]);let r=e.map(f=>{const w=f.map(d=>{if(d instanceof RegExp)for(const y of d.flags.split(""))n.add(y);return typeof d=="string"?Hs(d):d===F?F:d._src});return w.forEach((d,y)=>{const a=w[y+1],u=w[y-1];d!==F||u===F||(u===void 0?a!==void 0&&a!==F?w[y+1]="(?:\\/|"+s+"\\/)?"+a:w[y]=s:a===void 0?w[y-1]=u+"(?:\\/|"+s+")?":a!==F&&(w[y-1]=u+"(?:\\/|\\/"+s+"\\/)"+a,w[y+1]=F))}),w.filter(d=>d!==F).join("/")}).join("|");const[l,h]=e.length>1?["(?:",")"]:["",""];r="^"+l+r+h+"$",this.negate&&(r="^(?!"+r+").+$");try{this.regexp=new RegExp(r,[...n].join(""))}catch{this.regexp=!1}return this.regexp}slashSplit(e){return this.preserveMultipleSlashes?e.split("/"):this.isWindows&&/^\/\/[^\/]+/.test(e)?["",...e.split(/\/+/)]:e.split(/\/+/)}match(e,i=this.partial){if(this.debug("match",e,this.pattern),this.comment)return!1;if(this.empty)return e==="";if(e==="/"&&i)return!0;const s=this.options;this.isWindows&&(e=e.split("\\").join("/"));const n=this.slashSplit(e);this.debug(this.pattern,"split",n);const r=this.set;this.debug(this.pattern,"set",r);let l=n[n.length-1];if(!l)for(let h=n.length-2;!l&&h>=0;h--)l=n[h];for(let h=0;h<r.length;h++){const f=r[h];let w=n;if(s.matchBase&&f.length===1&&(w=[l]),this.matchOne(w,f,i))return s.flipNegate?!0:!this.negate}return s.flipNegate?!1:this.negate}static defaults(e){return K.defaults(e).Minimatch}}K.AST=Ue;K.Minimatch=Ge;K.escape=bs;K.unescape=ke;var at={exports:{}};(function(t,e){(function(i,s){var n="1.0.39",r="",l="?",h="function",f="undefined",w="object",d="string",y="major",a="model",u="name",c="type",o="vendor",p="version",g="architecture",x="console",m="mobile",v="tablet",M="smarttv",_="wearable",A="embedded",C=500,$="Amazon",J="Apple",bt="ASUS",vt="BlackBerry",me="Browser",Ce="Chrome",yi="Edge",Re="Firefox",_e="Google",yt="Huawei",Fe="LG",Je="Microsoft",xt="Motorola",be="Opera",ve="Samsung",St="Sharp",Ne="Sony",Ze="Xiaomi",Xe="Zebra",Et="Facebook",Tt="Chromium OS",kt="Mac OS",At=" Browser",xi=function(k,O){var E={};for(var N in k)O[N]&&O[N].length%2===0?E[N]=O[N].concat(k[N]):E[N]=k[N];return E},Ie=function(k){for(var O={},E=0;E<k.length;E++)O[k[E].toUpperCase()]=k[E];return O},Mt=function(k,O){return typeof k===d?de(O).indexOf(de(k))!==-1:!1},de=function(k){return k.toLowerCase()},Si=function(k){return typeof k===d?k.replace(/[^\d\.]/g,r).split(".")[0]:s},Ye=function(k,O){if(typeof k===d)return k=k.replace(/^\s\s*/,r),typeof O===f?k:k.substring(0,C)},ye=function(k,O){for(var E=0,N,te,Y,R,S,Q;E<O.length&&!S;){var Qe=O[E],Rt=O[E+1];for(N=te=0;N<Qe.length&&!S&&Qe[N];)if(S=Qe[N++].exec(k),S)for(Y=0;Y<Rt.length;Y++)Q=S[++te],R=Rt[Y],typeof R===w&&R.length>0?R.length===2?typeof R[1]==h?this[R[0]]=R[1].call(this,Q):this[R[0]]=R[1]:R.length===3?typeof R[1]===h&&!(R[1].exec&&R[1].test)?this[R[0]]=Q?R[1].call(this,Q,R[2]):s:this[R[0]]=Q?Q.replace(R[1],R[2]):s:R.length===4&&(this[R[0]]=Q?R[3].call(this,Q.replace(R[1],R[2])):s):this[R]=Q||s;E+=2}},Le=function(k,O){for(var E in O)if(typeof O[E]===w&&O[E].length>0){for(var N=0;N<O[E].length;N++)if(Mt(O[E][N],k))return E===l?s:E}else if(Mt(O[E],k))return E===l?s:E;return O.hasOwnProperty("*")?O["*"]:k},Ei={"1.0":"/8","1.2":"/1","1.3":"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"},Ot={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Ct={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[p,[u,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[p,[u,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[u,p],[/opios[\/ ]+([\w\.]+)/i],[p,[u,be+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[p,[u,be+" GX"]],[/\bopr\/([\w\.]+)/i],[p,[u,be]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[p,[u,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[u,p],[/quark(?:pc)?\/([-\w\.]+)/i],[p,[u,"Quark"]],[/\bddg\/([\w\.]+)/i],[p,[u,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[p,[u,"UC"+me]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[p,[u,"WeChat"]],[/konqueror\/([\w\.]+)/i],[p,[u,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[p,[u,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[p,[u,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[p,[u,"Smart Lenovo "+me]],[/(avast|avg)\/([\w\.]+)/i],[[u,/(.+)/,"$1 Secure "+me],p],[/\bfocus\/([\w\.]+)/i],[p,[u,Re+" Focus"]],[/\bopt\/([\w\.]+)/i],[p,[u,be+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[p,[u,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[p,[u,"Dolphin"]],[/coast\/([\w\.]+)/i],[p,[u,be+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[p,[u,"MIUI "+me]],[/fxios\/([-\w\.]+)/i],[p,[u,Re]],[/\bqihu|(qi?ho?o?|360)browser/i],[[u,"360"+At]],[/\b(qq)\/([\w\.]+)/i],[[u,/(.+)/,"$1Browser"],p],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[u,/(.+)/,"$1"+At],p],[/samsungbrowser\/([\w\.]+)/i],[p,[u,ve+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[u,/_/g," "],p],[/metasr[\/ ]?([\d\.]+)/i],[p,[u,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[u,"Sogou Mobile"],p],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[u,p],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[u],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[u,Et],p],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[u,p],[/\bgsa\/([\w\.]+) .*safari\//i],[p,[u,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[p,[u,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[p,[u,Ce+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[u,Ce+" WebView"],p],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[p,[u,"Android "+me]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[u,p],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[p,[u,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[p,u],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[u,[p,Le,Ei]],[/(webkit|khtml)\/([\w\.]+)/i],[u,p],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[u,"Netscape"],p],[/(wolvic)\/([\w\.]+)/i],[u,p],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[p,[u,Re+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[u,[p,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[u,[p,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[g,"amd64"]],[/(ia32(?=;))/i],[[g,de]],[/((?:i[346]|x)86)[;\)]/i],[[g,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[g,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[g,"armhf"]],[/windows (ce|mobile); ppc;/i],[[g,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[g,/ower/,r,de]],[/(sun4\w)[;\)]/i],[[g,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[g,de]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[a,[o,ve],[c,v]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[a,[o,ve],[c,m]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[a,[o,J],[c,m]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[a,[o,J],[c,v]],[/(macintosh);/i],[a,[o,J]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[a,[o,St],[c,m]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[a,[o,yt],[c,v]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[a,[o,yt],[c,m]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[a,/_/g," "],[o,Ze],[c,m]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[a,/_/g," "],[o,Ze],[c,v]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[a,[o,"OPPO"],[c,m]],[/\b(opd2\d{3}a?) bui/i],[a,[o,"OPPO"],[c,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[a,[o,"Vivo"],[c,m]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[a,[o,"Realme"],[c,m]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[a,[o,xt],[c,m]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[a,[o,xt],[c,v]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[a,[o,Fe],[c,v]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[a,[o,Fe],[c,m]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[a,[o,"Lenovo"],[c,v]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[a,/_/g," "],[o,"Nokia"],[c,m]],[/(pixel c)\b/i],[a,[o,_e],[c,v]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[a,[o,_e],[c,m]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[a,[o,Ne],[c,m]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[a,"Xperia Tablet"],[o,Ne],[c,v]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[a,[o,"OnePlus"],[c,m]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[a,[o,$],[c,v]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[a,/(.+)/g,"Fire Phone $1"],[o,$],[c,m]],[/(playbook);[-\w\),; ]+(rim)/i],[a,o,[c,v]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[a,[o,vt],[c,m]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[a,[o,bt],[c,v]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[a,[o,bt],[c,m]],[/(nexus 9)/i],[a,[o,"HTC"],[c,v]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[o,[a,/_/g," "],[c,m]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[a,[o,"TCL"],[c,v]],[/(itel) ((\w+))/i],[[o,de],a,[c,Le,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[a,[o,"Acer"],[c,v]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[a,[o,"Meizu"],[c,m]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[a,[o,"Ulefone"],[c,m]],[/droid.+; (a(?:015|06[35]|142p?))/i],[a,[o,"Nothing"],[c,m]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[o,a,[c,m]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[o,a,[c,v]],[/(surface duo)/i],[a,[o,Je],[c,v]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[a,[o,"Fairphone"],[c,m]],[/(u304aa)/i],[a,[o,"AT&T"],[c,m]],[/\bsie-(\w*)/i],[a,[o,"Siemens"],[c,m]],[/\b(rct\w+) b/i],[a,[o,"RCA"],[c,v]],[/\b(venue[\d ]{2,7}) b/i],[a,[o,"Dell"],[c,v]],[/\b(q(?:mv|ta)\w+) b/i],[a,[o,"Verizon"],[c,v]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[a,[o,"Barnes & Noble"],[c,v]],[/\b(tm\d{3}\w+) b/i],[a,[o,"NuVision"],[c,v]],[/\b(k88) b/i],[a,[o,"ZTE"],[c,v]],[/\b(nx\d{3}j) b/i],[a,[o,"ZTE"],[c,m]],[/\b(gen\d{3}) b.+49h/i],[a,[o,"Swiss"],[c,m]],[/\b(zur\d{3}) b/i],[a,[o,"Swiss"],[c,v]],[/\b((zeki)?tb.*\b) b/i],[a,[o,"Zeki"],[c,v]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[o,"Dragon Touch"],a,[c,v]],[/\b(ns-?\w{0,9}) b/i],[a,[o,"Insignia"],[c,v]],[/\b((nxa|next)-?\w{0,9}) b/i],[a,[o,"NextBook"],[c,v]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[o,"Voice"],a,[c,m]],[/\b(lvtel\-)?(v1[12]) b/i],[[o,"LvTel"],a,[c,m]],[/\b(ph-1) /i],[a,[o,"Essential"],[c,m]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[a,[o,"Envizen"],[c,v]],[/\b(trio[-\w\. ]+) b/i],[a,[o,"MachSpeed"],[c,v]],[/\btu_(1491) b/i],[a,[o,"Rotor"],[c,v]],[/(shield[\w ]+) b/i],[a,[o,"Nvidia"],[c,v]],[/(sprint) (\w+)/i],[o,a,[c,m]],[/(kin\.[onetw]{3})/i],[[a,/\./g," "],[o,Je],[c,m]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[a,[o,Xe],[c,v]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[a,[o,Xe],[c,m]],[/smart-tv.+(samsung)/i],[o,[c,M]],[/hbbtv.+maple;(\d+)/i],[[a,/^/,"SmartTV"],[o,ve],[c,M]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[o,Fe],[c,M]],[/(apple) ?tv/i],[o,[a,J+" TV"],[c,M]],[/crkey/i],[[a,Ce+"cast"],[o,_e],[c,M]],[/droid.+aft(\w+)( bui|\))/i],[a,[o,$],[c,M]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[a,[o,St],[c,M]],[/(bravia[\w ]+)( bui|\))/i],[a,[o,Ne],[c,M]],[/(mitv-\w{5}) bui/i],[a,[o,Ze],[c,M]],[/Hbbtv.*(technisat) (.*);/i],[o,a,[c,M]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[o,Ye],[a,Ye],[c,M]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[c,M]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[o,a,[c,x]],[/droid.+; (shield) bui/i],[a,[o,"Nvidia"],[c,x]],[/(playstation [345portablevi]+)/i],[a,[o,Ne],[c,x]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[a,[o,Je],[c,x]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[a,[o,ve],[c,_]],[/((pebble))app/i],[o,a,[c,_]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[a,[o,J],[c,_]],[/droid.+; (glass) \d/i],[a,[o,_e],[c,_]],[/droid.+; (wt63?0{2,3})\)/i],[a,[o,Xe],[c,_]],[/(quest( \d| pro)?)/i],[a,[o,Et],[c,_]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[o,[c,A]],[/(aeobc)\b/i],[a,[o,$],[c,A]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[a,[c,m]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[a,[c,v]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[c,v]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[c,m]],[/(android[-\w\. ]{0,9});.+buil/i],[a,[o,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[p,[u,yi+"HTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[p,[u,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[u,p],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[p,u]],os:[[/microsoft (windows) (vista|xp)/i],[u,p],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[u,[p,Le,Ot]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,Le,Ot],[u,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[p,/_/g,"."],[u,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[u,kt],[p,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[p,u],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[u,p],[/\(bb(10);/i],[p,[u,vt]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[p,[u,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[p,[u,Re+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[p,[u,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[p,[u,"watchOS"]],[/crkey\/([\d\.]+)/i],[p,[u,Ce+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[u,Tt],p],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[u,p],[/(sunos) ?([\w\.\d]*)/i],[[u,"Solaris"],p],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[u,p]]},Z=function(k,O){if(typeof k===w&&(O=k,k=s),!(this instanceof Z))return new Z(k,O).getResult();var E=typeof i!==f&&i.navigator?i.navigator:s,N=k||(E&&E.userAgent?E.userAgent:r),te=E&&E.userAgentData?E.userAgentData:s,Y=O?xi(Ct,O):Ct,R=E&&E.userAgent==N;return this.getBrowser=function(){var S={};return S[u]=s,S[p]=s,ye.call(S,N,Y.browser),S[y]=Si(S[p]),R&&E&&E.brave&&typeof E.brave.isBrave==h&&(S[u]="Brave"),S},this.getCPU=function(){var S={};return S[g]=s,ye.call(S,N,Y.cpu),S},this.getDevice=function(){var S={};return S[o]=s,S[a]=s,S[c]=s,ye.call(S,N,Y.device),R&&!S[c]&&te&&te.mobile&&(S[c]=m),R&&S[a]=="Macintosh"&&E&&typeof E.standalone!==f&&E.maxTouchPoints&&E.maxTouchPoints>2&&(S[a]="iPad",S[c]=v),S},this.getEngine=function(){var S={};return S[u]=s,S[p]=s,ye.call(S,N,Y.engine),S},this.getOS=function(){var S={};return S[u]=s,S[p]=s,ye.call(S,N,Y.os),R&&!S[u]&&te&&te.platform&&te.platform!="Unknown"&&(S[u]=te.platform.replace(/chrome os/i,Tt).replace(/macos/i,kt)),S},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return N},this.setUA=function(S){return N=typeof S===d&&S.length>C?Ye(S,C):S,this},this.setUA(N),this};Z.VERSION=n,Z.BROWSER=Ie([u,p,y]),Z.CPU=Ie([g]),Z.DEVICE=Ie([a,o,c,x,m,M,v,_,A]),Z.ENGINE=Z.OS=Ie([u,p]),t.exports&&(e=t.exports=Z),e.UAParser=Z;var pe=typeof i!==f&&(i.jQuery||i.Zepto);if(pe&&!pe.ua){var Pe=new Z;pe.ua=Pe.getResult(),pe.ua.get=function(){return Pe.getUA()},pe.ua.set=function(k){Pe.setUA(k);var O=Pe.getResult();for(var E in O)pe.ua[E]=O[E]}}})(typeof window=="object"?window:Xi)})(at,at.exports);var Fs=at.exports;const $t=self.chrome||self.browser;async function Wt(t=[]){if(t.length===0)return await $t.declarativeNetRequest.updateDynamicRules({removeRuleIds:[10],addRules:[]},()=>{console.log("请求头动态修改规则 清空成功",t)});if(!t.length)return;let s=[{id:10,priority:1,action:{type:"modifyHeaders",requestHeaders:t},condition:{urlFilter:"|http",resourceTypes:["main_frame","sub_frame","stylesheet","script","image","font","object","xmlhttprequest","ping","csp_report","media","websocket","other"]}}];return await $t.declarativeNetRequest.updateDynamicRules({removeRuleIds:[10],addRules:s},()=>{console.log(" 请求头动态修改规则 添加成功",t)})}const zt={Chrome:"Google",Edge:"Microsoft",IE:"Microsoft",Firefox:"Mozilla",Safari:"Apple",QQ:"Tencent"};async function pi(t){var h,f,w,d,y,a;if(!t){await Wt([]);return}let e=t.uaInfo,i=t.userAgent||"",s=t.language,n=[],r=[];r.push("Sec-Ch-Ua-Form-Factors","Sec-Ch-Ua-Full-Version","Sec-Ch-Ua-Full-Version-List"),s&&n.push({header:"Accept-Language",operation:"set",value:s});e:if(i){let m=function(v){var A,C;let M=t.uaInfo;if(!M)return v;switch((A=M.os)==null?void 0:A.name.toString().toLowerCase()){case"ios":return M.device.type==="mobile"?"iPhone":"iPad";case"windows":if(v.toLowerCase().includes("win"))return v}return((C=M.os)==null?void 0:C.name)||v};var l=m;if(i&&n.push({header:"User-Agent",operation:"set",value:i}),i.startsWith(navigator.userAgent)||!e)break e;if(i.toLowerCase().includes("firefox")){r.push("Sec-Ch-Ua-Platform","Sec-Ch-Ua-Mobile","Sec-Ch-Ua-Arch","Sec-Ch-Ua-Bitness","Sec-Ch-Ua");break e}let u="",c,o="",p,g,x;if(u=m(navigator.platform||((h=navigator.userAgentData)==null?void 0:h.platform)),c=e.device.type==="mobile",navigator&&navigator.userAgentData&&navigator.userAgentData.brands){let v=[...navigator.userAgentData.brands],M=e.browser||e.product,_=M.name||"";for(let A in zt)if(_.toUpperCase().includes(A.toUpperCase())){let C=[{brand:zt[A]+" "+_,version:M.major},{brand:"Not=A?Brand",version:"8"}];v[2]&&C.push({brand:v[2].brand,version:M.major}),v=C;break}o=v.map(A=>`"${A.brand}";v="${A.version}"`).join(", ")}if((e.cpu||e.os)&&((((f=e.os)==null?void 0:f.name.toLowerCase())||"").includes("win")?p=qt(t.seed||0,["x86","x64"]):p=(w=e.cpu)==null?void 0:w.architecture),g=(d=t.arch)!=null&&d.toString().includes("32")?"32":"64",e.os){let v=((a=(y=e.os)==null?void 0:y.version)==null?void 0:a.split("."))||[];v.length>3?v=v.slice(0,3):v.length<3&&(v.length===1?v.push("0.0"):v.length===2&&v.push("0")),x=v.join(".")}u&&n.push({header:"Sec-Ch-Ua-Platform",operation:"set",value:'"'+u+'"'}),x&&n.push({header:"Sec-Ch-Ua-Platform-Version",operation:"set",value:'"'+x+'"'}),c!==void 0&&n.push({header:"Sec-Ch-Ua-Mobile",operation:"set",value:c?"?1":"?0"}),o&&n.push({header:"Sec-Ch-Ua",operation:"set",value:o}),p&&n.push({header:"Sec-Ch-Ua-Arch",operation:"set",value:'"'+p+'"'}),g&&n.push({header:"Sec-Ch-Ua-Bitness",operation:"set",value:'"'+g+'"'})}r.forEach(u=>{n.push({header:u,operation:"remove"})}),n.length&&await Wt(n)}async function pt(t){await Ys(t),await pi(t)}async function Js(){await wt(),await pi(void 0)}const $e=navigator.language.includes("zh"),U={userAgent:navigator.userAgent,deviceType:"",osName:"",browserName:"",timezone:"",language:navigator.language,hardwareConcurrency:navigator.hardwareConcurrency,webglInfo:{VENDOR:"",RENDERER:"",VERSION:"",SHADING_LANGUAGE_VERSION:"",UNMASKED_VENDOR_WEBGL:"",UNMASKED_RENDERER_WEBGL:""}},Ke=new lt(0),Zs={id:"1",name:"默认配置（全部随机-安全模式）",seed:2,safeMode:!0,disableWorker:!1,antiDebugger:!1,userAgent:"auto",webrtc:"auto",location:{lng:-1,lat:-1},timezone:"auto",language:"auto",factors:{audio:-1,canvas:-1,fonts:-1,plugins:-1,webgl:-1,webgpu:-1,voice:-1,clientRect:-1},webglInfo:"auto",screen:{noise:-1,pixelDepth:-1,colorDepth:-1,maxTouchPoints:-1,dpr:0,scheme:"auto"},memoryCapacity:-1,processors:-1};function wi(){return JSON.parse(JSON.stringify(Zs))}function qe(t){let e=new Fs.UAParser(t).getResult(),i={};return["browser","os","device","cpu","engine"].forEach(s=>{i[s]=e[s]}),i.product=i.browser,delete i.browser,i}function Xs(){var n,r,l,h;U.userAgent=navigator.userAgent;let t=qe(U.userAgent);if(U.deviceType=(n=t.device)==null?void 0:n.type,U.browserName=(r=t.product)==null?void 0:r.name,U.osName=((l=t.os)==null?void 0:l.name)||"",!U.deviceType){let f=((h=U.osName)==null?void 0:h.toLowerCase())||"";(f.startsWith("win")||f.includes("linux")||f.includes("mac"))&&(U.deviceType="desktop"),(f.includes("ios")||f.includes("android"))&&(U.deviceType="mobile")}U.timezone=new Intl.DateTimeFormat().resolvedOptions().timeZone,U.webglInfo={};const i=new OffscreenCanvas(1,1).getContext("webgl");Object.keys(WebGLRenderingContext).forEach(f=>{try{const w=WebGLRenderingContext[f];if(typeof w=="number"){const d=(i==null?void 0:i.getParameter(w))||"";U.webglInfo[f]=d}}catch{}});const s=i.getExtension("WEBGL_debug_renderer_info");Object.keys(s.__proto__).forEach(f=>{U.webglInfo[f]=i.getParameter(s[f])})}async function Ys(t){return await We.set("global-0",t)}async function Oe(){return await We.get("global-0")}async function wt(){return await We.remove("global-0")}async function Qs(t,e){let i=await mt(t);if(!i)throw new Error("invalid browserId");return await pt(i),t}async function Vs(){return wt()}async function en(){let t=await Oe();return(t==null?void 0:t.id)||0}function gi(){return Ke.int(100,1e5)}async function tn({keyword:t,page:e=1,size:i=10},s){var h;e-=1;let n=[],r=await Oe();return await ce.iterWith("",(f,w)=>{let{id:d,name:y}=w;if(r&&r.id===d){r=w,n.unshift(r);return}if(t){((y||"")+"").includes(t)&&n.push(w);return}n.push(w)}),r&&!t&&r.id!==((h=n[0])==null?void 0:h.id)&&n.unshift(r),i<0?{data:n,total:n.length}:{data:n.slice(e*i,e*i+i),total:n.length}}async function sn(t,e){if(t)return await ce.remove(t)}async function mi(t){if(t)return await ce.get(t)}async function nn(t,e){let{id:i,name:s,timezone:n,language:r,webrtc:l,proxy:h,userAgent:f,factors:w,screen:d}=t;if(!s)throw new Error($e?"浏览器名称不能为空":"browser's name required");let{height:y,width:a}=d||{};if(l&&!z(l)&&!Ai(l))throw new Error($e?"webrtc ip格式不正确":"webrtc ip with invalid format");if(y&&y<0)throw new Error("屏幕的高度不能小于0");if(a&&a<0)throw new Error("屏幕的宽度不能小于0");return f&&(t.uaInfo=qe(f)),i||(t.id=Ht()),t.updateTime=Date.now(),await ce.set(t.id,t),t.id}const we={last:0,data:void 0};async function He(){var e,i;const t=async()=>{let n=await(await fetch("https://ip-scan.browserscan.net/sys/config/ip/get-visitor-ip?type=ip-api")).json();if(!n.data)throw new Error("获取当前代理ip信息失败");let{ip:r,language:l,ip_data:{timezone:h,latitude:f,longitude:w}}=n.data;return{ip:r,language:l,timezone:h,longitude:w,latitude:f}};try{let s=((e=we.data)==null?void 0:e.ip)||"";if(we.data=await t(),s!==((i=we.data)==null?void 0:i.ip)){let n=await Oe();n&&(await vi(n),await pt(n))}return we.data}catch(s){throw console.warn($e?"获取ip信息失败，请检查网络连接":"Failed to obtain IP information, please check network connection"),s}}async function gt(){return we.data||await He()}async function rn(){let t=await ze();if(t==="无"){console.debug("option is no-ops");return}if(t==="随机选取"){let e=await ce.keys();return Ke.item([...e])}if(t==="随机生成")return"randomGenerate";if(t==="静态")return T("fixedBrowserId")}const bi=async(t,e)=>{var d,y,a,u;let i,s,n,r={};z(t.language)?i=e.language||"":i=t.language||"",z(t.timezone)?s=e.timezone||"":s=t.timezone||"",z(t.webrtc)?n=e.ip||"":n=t.webrtc||"",z((d=t.location)==null?void 0:d.lat)?r.lat=e.latitude||0:r.lat=((y=t.location)==null?void 0:y.lat)||0,z((a=t.location)==null?void 0:a.lng)?r.lng=e.longitude||0:r.lng=((u=t.location)==null?void 0:u.lng)||0;let{languages:l,regions:h}=ge,f=await T("fixedLanguage");f!==void 0&&(f!==""&&f/1===f/1?i=l[f].code:f&&(i=f+""));let w=await T("fixedTimezone");return w!==void 0&&(w!==""&&w/1===w/1?s=h[w].timezone:w&&(s=w+"")),{language:i,timezone:s,webrtc:n,location:r}},an=async t=>{const e=new lt(t.seed||gi());let{languages:i,regions:s,devices:n}=ge,r=we.data||await He()||{},{language:l,timezone:h,webrtc:f,location:w}=await bi(t,r),d=t.safeMode,y=t.disableWorker,a=t.antiDebugger,u=t.deviceType||U.deviceType,c=t.userAgent||"";if(z(c))if(d)c=navigator.userAgent+" "+e.int(11111,99999);else{let A=U.osName,C=U.browserName;u!==U.deviceType&&(A="",C=""),t.os&&(A=t.os),u||(u=e.item(["desktop","tablet","mobile"])),A||(u==="desktop"?A=e.item(["macos","windows","linux"]):A=e.item(["ios","android"])),C||(C=e.item(["Chrome","Safari","Firefox","Edge"]));let $=(n[u]||{})[A]||[],J=e.item($);c=Oi({osName:A,browserName:C,deviceName:J},e)}let o=await T("fixedUA");o&&(c=o);let p=t.uaInfo||qe(c||navigator.userAgent);u=p.device.type;let g=t.screen||{};z(g.noise)&&(g.noise=e.int(0,5e3)/1e3,g.height=0,g.width=0),z(g.dpr)&&(g.dpr=e.int(100,300)/100),z(g.maxTouchPoints)&&(u==="desktop"?g.maxTouchPoints=0:u==="mobile"||u==="tablet"?g.maxTouchPoints=e.int(2,10):g.maxTouchPoints=0),z(g.colorDepth)&&(g.colorDepth=e.item([4,6,8,10,16,24])),z(g.pixelDepth)&&(g.pixelDepth=e.item([4,6,8,10,16,24])),z(g.scheme)&&(g.scheme=e.item(["no-preference","light","dark"]));const x={};if(z(t.webglInfo))if(t.webglSafeMode){x.UNMASKED_VENDOR_WEBGL=U.webglInfo.UNMASKED_VENDOR_WEBGL;const A=e.int(1,1e3);x.UNMASKED_RENDERER_WEBGL=U.webglInfo.UNMASKED_RENDERER_WEBGL.replace(/\d+(\.\d+)+/,C=>C+"."+A)}else{const A=e.item(ge.webglInfos);for(let C of Object.keys(A)){const $=A[C];if($ instanceof Array){const J=e.item($);x[C]=J}else x[C]=$}}let m={};Ft.flat().forEach(A=>{let C=t.factors||{};C[A]&&(m[A]=z(C[A])?e.int(0,5e3)/1e3:C[A]||0)});let v=await Xt();v.driver=0;let M=await ft(),_={...t,id:t.id,name:t.name,safeMode:d,disableWorker:y,antiDebugger:a,userAgent:c,enables:v,webglInfo:x,customProtos:t.customProtos||[],customVars:t.customVars||[],webrtc:f,timezone:h,language:l,location:w,factors:m,screen:g,memoryCapacity:z(t.memoryCapacity)?e.item([.25,.5,1,2,4,8]):t.memoryCapacity||0,processors:z(t.processors)?e.item([1,2,4,8,16]):t.processors||0,uaInfo:p,iceServers:M,javaEnabled:t.javaEnabled,doNotTrack:t.doNotTrack,hideFlash:t.hideFlash};return(u==="mobile"||u==="tablet")&&["ontouchstart","ontouchend","ontouchmove","ontouchcancel"].forEach(C=>{_.customVars.push({path:"HTMLElement.prototype."+C,value:null}),_.customVars.push({path:"window."+C,value:null})}),_};async function mt(t){if(!t)return null;const e=gi()|0;let i;return t==="randomGenerate"?(i=wi(),i.id=t,i.seed=e,i.name=($e?"随机生成临时配置":"Random Temp Browser")+":"+e,i.safeMode=await se("safeMode",!0),i.webglSafeMode=await se("webglSafeMode",!1),i.disableWorker=await se("disableWorker"),i.antiDebugger=await se("antiDebugger"),i.deviceType=await T("deviceType"),i.os=await T("os"),i.isTemp=!0,await an(i)):(i=await mi(t),await vi(i),i)}async function ot(){let t=await rn();if(!t)return await Js(),!1;let e=await mt(t);return e?(await pt(e),!0):!1}function z(t){return t==="auto"||t==="dynamic"||t==="-1"||t===-1}async function vi(t){let e=await gt();const i={...wi(),...t};let{language:s,timezone:n,webrtc:r,location:l}=await bi(i,e),h=t.userAgent||"",f=await T("fixedUA");f&&(h=f),t.userAgent=h,t.language=s,t.timezone=n,t.webrtc=r,t.location=l}async function Gt(t=""){let e=await Qt();if(e){let s=(await Me())[t];if(e==="white"&&s!==1)return console.debug(t+" 不在白名单",scopeKey.id),!1;if(e==="white"&&s===0)return console.debug(t+" 在黑名单",scopeKey.id),!1}return!0}const on=self.chrome||self.browser,ln={beat:()=>1,uaParse:qe,selectBrowsers:tn,refreshIpInfo:He,getIpInfo:gt,delGlobalBrowser:wt,setBrowser:nn,getBrowser:mi,delBrowser:sn,randomBrowser:ot,obtainBrowser:mt,setEnable:Qs,removeEnable:Vs,getEnable:en,clearBrowsingData:async(t,e)=>{await on.browsingData.remove({since:0,originTypes:{unprotectedWeb:!0}},{appcache:!0,history:!0,cacheStorage:!0,cookies:!0,formData:!0,indexedDB:!0,localStorage:!0,pluginData:!0,serviceWorkers:!0,webSQL:!0})},getConfig:T,setConfig:Jt,delConfig:Di,configs:ji,delHost:$i,getHostTable:Me,setHostEnable:Wi,addHost:Ui,getDevice:ut,setDevice:Zt,getOption:ze,setOption:(...t)=>Yt(...t).then(()=>ot(...t)),getProxies:Ki,delProxy:Gi,addProxy:zi,getMatches:Fi,delMatch:Hi,addMatch:qi,importData:Zi,exportData:Ji},X=self.chrome||self.browser;function cn(t,e){X.notifications.create({type:"basic",iconUrl:"/assets/logo.png",title:t,message:e||""})}(async()=>{await Ci(),await gt(),Xs(),Ke.seed=Math.random(),await He();let t=await Oe();t||(t=await ot()),cn("Injected",t.name),console.log("injected",t),setInterval(()=>{Jt({key:"beat",value:Date.now()}).then(()=>{console.log("beat...")})},1e3*15),X.windows.onRemoved.addListener(function(){});async function e(s,n,r){let l=ln[s];if(!l)throw new Error("路径不存在,key="+s);try{let h=l(n,r);return h instanceof Promise&&(h=await h),{code:200,data:h}}catch(h){return console.error(h),{code:500,msg:h.message}}}X.runtime.onConnect.addListener(s=>{s.onMessage.addListener(async n=>{var w,d;let r=((d=(w=s.sender)==null?void 0:w.tab)==null?void 0:d.id)||n.tabId||0;if(!r){let[y]=await X.tabs.query({active:!0,currentWindow:!0});r=(y==null?void 0:y.id)||0}if(!r)return s.postMessage({code:500,msg:"请先刷新页面!",requestId:n.requestId}),!0;const l=Ni(r);let h=n.key,f=n.data;l.then(async y=>{let a=await e(h,f,y);return console.log(`request==${r}==${h}`,n,a),s.postMessage({...a,requestId:n.requestId}),!0})})}),X.tabs.onActivated.addListener(function(){}),X.webNavigation.onCreatedNavigationTarget.addListener(async s=>{if(!(!s.url.toString().startsWith("http")||"frameType"in s&&s.frameType!=="outermost_frame")){if(!await Gt(new URL(s.url).host)){await Nt();return}await nt(s.tabId,s.sourceTabId)}}),X.webNavigation.onCommitted.addListener(async s=>{if(!(!s.url.toString().startsWith("http")||"frameType"in s&&s.frameType!=="outermost_frame")){if(!await Gt(new URL(s.url).host)){await Nt();return}await nt(s.tabId)}});async function i(){let n=(await X.declarativeNetRequest.getSessionRules()||[]).map(r=>r.id);await X.declarativeNetRequest.updateSessionRules({removeRuleIds:[...n],addRules:[]}),await X.scripting.unregisterContentScripts()}await i(),await X.scripting.registerContentScripts([{id:"window",js:["content-scripts/window.js"],matches:["*://*/*"],allFrames:!0,runAt:"document_start",world:"MAIN"}])})();
