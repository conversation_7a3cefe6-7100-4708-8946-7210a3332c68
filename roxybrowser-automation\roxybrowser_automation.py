import time
import json
import re
import sys
import os
import random
from pathlib import Path
from typing import Optional, Dict, Any

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

# Selenium WebDriver imports
from selenium import webdriver
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

from config import RoxyBrowserConfig
from roxybrowser_client import RoxyBrowserClient, RoxyBrowserManager

# Import shared handlers from parent directory
try:
    from handlers import CaptchaHand<PERSON>, AugmentAuth, TokenStorage, OneMailHandler
except ImportError:
    # Try importing from drissionpage-automation-firefox directory
    sys.path.append(str(Path(__file__).parent.parent / 'drissionpage-automation-firefox'))
    from handlers import CaptchaHandler, AugmentAuth, TokenStorage, OneMailHandler

class RoxyBrowserLogger:
    """RoxyBrowser 专用日志记录器"""

    def __init__(self, save_screenshots=True, save_html=True):
        self.save_screenshots = save_screenshots
        self.save_html_enabled = save_html
        self.screenshot_dir = Path(__file__).parent / 'screenshots'
        self.html_dir = Path(__file__).parent / 'html'
        self.logs_dir = Path(__file__).parent / 'logs'
        
        # 创建目录
        for dir_path in [self.screenshot_dir, self.html_dir, self.logs_dir]:
            dir_path.mkdir(exist_ok=True)
    
    def save_screenshot(self, driver, filename: str):
        """保存截图"""
        if not self.save_screenshots:
            return
        try:
            filepath = self.screenshot_dir / f"{filename}.png"
            driver.save_screenshot(str(filepath))
            print(f"📸 截图已保存: {filepath}")
        except Exception as e:
            print(f"⚠️ 截图保存失败: {str(e)}")
    
    def save_html(self, driver, filename: str):
        """保存HTML"""
        if not self.save_html_enabled:
            return
        try:
            filepath = self.html_dir / f"{filename}.html"
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(driver.page_source)
            print(f"📄 HTML已保存: {filepath}")
        except Exception as e:
            print(f"⚠️ HTML保存失败: {str(e)}")
    
    def log_flow_start(self):
        """记录流程开始"""
        print("🚀 RoxyBrowser 自动化流程开始")
        print("=" * 50)
    
    def log_flow_end(self, success: bool, duration: float):
        """记录流程结束"""
        print("=" * 50)
        status = "✅ 成功" if success else "❌ 失败"
        print(f"🏁 RoxyBrowser 自动化流程结束: {status}")
        print(f"⏱️ 总耗时: {duration:.2f}秒")


class RoxyBrowserAutomation:
    """
    RoxyBrowser 自动化类
    使用 RoxyBrowser API + Selenium WebDriver 进行自动化操作
    """
    
    def __init__(self):
        self.config = RoxyBrowserConfig()
        self.logger = RoxyBrowserLogger(
            save_screenshots=self.config.save_screenshots,
            save_html=self.config.save_html
        )
        
        # RoxyBrowser API 客户端
        self.roxy_client = RoxyBrowserClient(
            api_host=self.config.api_host,
            api_token=self.config.api_token
        )
        
        # RoxyBrowser 管理器
        self.roxy_manager = RoxyBrowserManager(
            client=self.roxy_client,
            workspace_id=self.config.workspace_id
        )
        
        # Selenium WebDriver
        self.driver = None
        self.temp_email = None
        
        # 验证 API 连接
        self._verify_api_connection()
    
    def _verify_api_connection(self):
        """验证 API 连接"""
        print("🔍 验证 RoxyBrowser API 连接...")
        if not self.roxy_client.health_check():
            raise Exception("RoxyBrowser API 连接失败，请检查配置")
        print("✅ RoxyBrowser API 连接正常")
    
    def _create_webdriver(self, connection_info: Dict) -> webdriver.Chrome:
        """创建 WebDriver 实例"""
        print("🔧 创建 WebDriver 连接...")
        
        # 解析连接信息
        debug_port = connection_info['http'].split(':')[1]
        
        # 配置 Chrome 选项
        chrome_options = ChromeOptions()
        chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{debug_port}")
        
        # 创建 WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        
        print(f"✅ WebDriver 连接成功 - 调试端口: {debug_port}")
        return driver
    
    def start_browser(self) -> bool:
        """启动浏览器"""
        try:
            print("🚀 启动 RoxyBrowser...")
            
            # 打印配置信息
            self.config.print_config()
            print("")
            
            # 获取窗口配置
            window_config = self.config.get_window_config()
            
            # 创建并打开浏览器窗口
            window_info = self.roxy_manager.create_and_open_browser(window_config)

            # 应用随机指纹（每次都执行）
            self._apply_random_fingerprint(window_info)

            # 等待浏览器启动
            time.sleep(3)
            
            # 创建 WebDriver 连接
            connection_info = window_info['connection_info']
            self.driver = self._create_webdriver(connection_info)
            
            # 设置超时
            self.driver.implicitly_wait(10)
            
            print("✅ RoxyBrowser 启动成功")
            return True
            
        except Exception as e:
            print(f"❌ RoxyBrowser 启动失败: {str(e)}")
            self.cleanup()
            return False

    def _apply_random_fingerprint(self, window_info: Dict):
        """应用随机指纹到当前窗口"""
        try:
            print("🎲 正在应用随机指纹...")
            print(f"   🆔 窗口ID: {window_info['dir_id']}")

            # 调用随机指纹API
            result = self.roxy_client.random_fingerprint(
                workspace_id=self.config.workspace_id,
                dir_id=window_info['dir_id']
            )

            if result.get('code') == 0:
                print("✅ 随机指纹应用成功！")
                print("   🛡️ 浏览器指纹已随机化")
            else:
                print(f"⚠️ 随机指纹应用失败: {result.get('msg', '未知错误')}")

        except Exception as e:
            print(f"❌ 随机指纹应用异常: {str(e)}")
            # 不抛出异常，因为这不是致命错误
    
    def navigate_to_url(self, url: str) -> bool:
        """导航到指定URL"""
        try:
            print(f"🌐 导航到: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 保存截图和HTML
            self.logger.save_screenshot(self.driver, "01_page_loaded")
            self.logger.save_html(self.driver, "01_page_loaded")
            
            print("✅ 页面加载完成")
            return True
            
        except Exception as e:
            print(f"❌ 页面导航失败: {str(e)}")
            return False
    
    def input_email(self, email: str) -> bool:
        """输入邮箱地址（使用与Firefox版本相同的策略）"""
        try:
            print(f"📧 输入邮箱: {email}")

            # 保存输入前状态
            self.logger.save_screenshot(self.driver, "before_email_input")

            # 使用与Firefox版本完全相同的选择器列表
            email_input_selectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[name="username"]',
                'input[id="username"]',
                'input[inputmode="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="Email"]',
                'input[id*="email"]',
                'input[class*="email"]'
            ]

            # 首先尝试最可能的选择器
            primary_selectors = [
                'input[name="username"]',
                'input[id="username"]',
                '#username',
                'input[inputmode="email"]'
            ]

            email_input = None

            # 尝试主要选择器
            for selector in primary_selectors:
                try:
                    print(f"   🔍 尝试选择器: {selector}")
                    email_input = WebDriverWait(self.driver, 3).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if email_input:
                        print(f"   ✅ 找到邮箱输入框: {selector}")
                        break
                except TimeoutException:
                    print(f"   ⏰ 选择器 {selector} 超时")
                    continue
                except Exception as e:
                    print(f"   ❌ 选择器 {selector} 失败: {str(e)}")
                    continue

            # 如果还没找到，尝试所有选择器
            if not email_input:
                print("   🔍 尝试所有邮箱输入框选择器...")
                for selector in email_input_selectors:
                    try:
                        email_input = WebDriverWait(self.driver, 2).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                        if email_input:
                            print(f"   ✅ 找到邮箱输入框: {selector}")
                            break
                    except:
                        continue

            # 最后尝试：使用JavaScript验证并直接获取
            if not email_input:
                print("   🔍 使用JavaScript直接查找输入框...")
                self.logger.save_screenshot(self.driver, "before_js_search")

                js_result = self.driver.execute_script("""
                    const element = document.querySelector('input[name="username"]') ||
                                   document.querySelector('input[id="username"]') ||
                                   document.querySelector('#username');

                    if (element) {
                        return {
                            found: true,
                            tagName: element.tagName,
                            type: element.type,
                            name: element.name,
                            id: element.id,
                            className: element.className,
                            value: element.value
                        };
                    }

                    return { found: false };
                """)

                print(f"   📋 JavaScript查找结果: {js_result}")

                if js_result and js_result.get('found'):
                    # 如果JavaScript找到了，再次尝试获取
                    try:
                        email_input = WebDriverWait(self.driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, '#username'))
                        )
                        print("   ✅ 通过JavaScript验证后成功获取元素")
                    except:
                        try:
                            email_input = WebDriverWait(self.driver, 5).until(
                                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="username"]'))
                            )
                            print("   ✅ 通过name属性成功获取元素")
                        except:
                            pass

            if not email_input:
                self.logger.save_screenshot(self.driver, "email_input_not_found")
                raise Exception('未找到邮箱输入框')

            print(f"✅ 找到邮箱输入框")
            self.logger.save_screenshot(self.driver, "email_input_found")

            # 滚动到元素并聚焦
            self.driver.execute_script("arguments[0].scrollIntoView(true);", email_input)
            self.logger.save_screenshot(self.driver, "after_scroll")

            email_input.click()
            time.sleep(0.4)
            self.logger.save_screenshot(self.driver, "after_click")

            # 清空现有内容
            email_input.clear()
            time.sleep(0.2)
            self.logger.save_screenshot(self.driver, "after_clear")

            # 输入邮箱
            email_input.send_keys(email)
            time.sleep(0.3)
            self.logger.save_screenshot(self.driver, "after_input")

            # 触发事件
            self.driver.execute_script(f"""
                const input = document.querySelector('input[name="username"], input[id="username"], input[type="email"]');
                if (input) {{
                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    input.blur();
                }}
            """)
            time.sleep(0.3)
            self.logger.save_screenshot(self.driver, "after_events")

            # 验证输入是否成功
            written_value = email_input.get_attribute('value')
            if not written_value or written_value.strip() != email:
                print(f"   ⚠️ 邮箱写入不完整，使用DOM直接设置: written=\"{written_value}\"")
                # 回退到 DOM 直接设置
                self.driver.execute_script(f"""
                    const input = document.querySelector('input[name="username"], input[id="username"], input[type="email"]');
                    if (input) {{
                        input.value = '{email}';
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        input.blur();
                    }}
                """)
                time.sleep(0.3)

            # 保存最终截图
            self.logger.save_screenshot(self.driver, "email_entered")
            self.logger.save_html(self.driver, "email_entered")

            print("✅ 邮箱输入完成")
            return True

        except Exception as e:
            print(f"❌ 邮箱输入失败: {str(e)}")
            self.logger.save_screenshot(self.driver, "email_input_error")
            return False
    
    def handle_captcha(self, captcha_handler: CaptchaHandler) -> bool:
        """处理验证码（使用与Firefox版本相同的策略）"""
        try:
            print("🔍 检查是否存在验证码...")

            # 等待页面稳定
            time.sleep(2)
            self.logger.save_screenshot(self.driver, "before_captcha_check")

            # 检查多种验证码类型（与Firefox版本相同）
            captcha_selectors = [
                'div[data-captcha-sitekey]',           # Auth0 v2 / Turnstile
                'iframe[src*="recaptcha"]',            # reCAPTCHA
                'iframe[src*="hcaptcha"]',             # hCaptcha
                '.cf-turnstile',                       # Cloudflare Turnstile
                '[id*="turnstile"]',                   # Turnstile ID
                '[class*="turnstile"]',                # Turnstile class
                '.g-recaptcha',                        # Google reCAPTCHA
                '[id*="recaptcha"]',                   # reCAPTCHA ID
                '[class*="captcha"]',                  # 通用验证码
                '[id*="captcha"]'                      # 验证码 ID
            ]

            captcha_found = False
            captcha_type = ''

            for selector in captcha_selectors:
                try:
                    captcha_element = WebDriverWait(self.driver, 1).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if captcha_element:
                        captcha_found = True
                        captcha_type = selector
                        print(f"🤖 检测到验证码: {selector}")
                        break
                except:
                    continue

            if captcha_found:
                self.logger.save_screenshot(self.driver, "captcha_detected")

                # 这个初始验证码总是自动解决
                print("🚀 开始自动解决初始验证码...")

                # 获取当前页面URL和siteKey
                current_url = self.driver.current_url
                site_key = None

                # 获取 siteKey
                try:
                    site_key = self.driver.execute_script("""
                        const element = document.querySelector('div[data-captcha-sitekey]');
                        return element ? element.getAttribute('data-captcha-sitekey') : null;
                    """)
                    if site_key:
                        print(f"🔑 获取到 siteKey: {site_key}")
                except Exception as e:
                    print(f"⚠️ 获取 siteKey 失败: {e}")

                # 如果没有获取到，尝试其他方法
                if not site_key:
                    js_result = self.driver.execute_script("""
                        // 检测 Turnstile
                        const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
                        if (turnstileIframes.length > 0) {
                            const src = turnstileIframes[0].src;
                            const match = src.match(/0x4[A-Za-z0-9]{20,}/);
                            if (match) return { found: true, siteKey: match[0], source: 'iframe' };
                        }

                        const siteKeyElements = document.querySelectorAll('[data-sitekey]');
                        if (siteKeyElements.length > 0) {
                            const sitekey = siteKeyElements[0].getAttribute('data-sitekey');
                            if (sitekey && sitekey.startsWith('0x4')) {
                                return { found: true, siteKey: sitekey, source: 'data-sitekey' };
                            }
                        }

                        return { found: false };
                    """)

                    if js_result and js_result.get('found'):
                        site_key = js_result.get('siteKey')
                        print(f"🔑 通过 {js_result.get('source')} 获取到 siteKey: {site_key}")

                success = False

                # 根据验证码类型使用相应的处理方法
                if captcha_type == 'div[data-captcha-sitekey]' and site_key:
                    # Turnstile 验证码
                    print("🎯 使用 YesCaptcha 处理 Turnstile 验证码...")
                    # 使用Firefox版本的处理方法
                    success = captcha_handler.handle_turnstile_firefox(self.driver, current_url, site_key)
                else:
                    # 其他类型验证码，尝试通用处理
                    print("🎯 使用通用验证码处理...")
                    success = captcha_handler.solve_recaptcha(self.driver)

                if success:
                    self.logger.save_screenshot(self.driver, "captcha_solved")
                    print("✅ 验证码处理成功")
                    return True
                else:
                    self.logger.save_screenshot(self.driver, "captcha_failed")
                    print("❌ 验证码处理失败")
                    return False
            else:
                print("✅ 未检测到验证码")
                return True

        except Exception as e:
            print(f"❌ 验证码处理异常: {str(e)}")
            self.logger.save_screenshot(self.driver, "captcha_error")
            return False
    
    def click_continue(self) -> bool:
        """点击继续按钮（使用与Firefox版本相同的策略）"""
        try:
            print("🔘 查找并点击继续按钮...")

            # 记录点击前的URL
            before_url = self.driver.current_url

            # 关键步骤：确保 token 在表单中并同步到 Auth0 字段
            self.ensure_turnstile_token_in_form()
            self.sync_captcha_hidden_input()

            # 在点击前确保按钮可用和表单验证
            self.driver.execute_script("""
                const btn = document.querySelector('button[type="submit"], button[name="action"], input[type="submit"]');
                const form = document.querySelector('form[data-form-primary="true"]') || document.querySelector('form');
                if (btn) {
                    btn.removeAttribute('disabled');
                    btn.removeAttribute('aria-disabled');
                }
                if (form) {
                    // 触发表单验证器
                    const email = form.querySelector('input[name="username"], input[type="email"]');
                    if (email) {
                        email.dispatchEvent(new Event('input', { bubbles: true }));
                        email.dispatchEvent(new Event('change', { bubbles: true }));
                        email.blur();
                    }
                    const captcha = form.querySelector('input[name="captcha"], input[name="cf-turnstile-response"]');
                    if (captcha && captcha.value) {
                        captcha.dispatchEvent(new Event('input', { bubbles: true }));
                        captcha.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }
            """)

            # 捕获点击前的状态
            self.logger.save_screenshot(self.driver, "before_continue_click")

            # 查找继续按钮（使用与Firefox版本相同的选择器）
            continue_selectors = [
                'button[type="submit"]',
                'button[name="action"]',
                'input[type="submit"]',
                'button:contains("Continue")',
                'button:contains("继续")',
                '.continue-btn',
                '#continue',
                '[data-testid*="continue"]'
            ]

            continue_button = None
            for selector in continue_selectors:
                try:
                    continue_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if continue_button.is_displayed() and continue_button.is_enabled():
                        print(f"   ✅ 找到继续按钮: {selector}")
                        break
                except:
                    continue

            if not continue_button:
                print("❌ 未找到继续按钮")
                return False

            # 滚动到按钮
            self.driver.execute_script("arguments[0].scrollIntoView(true);", continue_button)
            time.sleep(1)

            # 点击按钮
            continue_button.click()

            # 等待页面响应
            time.sleep(3)

            # 保存截图
            self.logger.save_screenshot(self.driver, "continue_clicked")
            self.logger.save_html(self.driver, "continue_clicked")

            print("✅ 继续按钮点击完成")
            return True

        except Exception as e:
            print(f"❌ 点击继续按钮失败: {str(e)}")
            self.logger.save_screenshot(self.driver, "continue_click_error")
            return False

    def ensure_turnstile_token_in_form(self):
        """确保 turnstile token 在主表单中"""
        try:
            self.driver.execute_script("""
                const primaryForm = document.querySelector('form[data-form-primary="true"], form[action*="/login"], form[action*="/identifier"]') || document.querySelector('form');
                if (!primaryForm) return;

                // 查找 token 输入
                const tokenInputs = Array.from(document.querySelectorAll('input[name="cf-turnstile-response"], input[name*="turnstile"], input[name*="captcha"], input[name*="token"]'));
                tokenInputs.forEach(input => {
                    if (input.form !== primaryForm) {
                        // 迁移 token 到主表单
                        const clone = input.cloneNode(true);
                        primaryForm.appendChild(clone);
                    }
                });
            """)
            print("✅ 确保 token 在主表单中")
        except Exception as e:
            print(f"⚠️ ensureTurnstileTokenInForm 出错: {str(e)}")

    def sync_captcha_hidden_input(self):
        """同步 YesCaptcha 注入的 token 到 Auth0 ULP 期望的隐藏字段"""
        try:
            status = self.driver.execute_script("""
                const tokenInput = document.querySelector('input[name="cf-turnstile-response"]');
                const captchaInput = document.querySelector('div[data-captcha-provider="auth0_v2"] input[name="captcha"], input[name="captcha"]');
                let copied = false;

                if (tokenInput && tokenInput.value) {
                    if (captchaInput) {
                        // 将 token 复制到 Auth0 的隐藏字段
                        captchaInput.value = tokenInput.value;
                        // 触发事件以通过前端验证器
                        captchaInput.dispatchEvent(new Event('input', { bubbles: true }));
                        captchaInput.dispatchEvent(new Event('change', { bubbles: true }));
                        // 去除错误/待处理样式（如果有）
                        const container = captchaInput.closest('.ulp-captcha-container') || document.querySelector('.ulp-captcha-container');
                        if (container) {
                            try { container.classList.remove('c7cf794f8'); } catch (e) {}
                        }
                        copied = true;
                    }
                }
                return {
                    copied: copied,
                    tokenLen: tokenInput?.value?.length || 0,
                    hasCaptchaInput: !!captchaInput,
                    captchaLen: captchaInput?.value?.length || 0
                };
            """)

            import json
            print(f"🔄 同步captcha隐藏字段: {json.dumps(status)}")
            self.logger.save_screenshot(self.driver, "captcha_hidden_synced")
            return status.get('copied', False)
        except Exception as e:
            print(f"⚠️ syncCaptchaHiddenInput 出错: {str(e)}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            print("🧹 开始清理资源...")
            
            # 关闭 WebDriver
            if self.driver:
                try:
                    self.driver.quit()
                    print("✅ WebDriver 已关闭")
                except:
                    pass
                finally:
                    self.driver = None
            
            # 清理 RoxyBrowser 窗口
            self.roxy_manager.close_and_delete_current_window()
            
            print("✅ 资源清理完成")
            
        except Exception as e:
            print(f"⚠️ 清理资源时出错: {str(e)}")
    
    def emergency_cleanup(self):
        """紧急清理所有窗口"""
        try:
            print("🚨 执行紧急清理...")
            self.roxy_manager.cleanup_all_windows()
        except Exception as e:
            print(f"⚠️ 紧急清理失败: {str(e)}")
    
    def get_current_url(self) -> str:
        """获取当前URL"""
        if self.driver:
            return self.driver.current_url
        return ""
    
    def get_page_title(self) -> str:
        """获取页面标题"""
        if self.driver:
            return self.driver.title
        return ""
