#!/usr/bin/env python3
"""
测试 RoxyBrowser 随机指纹功能
"""

import sys
import time
from pathlib import Path

# 添加 drissionpage-automation-roxy 目录到路径
sys.path.append(str(Path(__file__).parent / 'drissionpage-automation-roxy'))

from roxybrowser_client import RoxyBrowserClient, RoxyBrowserManager
from config import DrissionPageConfig

def test_random_fingerprint():
    """测试随机指纹功能"""
    try:
        print("🧪 开始测试 RoxyBrowser 随机指纹功能...")
        
        # 加载配置
        config = DrissionPageConfig()
        
        # 创建客户端
        client = RoxyBrowserClient(
            api_host=config.roxybrowser_api_host,
            api_token=config.roxybrowser_api_token
        )
        
        # 创建管理器
        manager = RoxyBrowserManager(
            client=client,
            workspace_id=config.roxybrowser_workspace_id
        )
        
        print("✅ RoxyBrowser 客户端初始化成功")
        
        # 获取窗口配置
        window_config = {
            "workspaceId": config.roxybrowser_workspace_id,
            "windowName": f"Fingerprint-Test-{int(time.time())}",
            "coreVersion": config.roxybrowser_core_version,
            "os": config.roxybrowser_os,
            "osVersion": config.roxybrowser_os_version,
            "fingerInfo": {
                "randomFingerprint": True,
                "forbidSavePassword": True,
                "clearCacheFile": True,
                "clearCookie": True,
                "clearLocalStorage": True,
                "webRTC": 2,
                "webGL": True,
                "canvas": True,
                "audioContext": True,
                "doNotTrack": True,
                "clientRects": True,
                "deviceInfo": True,
                "deviceNameSwitch": True,
                "macInfo": True,
                "portScanProtect": True,
                "useGpu": True,
                "sandboxPermission": False
            }
        }
        
        print(f"🔧 创建测试窗口: {window_config['windowName']}")
        
        # 创建并打开窗口
        window_info = manager.create_and_open_browser(window_config)
        dir_id = window_info['dir_id']
        
        print(f"✅ 窗口创建成功 - DirID: {dir_id}")
        
        # 测试随机指纹API
        print("🎲 测试随机指纹API...")
        
        for i in range(3):
            print(f"\n--- 第 {i+1} 次随机指纹测试 ---")
            
            try:
                result = client.random_fingerprint(
                    workspace_id=config.roxybrowser_workspace_id,
                    dir_id=dir_id
                )
                
                if result.get('code') == 0:
                    print("✅ 随机指纹应用成功！")
                    print(f"   📊 响应消息: {result.get('msg', '已更新')}")
                    print(f"   📋 响应数据: {result.get('data', 'N/A')}")
                else:
                    print(f"⚠️ 随机指纹应用失败: {result.get('msg', '未知错误')}")
                    print(f"   📋 完整响应: {result}")
                    
            except Exception as e:
                print(f"❌ 随机指纹API调用异常: {str(e)}")
            
            # 等待2秒再进行下一次测试
            if i < 2:
                time.sleep(2)
        
        # 清理：关闭并删除窗口
        print("\n🧹 清理测试窗口...")
        manager.close_and_delete_current_window()
        
        print("✅ 随机指纹功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_random_fingerprint()
    sys.exit(0 if success else 1)
