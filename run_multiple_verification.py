#!/usr/bin/env python3
"""
Multiple DrissionPage Verification Runner
运行多次 DrissionPage 验证脚本

Usage:
    python run_multiple_verification.py [count]
    
Examples:
    python run_multiple_verification.py 5    # Run 5 times
    python run_multiple_verification.py      # Run 1 time (default)
"""

import sys
import subprocess
import time
import os
from pathlib import Path

def log_message(message):
    """打印带时间戳的日志消息"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def run_verification_script(run_number, total_runs):
    """运行单次验证脚本"""
    script_dir = Path(__file__).parent / "drissionpage-automation-roxy"
    script_path = script_dir / "run_drissionpage_verification.py"
    
    if not script_path.exists():
        log_message(f"❌ 脚本文件不存在: {script_path}")
        return False
    
    log_message(f"🚀 开始运行第 {run_number}/{total_runs} 次验证...")
    
    try:
        # 运行脚本
        result = subprocess.run(
            [sys.executable, "run_drissionpage_verification.py"],
            cwd=script_dir,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        if result.returncode == 0:
            log_message(f"✅ 第 {run_number}/{total_runs} 次运行成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            log_message(f"❌ 第 {run_number}/{total_runs} 次运行失败 (退出码: {result.returncode})")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False
            
    except subprocess.TimeoutExpired:
        log_message(f"⏰ 第 {run_number}/{total_runs} 次运行超时 (5分钟)")
        return False
    except Exception as e:
        log_message(f"💥 第 {run_number}/{total_runs} 次运行异常: {str(e)}")
        return False

def main():
    """主函数"""
    # 获取运行次数参数
    if len(sys.argv) > 1:
        try:
            run_count = int(sys.argv[1])
            if run_count <= 0:
                print("❌ 运行次数必须是正整数")
                sys.exit(1)
        except ValueError:
            print("❌ 无效的运行次数，请输入正整数")
            sys.exit(1)
    else:
        run_count = 1
    
    log_message(f"📋 准备运行 DrissionPage 验证脚本 {run_count} 次")
    
    # 检查脚本目录是否存在
    script_dir = Path(__file__).parent / "drissionpage-automation-roxy"
    if not script_dir.exists():
        log_message(f"❌ 脚本目录不存在: {script_dir}")
        sys.exit(1)
    
    # 统计变量
    success_count = 0
    failure_count = 0
    start_time = time.time()
    
    # 运行脚本
    for i in range(1, run_count + 1):
        if run_verification_script(i, run_count):
            success_count += 1
        else:
            failure_count += 1
        
        # 如果不是最后一次运行，等待3秒
        if i < run_count:
            log_message("⏳ 等待 3 秒后继续下一次运行...")
            time.sleep(3)
    
    # 计算总耗时
    total_time = time.time() - start_time
    minutes = int(total_time // 60)
    seconds = int(total_time % 60)
    
    # 输出总结
    log_message("=" * 50)
    log_message("📊 运行总结:")
    log_message(f"   总运行次数: {run_count}")
    log_message(f"   成功次数: {success_count}")
    log_message(f"   失败次数: {failure_count}")
    log_message(f"   成功率: {(success_count/run_count)*100:.1f}%")
    log_message(f"   总耗时: {minutes}分{seconds}秒")
    log_message("=" * 50)
    
    # 根据结果设置退出码
    if failure_count == 0:
        log_message("🎉 所有运行都成功完成！")
        sys.exit(0)
    elif success_count > 0:
        log_message("⚠️ 部分运行成功")
        sys.exit(1)
    else:
        log_message("💥 所有运行都失败了")
        sys.exit(2)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_message("\n🛑 用户中断运行")
        sys.exit(130)
    except Exception as e:
        log_message(f"💥 程序异常: {str(e)}")
        sys.exit(1)
