@echo off
setlocal enabledelayedexpansion

REM Multiple DrissionPage Verification Runner (Windows Batch)
REM 运行多次 DrissionPage 验证脚本

REM 设置默认运行次数
set "RUN_COUNT=1"

REM 检查命令行参数
if not "%1"=="" (
    set "RUN_COUNT=%1"
    REM 验证参数是否为数字
    echo !RUN_COUNT! | findstr /r "^[1-9][0-9]*$" >nul
    if errorlevel 1 (
        echo ❌ 无效的运行次数，请输入正整数
        exit /b 1
    )
)

echo 📋 准备运行 DrissionPage 验证脚本 !RUN_COUNT! 次
echo.

REM 检查脚本目录是否存在
if not exist "drissionpage-automation-roxy" (
    echo ❌ 脚本目录不存在: drissionpage-automation-roxy
    exit /b 1
)

if not exist "drissionpage-automation-roxy\run_drissionpage_verification.py" (
    echo ❌ 脚本文件不存在: drissionpage-automation-roxy\run_drissionpage_verification.py
    exit /b 1
)

REM 统计变量
set "SUCCESS_COUNT=0"
set "FAILURE_COUNT=0"

REM 记录开始时间
for /f "tokens=1-4 delims=:.," %%a in ("%time%") do (
    set /a "start_time=(((%%a*60)+1%%b %% 100)*60+1%%c %% 100)*100+1%%d %% 100"
)

REM 运行脚本循环
for /l %%i in (1,1,!RUN_COUNT!) do (
    echo [%date% %time%] 🚀 开始运行第 %%i/!RUN_COUNT! 次验证...
    
    REM 切换到脚本目录并运行
    cd /d "drissionpage-automation-roxy"
    python run_drissionpage_verification.py
    set "EXIT_CODE=!errorlevel!"
    cd /d "%~dp0"
    
    if !EXIT_CODE! equ 0 (
        echo [%date% %time%] ✅ 第 %%i/!RUN_COUNT! 次运行成功
        set /a "SUCCESS_COUNT+=1"
    ) else (
        echo [%date% %time%] ❌ 第 %%i/!RUN_COUNT! 次运行失败 ^(退出码: !EXIT_CODE!^)
        set /a "FAILURE_COUNT+=1"
    )
    
    REM 如果不是最后一次运行，等待3秒
    if %%i lss !RUN_COUNT! (
        echo [%date% %time%] ⏳ 等待 3 秒后继续下一次运行...
        timeout /t 3 /nobreak >nul
    )
)

REM 计算总耗时
for /f "tokens=1-4 delims=:.," %%a in ("%time%") do (
    set /a "end_time=(((%%a*60)+1%%b %% 100)*60+1%%c %% 100)*100+1%%d %% 100"
)
set /a "total_time=end_time-start_time"
if !total_time! lss 0 set /a "total_time+=24*60*60*100"
set /a "minutes=total_time/6000"
set /a "seconds=(total_time%%6000)/100"

REM 计算成功率
set /a "success_rate=(SUCCESS_COUNT*100)/RUN_COUNT"

REM 输出总结
echo.
echo ==================================================
echo [%date% %time%] 📊 运行总结:
echo    总运行次数: !RUN_COUNT!
echo    成功次数: !SUCCESS_COUNT!
echo    失败次数: !FAILURE_COUNT!
echo    成功率: !success_rate!%%
echo    总耗时: !minutes!分!seconds!秒
echo ==================================================

REM 根据结果设置退出码
if !FAILURE_COUNT! equ 0 (
    echo [%date% %time%] 🎉 所有运行都成功完成！
    exit /b 0
) else if !SUCCESS_COUNT! gtr 0 (
    echo [%date% %time%] ⚠️ 部分运行成功
    exit /b 1
) else (
    echo [%date% %time%] 💥 所有运行都失败了
    exit /b 2
)
