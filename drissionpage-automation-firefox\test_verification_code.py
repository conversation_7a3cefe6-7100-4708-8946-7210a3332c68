#!/usr/bin/env python3
"""
测试验证码获取和输入
假设已经到达验证码页面
"""

import sys
import time
import random
from pathlib import Path

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

from selenium import webdriver
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from handlers import OneMailHandler, AugmentAuth, TokenStorage

def test_verification_code_flow():
    """测试验证码流程"""
    print('🔢 测试验证码获取和输入流程')
    print('=' * 40)
    
    # 假设的测试数据
    temp_email = "<EMAIL>"  # 替换为实际的临时邮箱
    
    try:
        # 初始化处理器
        onemail_handler = OneMailHandler()
        augment_auth = AugmentAuth()
        token_storage = TokenStorage()
        
        print('📧 模拟等待邮件到达...')
        
        # 模拟等待邮件发送的时间（Auth0需要时间发送邮件）- 随机8-15秒
        wait_seconds = random.randint(8, 15)
        print(f'⏰ 等待Auth0发送邮件（{wait_seconds}秒）...')
        time.sleep(wait_seconds)

        print('🔍 开始检查邮箱中的验证码（最多等待2分钟）...')
        verification_code = onemail_handler.get_verification_code(temp_email, 2)
        
        if not verification_code:
            print('❌ 未能获取到验证码')
            return False
        
        print(f'✅ 验证码获取成功: {verification_code}')
        
        # 如果有浏览器实例，可以测试输入
        print('🔢 验证码获取测试完成')
        print(f'验证码: {verification_code}')
        
        return True
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_with_browser():
    """使用浏览器测试完整流程"""
    print('🦊 使用 Firefox 测试验证码输入')
    print('=' * 40)
    
    # 这里需要用户手动导航到验证码页面
    print('请手动执行以下步骤：')
    print('1. 运行 Firefox 自动化到验证码页面')
    print('2. 然后运行此脚本获取验证码')
    print('3. 手动输入验证码测试')
    
    # 生成临时邮箱用于测试
    onemail_handler = OneMailHandler()
    temp_email = onemail_handler.generate_email()
    
    if temp_email:
        print(f'📧 生成的测试邮箱: {temp_email}')
        print('请在 Firefox 中使用此邮箱进行测试')
        
        input('按 Enter 键开始获取验证码...')
        
        # 获取验证码
        verification_code = onemail_handler.get_verification_code(temp_email, 2)
        
        if verification_code:
            print(f'✅ 获取到验证码: {verification_code}')
            print('请在浏览器中输入此验证码')
        else:
            print('❌ 未获取到验证码')
    else:
        print('❌ 临时邮箱生成失败')

def main():
    """主函数"""
    print('选择测试模式：')
    print('1. 仅测试验证码获取')
    print('2. 生成邮箱并等待验证码')
    
    choice = input('请选择 (1/2): ')
    
    if choice == '1':
        test_verification_code_flow()
    elif choice == '2':
        test_with_browser()
    else:
        print('无效选择')

if __name__ == '__main__':
    main()
