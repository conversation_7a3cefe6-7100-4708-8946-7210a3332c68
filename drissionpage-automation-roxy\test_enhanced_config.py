#!/usr/bin/env python3
"""
测试增强版 DrissionPage 配置
验证非 headless 模式、代理设置和 Chrome 参数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import DrissionPageConfig
from drissionpage_automation import DrissionPageAutomation, find_chrome_path, find_available_port
import platform

def test_config():
    """测试配置"""
    print("🔧 测试 DrissionPage 增强配置...")

    # 创建配置
    config = DrissionPageConfig()
    config.print_config()

    # 验证关键设置
    print("\n✅ 配置验证:")
    print(f"   操作系统: {platform.system()}")
    print(f"   非 headless 模式: {'✓' if config.headfull else '✗'}")

    # 检查代理配置来源
    import os
    enhanced_proxy_set = os.getenv('DRISSON_ENHANCED_PROXY') is not None
    proxy_source = "DRISSON_ENHANCED_PROXY" if enhanced_proxy_set else "DRISSIONPAGE_PROXY (fallback)"
    print(f"   代理启用: {'✓' if config.drissionpage_proxy else '✗'} (来源: {proxy_source})")

    # Ubuntu 兼容性测试
    chrome_path = find_chrome_path()
    if chrome_path:
        print(f"   Chrome 路径: ✓ {chrome_path}")
    else:
        print("   Chrome 路径: ✗ 未找到")

    # 端口测试
    available_port = find_available_port()
    print(f"   可用调试端口: {available_port}")

    if config.drissionpage_proxy:
        proxy_config = config.get_proxy_config()
        if proxy_config:
            print(f"   代理地址: {proxy_config['host']}:{proxy_config['port']}")
            print(f"   代理用户: {proxy_config['username']}")
        else:
            print("   ⚠️ 代理配置不完整")
    else:
        print(f"   代理状态: 已禁用 (DRISSON_ENHANCED_PROXY={os.getenv('DRISSON_ENHANCED_PROXY', 'not set')})")

    return config

def test_browser_launch():
    """测试浏览器启动"""
    print("\n🚀 测试浏览器启动...")
    
    try:
        automation = DrissionPageAutomation()
        print("✅ DrissionPage 自动化实例创建成功")

        # 初始化浏览器
        print("🚀 初始化浏览器...")
        automation.init_browser()
        print("✅ 浏览器初始化成功")

        # 尝试访问一个简单页面测试
        print("🌐 测试访问 Google...")
        automation.page.get('https://www.google.com')
        
        # 检查页面标题
        title = automation.page.title
        print(f"📄 页面标题: {title}")
        
        # 检查语言设置
        lang = automation.page.run_js('return navigator.language')
        print(f"🌍 浏览器语言: {lang}")
        
        # 检查用户代理
        user_agent = automation.page.run_js('return navigator.userAgent')
        print(f"🤖 用户代理: {user_agent[:100]}...")
        
        print("✅ 浏览器测试完成")
        
        # 保持浏览器打开一段时间以便观察
        input("\n按 Enter 键关闭浏览器...")
        
        automation.page.quit()
        
    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 DrissionPage 增强配置测试")
    print("=" * 50)
    
    # 测试配置
    config = test_config()
    
    # 询问是否测试浏览器启动
    response = input("\n是否测试浏览器启动? (y/n): ").lower().strip()
    if response in ['y', 'yes', '是']:
        test_browser_launch()
    else:
        print("跳过浏览器启动测试")
    
    print("\n🎉 测试完成!")
