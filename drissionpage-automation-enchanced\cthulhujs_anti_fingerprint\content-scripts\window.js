(()=>{"use strict";var e={};function t(e,t,n){return e=(e||0)+9,e=1e4*Math.sin(e),e|=0,Math.abs(e)%(n-t+1)+t}function n(e,n){return n[t(e,0,n.length-1)]}e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();const r=(e,t,n,r=1,i=[])=>{if(t===n)return!1;if(t<0||n<0||t+r>e.length||n+r>e.length)return!1;for(let o=0;o<r;o++){let r=e[t+o];if(i.includes(r))return!1;if(0===r)return;e[t+o]=e[n+o],e[n+o]=r}return!0};function i(e){return null==e}const o={guid:function(){function e(){return(65536*(1+Math.random())|0).toString(16).substring(1)}return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},ipv4ToIpv6:function(e){if(!e)return"";let t="",n=e.split(".");for(let e=0;e<n.length;e++){let r=parseInt(n[e]).toString(16);1===r.length&&(r="0"+r),t+=r,e%2!=0&&(t+=":")}return"0:0:0:0:0:ffff:"+t},randomNum:function(e,n){return t(e,0,n)},randomItem:n,randomInt:t,randomSwap:function(e,n,i={times:10,unit:2,loopTimes:50,skips:[]}){if(!n)return;if(n.length<=i.times*i.unit*2)return;let o=(n.length/(2*i.times)|0)/i.unit|0,a=e,s=0;for(let e=0;e<i.loopTimes;e++){let l=e*i.times*2+t(a+e,0,o)*i.unit*2;if(r(n,l,l+i.unit,i.unit,i.skips)&&s++,s>=i.times)break;a+=e}return s},randomNoise:function(e,n,r=[0]){if(!n)return 0;let i=t(e,1,5e3)/1e3,o=0;for(let e=1;e<n.length-1;e++)r.includes(n[e])||(n[e]+=i,e+=t(e*i,1,n.length/300|0),o++);return o},randomPeek:function(e,n,r,i,o=50){if(!n)return;i=i||Math.min(n.length/500|0,8)||1;let a=t(e,1,100),s=0;for(let e=0;e<n.length&&!(s>=o);)if(n[e]){if(e%a<=3){let t=r(n[e],e);void 0!==t&&(n[e]=t,s++)}e+=i}else e+=i;return s},randomReverse:function(e,n,r,i=[]){if(!n)return;let o=0,a=n.length/r|0,s=r/2|0,l=t(e,3,13);for(let c=0;c<a;c++){let a=c*r;if(a>=n.length)break;if(c%l<=1)for(let e=0;e<s;e++){let t=a+e,s=a+(r-1-e);if(i.filter((e=>e===n[t]||e===n[s])).length)continue;let l=n[t];n[t]=n[s],n[s]=l,o++}c+=t(e+c,1,10)}return o},Random:class{constructor(e=0){this.seed=e}int(e,n){let r=t(this.seed,e,n);return this.seed=r+this.seed,this.seed>99999999&&(this.seed/=2),r}item(e=[]){let t=n(this.seed,e);return this.seed=this.seed+e.length,this.seed>99999999&&(this.seed/=2),t}},hashcode:function(e){let t,n,r=0;if(!e||0===e.length)return r;for(t=0;t<e.length;t++)n=e.charCodeAt(t),r=(r<<5)-r+n,r|=0;return r},arrayHashcode:function(e){let t,n,r=0;if(!e||0===e.length)return r;for(t=0;t<e.length;t++)n=e[t],r=(r<<5)-r+n,r|=0;return r},getStyle:function(e,t){return window.getComputedStyle?getComputedStyle(e,null)[t]:e.currentStyle[t]},formatString:function(e,t){return(e+="").length>t?e.slice(0,t-3)+"...":e.padEnd(t)},getValue:function(e,t){let n=t.split(/[.\[\]]/).filter((e=>""!==e)),r=e;for(let e of n){if(void 0===r[e])return;r=r[e]}return r},deleteValue:function(e,t){let n=t.split(/[.\[\]]/).filter((e=>""!==e)),r=e;for(let e=0;e<n.length-1;e++)if(r=r[n[e]],void 0===r)return;return delete r[n[n.length-1]]},swap:r,isNullify:i,isEmpty:function(e){if(i(e))return!0;if(e&&!i(e.length))return 0===e.length;if(0===e||0===e||""===e)return!0;for(const t in e)return!1;return!0},toDataURL:(e,t)=>`'data:${t};base64,${btoa(e)}`,getGlobal:function(){try{return self}catch(e){}try{return e.g}catch(e){}try{return globalThis}catch(e){}}},a={native:"__nativeCheat__",remove:"__removeCheat__",add:"__addCheat__",window:"__windowCheat__",trace:"__traceCheat__",proxyId:"__proxyIdCheat__",isProxy:"__isProxyCheat__",origin:"__originCheat__",create:"__createCheat__",attach:"__attachCheat__",fake:"__fakeCheat__",cheaters:"__cheatersCheat__",SCOPE_BROWSER:"SCOPE_BROWSER",SCOPE_CHEATER:"SCOPE_CHEATER",BROWSER_GENERATOR:"BROWSER_GENERATOR"},s={Chrome:"Google",Edge:"Microsoft",IE:"Microsoft",Firefox:"Mozilla",Safari:"Apple",QQ:"Tencent"};class l{constructor(){this.value=""}get get(){}set set(e){}func(){}}const c=sessionStorage.getItem,u=sessionStorage.setItem,d=sessionStorage.removeItem,m=Array.prototype.push,h=Set.prototype.has,f=self.eval,p=console.log,y=self.constructor.name.replace("GlobalScope",""),g=o.getGlobal();function b(e){const t={};let n={isFirst:!0,errors:[],warns:[],debugs:t,logs:new Set,proxies:[],cheaters:[],addDebugger:(n,r)=>{let i="DEBUG:"+n;e.sessionStorage?u.apply(e.sessionStorage,[i,r]):t[i]=r},removeDebugger:n=>{let r="DEBUG:"+n;e.sessionStorage?d.apply(e.sessionStorage,[r]):delete t[r]},isDebugger:t=>{let n="DEBUG:"+t;if(!e.sessionStorage)return!1;let r=c.apply(e.sessionStorage,[n]);if(!r)return!1;let i=Number(r)||0;return i>0&&(u.apply(e.sessionStorage,[n,i-1+""]),!0)}};return e[a.attach]=n,n}const S=g.Object;g.Date;let w=0;function k(e,t,n,r){h.apply(g[a.attach].logs,[e])&&p.apply(console,[t,r]),g[a.attach].isDebugger(e)&&f("debugger");try{return n()}catch(e){throw m.apply(g[a.attach].errors,[e]),e}}function v(e,t,n,r={skipAbsent:!1}){let i=S.getOwnPropertyDescriptor(e,t);const o=("function"==typeof e?e.name:e.constructor?.name)||"<unkonw>",s=g[a.attach];if(!i){if(r.skipAbsent)return;s.warns.push(`${y}: ${t} descriptor not in ${o}`),i=S.getOwnPropertyDescriptor(l.prototype,"get")}const c=`!!!${y}: 浏览器正在获取 ${o}.${t}`,u=`${o}.${t}`;let d=i.get;return s.isFirst&&s.proxies.push(`Getter:${u}`),i.get=S.getOwnPropertyDescriptor(class{get fn(){let e=this,t=arguments;return k(u,c,(()=>n(d,e,t)),t)}}.prototype,"fn").get,S.defineProperty(i.get,"name",{value:"get "+t}),i.get[a.origin]=d,i.get[a.proxyId]=t+"_"+w++,i.get[a.native]=2,S.defineProperty(e,t,{...i}),i.get}function E(e,t,n,r={skipAbsent:!1}){let i=S.getOwnPropertyDescriptor(e,t);const o=("function"==typeof e?e.name:e.constructor?.name)||"<unkonw>",s=g[a.attach];if(!i){if(r.skipAbsent)return void s.warns.push(`${y}: ${t} descriptor not in ${o}`);i=S.getOwnPropertyDescriptor(l.prototype,"set")}const c=`!!!${y}: 浏览器正在设置 ${o}.${t}`,u=`${o}.${t}`;let d=i.set;return s.isFirst&&s.proxies.push(`Setter:${u}`),i.set=S.getOwnPropertyDescriptor(class{set fn(e){let t=this,r=arguments;return k(u,c,(()=>n(d,t,r)),r)}}.prototype,"fn").set,S.defineProperty(i.set,"name",{value:"set "+t}),i.set[a.proxyId]=t+"_"+w++,i.set[a.native]=3,S.defineProperty(e,t,{...i}),i.set}function C(e,t,n,r={skipAbsent:!1}){let i=S.getOwnPropertyDescriptor(e,t);const o=("function"==typeof e?e.name:e.constructor?.name)||"<unkonw>",s=g[a.attach];if(!i){if(r.skipAbsent)return void s.warns.push(`${y}: ${t} descriptor not in ${o}`);i=S.getOwnPropertyDescriptor(l.prototype,"func")}const c=`!!!${y}: 浏览器正在调用 ${o}.${t}`,u=`${o}.${t}`;let d=i.value;return s.isFirst&&s.proxies.push(`Function:${u}`),i.value=S.getOwnPropertyDescriptor(class{fn(){let e=this,t=arguments;return k(u,c,(()=>n(d,e,t)),t)}}.prototype,"fn").value,S.defineProperty(i.value,"name",{value:t}),i.value.length!==d.length&&S.defineProperty(i.value,"length",{value:d.length}),i.value[a.proxyId]=t+"_"+w++,i.value[a.native]=1,i.value[a.origin]=d,S.defineProperty(e,t,{...i,writable:!0}),i.value}function T(e,t,n,r={skipAbsent:!1}){let i=e[t];const o=("function"==typeof e?e.name:e.constructor?.name)||"<unkonw>",s=g[a.attach];if(!i){if(r.skipAbsent)return void s.warns.push(`${y}: ${t} descriptor not in ${o}`);i=l}const c=`!!!${y}: 浏览器正在构建 ${o}.${t}`,u=`${o}.${t}`;function d(e,t,r){return k(u,c,(()=>n(e,t,r)),t)}s.isFirst&&s.proxies.push(`Construct:${u}`),e[t]=new Proxy(i,{get:(e,t,n)=>"constructor"===t||t===a.origin?i:Reflect.get(e,t,n),construct:(e,t,n)=>d(e,t,n)}),C(i,"constructor",d),e[t][a.proxyId]=t+"_"+w++,e[t][a.native]=1}function O(e,t,n){try{if(e[t]===n)return;if(e[t]=n,e[t]===n)return}catch(r){return void v(S.getPrototypeOf(e),t,(()=>n))}let r=S.getOwnPropertyDescriptor(e,t);if(r||(r=S.getOwnPropertyDescriptor(new l,"value")),r.value)return r.value=n,void S.defineProperty(e,t,{...r,writable:!0});v(e,t,(()=>n))}function D(e,t){const n=self[a.SCOPE_CHEATER],r=e.HTMLIFrameElement;if(!r)return!1;const i=e.Document,o=Object.getOwnPropertyDescriptor(r.prototype,"contentWindow").get;function s(e,r){if("IFRAME"===e.tagName){if(e.src&&e.src.toString().startsWith("javascript:")){let t=`window.${a.SCOPE_BROWSER}=window.top.${a.SCOPE_BROWSER};window.${a.SCOPE_CHEATER}=window.top.${a.SCOPE_CHEATER};window.${a.SCOPE_CHEATER}(self,window.${a.SCOPE_BROWSER},'direct js');`;e.src=`javascript:${t};${e.src.toString().substring(11)};`}let i=o.apply(e);i&&n(i,t,r)}}i&&(C(i.prototype,"getElementById",((e,t,n)=>{let r=e.call(t,...n);return r?(s(r,"getElementById"),r):r}),{skipAbsent:!0}),["getElementsByClassName","getElementsByName","getElementsByTagName","getElementsByTagNameNS"].forEach((e=>{C(i.prototype,e,((t,n,r)=>{let i=t.call(n,...r);if(!i)return i;for(let t=0;t<i.length;t++)s(i[t],e);return i}),{skipAbsent:!0})})));const l=e.Node;l&&C(l.prototype,"appendChild",((r,i,o)=>{let a=e.self.length,l=r.call(i,...o);if(o[0]instanceof c||o[0]instanceof DocumentFragment){s(o[0],"appendChild");try{let e=o[0].querySelectorAll("iframe");for(let t of e)s(t,"appendChild")}catch(e){throw e}}let u=e.self[a];return u&&e!==u&&n(u,t,"appendChild"),l}),{skipAbsent:!0});const c=e.Element;if(c&&(E(c.prototype,"innerHTML",((r,i,o)=>{let a=e.self.length,s=r.call(i,...o),l=e.self[a];return l&&n(l,t,"innerHTML"),s}),{skipAbsent:!0}),E(c.prototype,"outerHTML",((r,i,o)=>{let a=e.self.length,s=r.call(i,...o),l=e.self[a];return l&&n(l,t,"outerHTML"),s}),{skipAbsent:!0})),t.uaInfo&&t.uaInfo.product){let u=t.uaInfo.product.name.toString().toLowerCase();function d(){return 0}d[a.native]=1,u.match(/.*(edge|chrome).*/)||(e.chrome={},e.chrome&&O(e.chrome,"valueOf",d)),u.match(/.*(safari).*/)||(e.safari=void 0,e.safari&&O(e.safari,"valueOf",d))}return["contentWindow","contentDocument","src","srcdoc","name","sandbox","allowFullscreen","width","height","contentDocument","referrerPolicy","csp","allow","featurePolicy","align","scrolling","frameBorder","longDesc","marginHeight","marginWidth","loading","allowPaymentRequest","privateToken","credentialless"].forEach((e=>{v(r.prototype,e,((r,i,a)=>{let s=r.call(i,...a),l=o.apply(i,a);return l&&n(l,t,"contentWindow:"+e),s}),{skipAbsent:!1})})),T(e,"UIEvent",((e,t,n)=>{let r=Reflect.construct(e,t,n);return r?new Proxy(r,{get:(e,t,n)=>"isTrusted"===t||Reflect.get(e,t,n)}):r})),!0}const A=(Object.getOwnPropertyDescriptor(Navigator.prototype,"deviceMemory")||{}).get;function M(e,t){!function(e,t){const n=e.Navigator||e.WorkerNavigator;if(n){if(v(n.prototype,"webdriver",((e,t,n)=>(e.call(t,...n),!1)),{skipAbsent:!0}),"boolean"==typeof t.doNotTrack&&v(n.prototype,"doNotTrack",((e,n,r)=>(e.call(n,...r),t.doNotTrack?"1":"0")),{skipAbsent:!0}),"boolean"==typeof t.javaEnabled&&C(n.prototype,"javaEnabled",((e,n,r)=>(e.call(n,...r),t.javaEnabled)),{skipAbsent:!0}),t.screen?.maxTouchPoints&&v(n.prototype,"maxTouchPoints",((e,n,r)=>{let i=e.call(n,...r);return t.screen?.maxTouchPoints||i}),{skipAbsent:!0}),t.userAgent&&(v(n.prototype,"appVersion",((e,n,r)=>{let i=e.call(n,...r);return t.appVersion||i}),{skipAbsent:!0}),v(n.prototype,"userAgent",((e,n,r)=>{let i=e.call(n,...r);return t.userAgent||i}),{skipAbsent:!0})),t.uaInfo&&(v(n.prototype,"vendor",((e,n,r)=>{let i=e.call(n,...r);return t.browserVendor||i}),{skipAbsent:!0}),v(n.prototype,"platform",((e,n,r)=>{let i=e.call(n,...r);return t.getPlatform(i)||i}),{skipAbsent:!0})),t.memoryCapacity&&A){const r=A.call(e.navigator),i=t.memoryCapacity/r;if(e.navigator.deviceMemory&&e.performance.memory)for(const t in e.performance.memory){let n=e.performance.memory[t];O(e.performance.memory,t,Math.round(n*i))}v(n.prototype,"deviceMemory",((e,n,r)=>{let i=e.call(n,...r);return t.memoryCapacity||i}),{skipAbsent:!0});const o=e.performance.memory;if(o){let e=Object.getPrototypeOf(o);for(let t of["jsHeapSizeLimit","totalJSHeapSize","usedJSHeapSize"])v(e,t,((e,t,n)=>e.call(t,...n)*i|0),{skipAbsent:!0})}}t.processors&&v(n.prototype,"hardwareConcurrency",((e,n,r)=>{let i=e.call(n,...r);return t.processors||i}),{skipAbsent:!0}),t.language&&(v(n.prototype,"language",((e,n,r)=>{let i=e.call(n,...r);return t.languageCode||i}),{skipAbsent:!0}),v(n.prototype,"languages",((e,n,r)=>{let i=e.call(n,...r);return t.languages||i}),{skipAbsent:!0}))}}(e,t);const n=e.Number,r=["toFixed() digits argument must be between 0 and 100","Gecko toFixed() failed len 29","Webkit toFixed() failed len 49 between 0 and 100"];n&&t.uaInfo?.engine&&C(n.prototype,"toFixed",((e,n,i)=>{try{return e.call(n,...i)}catch(e){let n=-1;if("RangeError"===e.constructor.name&&t.uaInfo){let e=((t.uaInfo.engine||{}).name||"").toLowerCase();"blink"===e?n=0:"gecko"===e?n=1:"webkit"===e&&(n=2)}throw r[n]&&(e.message=r[n]),e}}),{skipAbsent:!1});const i=e.NavigatorUAData;return i&&t.uaInfo&&(t.uaInfo.os&&v(i.prototype,"platform",((e,n,r)=>{let i=e.call(n,...r);return t.getPlatform(i)||i})),v(i.prototype,"brands",((e,n,r)=>{let i=e.call(n,...r);return t.brands||i})),v(i.prototype,"mobile",((e,n,r)=>{let i=e.call(n,...r);return"boolean"==typeof t.mobile?t.mobile:i})),C(i.prototype,"getHighEntropyValues",((e,n,r)=>{let i=n;return e.call(n,...r).then((e=>((e={...e}).brands=i.brands,"platform"in e&&(e.platform=t.getPlatform(e.platform)||e.platform),"mobile"in e&&(e.mobile=void 0===i.mobile?e.mobile:i.mobile),"architecture"in e&&(e.architecture=t.arch||e.architecture),"bitness"in e&&(e.bitness=t.bitness||e.bitness),"uaFullVersion"in e&&(e.uaFullVersion=t.uaFullVersion||e.uaFullVersion),"fullVersionList"in e&&(e.fullVersionList=t.fullVersionList||e.fullVersionList),"platformVersion"in e&&(e.platformVersion=t.platformVersion||e.platformVersion),e)))})),C(i.prototype,"toJSON",((e,t,n)=>{let r=e.call(t,...n);return r={...r},r.mobile=t.mobile,r.brands=t.brands,r.platform=t.platform,r}))),!0}function I(e,t){null!=e&&"object"==typeof e&&function(e,t=[]){if(!(0>10)){if(e instanceof Array)for(let n of e)I(n,t);if(!e.__lock){e.__lock=1;for(let n of Object.keys(e)){for(let r of t){let t=typeof r.check,i=!1;if("function"===t?i=r.check(n,e[n]):"string"===t?i=n===r.check:r.check instanceof RegExp&&(i=!!n.match(r.check)),i){e[n]=r.handle(n,e[n]);break}}I(e[n],t)}delete e.__lock}}}(e,t)}function x(e,t){const n=e.EventTarget,r=e.MessageEvent;let i=function(e){let t=[];e.timezone&&(t.push({check:(e,t)=>"timezoneOffset"===e&&"number"==typeof t,handle:(t,n)=>e.timezoneOffset||n}),t.push({check:(e,t)=>{if(!e.includes("time")||"string"!=typeof t||!t)return!1;let n=["Africa","America","Antarctica","Asia","Atlantic","Australia","Europe","Indian","Pacific"];for(let e of n)if(t.startsWith(e+"/"))return!0;return!1},handle:(t,n)=>e.timezone||n})),e.memoryCapacity&&A&&t.push({check:(e,t)=>"deviceMemory"===e&&"number"==typeof t,handle:(t,n)=>e.memoryCapacity||n}),e.processors&&t.push({check:(e,t)=>"hardwareConcurrency"===e&&"number"==typeof t,handle:(t,n)=>e.processors||n}),e.language&&(t.push({check:"language",handle:(t,n)=>e.languageCode||n}),t.push({check:"languages",handle:(t,n)=>e.languages||n}));return e.gpu?.vendor&&t.push({check:(e,t)=>e.toString().match(/.*((webgl|unmasked)(R|_r)(enderer)).*/)&&"string"==typeof t&&t,handle:(t,n)=>e.gpu?.renderer||n}),e.gpu?.renderer&&t.push({check:(e,t)=>e.toString().match(/.*((webgl|unmasked)(V|_v)(endor|ender)).*/)&&"string"==typeof t&&t,handle:(t,n)=>e.gpu?.vendor||n}),e.userAgent&&(t.push({check:(e,t)=>!(!e.toString().match(/.*(((user)(A|_a)(gent))|ua|Ua|UA).*/)||"string"!=typeof t||!t)&&t.startsWith("Mozilla"),handle:(t,n)=>e.userAgent||n}),t.push({check:"appVersion",handle:(t,n)=>e.appVersion||n})),t.push({check:"platform",handle:(t,n)=>e.getPlatform(n)||n}),e.uaInfo&&(t.push({check:(e,t)=>e.toString().includes("vendor")&&"string"==typeof t&&t.endsWith("Inc."),handle:(t,n)=>e.browserVendor||n}),t.push({check:/brands|brand_list|brandList/,handle:(t,n)=>e.brands||n}),t.push({check:"brandsVersion",handle:(t,n)=>e.brandsVersion||n}),t.push({check:(e,t)=>e.toLowerCase().includes("mobile")&&"boolean"==typeof t,handle:(t,n)=>void 0===e.mobile?n:e.mobile}),t.push({check:/arch(itecture)*/,handle:(t,n)=>e.arch||n}),t.push({check:(e,t)=>e.toLowerCase().includes("bit")&&"number"==typeof t&&[16,32,64].some((e=>e==t)),handle:(t,n)=>e.bitness||n}),t.push({check:"uaFullVersion",handle:(t,n)=>e.uaFullVersion||n}),t.push({check:"platformVersion",handle:(t,n)=>e.platformVersion||n}),t.push({check:"fullVersionList",handle:(t,n)=>e.fullVersionList||n})),t}(t);if(!i.length)return!1;function o(t){return e.Worker&&t instanceof e.Worker||e.SharedWorker&&t instanceof e.SharedWorker||e.MessagePort&&t instanceof e.MessagePort||e.ServiceWorkerContainer&&t instanceof e.ServiceWorkerContainer}return C(n.prototype,"addEventListener",((e,n,r)=>o(n)&&t.disableWorker?void 0:e.call(n,...r))),v(r.prototype,"data",((e,t,n)=>{let r=t[a.fake]||e.call(t,...n);return o(t.currentTarget)?(I(r,i),t[a.fake]=r,r):r})),!0}function N(e="",t){switch(e){case"get":return`function ${t.startsWith("get ")?t:"get "+t}() { [native code] }`;case"set":return`function ${t.startsWith("set ")?t:"set "+t}() { [native code] }`;default:return`function ${t}() { [native code] }`}}let F=Object.values(a);function V(e){const t=e.Object,n=e.Function,r=e.Reflect;function i(t,n,r){let i=t.call(n,...r);if(!i||!n)return i;try{let e=r[0][a.add]||(Object.getPrototypeOf(r[0])||{}).constructor[a.add]||[],t=[...r[0][a.remove]||(Object.getPrototypeOf(r[0])||{}).constructor[a.remove]||[],...F];return i=i.filter((e=>!t.includes(e))),e.forEach((e=>i.push(e))),[...i]}catch(t){return e[a.attach].errors.push(t),i}}return C(t.prototype,"hasOwnProperty",((e,t,n)=>{let r=e.call(t,...n);return"function"!=typeof n[0]?r:n[0][a.native]?!["arguments","caller","toString","prototype"].includes(n[1])&&r:r}),{skipAbsent:!1}),t.hasOwnProperty=t.prototype.hasOwnProperty,C(n.prototype,"toString",(function(e,t,n){let r=typeof t;if("function"!==r&&"object"!==r||!t)return e.call(t,...n);let i=t[a.native],o=t[a.create];try{if(!i||o)return e.call(t,...n)}catch(e){throw e instanceof TypeError&&t[a.proxyId]&&(t[a.isProxy]?e.stack="TypeError: Function.prototype.toString requires that 'this' be a Function\n    at Object.toString (<anonymous>)\n    at <anonymous>:1:34":e.stack="TypeError: Function.prototype.toString requires that 'this' be a Function\n    at Function.toString (<anonymous>)\n    at <anonymous>:1:55"),e}return N(1===i?"function":2===i?"get":"set",t.name)}),{skipAbsent:!1}),C(t,"create",((e,t,n)=>{if(n[0]&&n[0][a.proxyId]){let r=e.call(t,...n);return r[a.proxyId]=n[0][a.proxyId],r[a.isProxy]=n[0][a.isProxy],r[a.create]=1,r}return e.call(t,...n)}),{skipAbsent:!1}),C(t,"keys",i,{skipAbsent:!1}),C(t,"getOwnPropertyNames",i,{skipAbsent:!1}),C(r,"ownKeys",i,{skipAbsent:!1}),C(t,"getOwnPropertyDescriptor",((e,t,n)=>{let r=e.call(t,...n);if(!r||!t||!n[1])return r;if(r.value&&(r.value instanceof Window||r.value.top instanceof Window))try{r.value[0]}catch(e){return r}let i=n[1];return(r.value&&r.value[a.proxyId]||r.get&&r.get[a.proxyId]||r.set&&r.set[a.proxyId])&&F.includes(i)||(t[a.remove]||(Object.getPrototypeOf(t)||{}).constructor[a.remove]||[]).includes(i)?void 0:(t[a.add]||(Object.getPrototypeOf(t)||{}).constructor[a.add]||[]).includes(i)?r||{configurable:!0,enumerable:!0,value:void 0,writable:!0}:r}),{skipAbsent:!1}),C(t,"getOwnPropertyDescriptors",((e,t,n)=>{let r=e.call(t,...n);if(!r||!t)return r;let i=t[a.remove]||(Object.getPrototypeOf(t)||{}).constructor[a.remove]||[],o=t[a.add]||(Object.getPrototypeOf(t)||{}).constructor[a.add]||[];return[...i,...F].forEach((e=>{delete r[e]})),o.forEach((e=>{r[e]=r[e]||{configurable:!0,enumerable:!0,value:1,writable:!0}})),r}),{skipAbsent:!1}),T(e,"Proxy",((e,t,n)=>{let r=t[1];if(t[0]&&r){let e=r.get,n=t[0][a.proxyId];if(n){let i=(t,r,i)=>r===a.isProxy||(r===a.proxyId?n:e?e(t,r,i):Reflect.get(t,r,i));t[1]={...r,get:i}}}return Reflect.construct(e,t,n)}),{skipAbsent:!1}),!0}function L(e,t){const n=e.UserActivation;if(n&&(v(n.prototype,"hasBeenActive",(()=>!1)),v(n.prototype,"isActive",(()=>!1))),!("getInterestGroupAdAuctionData"in e.navigator)){const s=Object.getOwnPropertyDescriptor(Navigator.prototype,"userAgent").get;C(e.Navigator.prototype,"getInterestGroupAdAuctionData",((e,t,n)=>{s.call(t)}),{skipAbsent:!1})}v(window,"outerWidth",((e,t,n)=>e.call(t,...n)||screen.availWidth)),v(window,"screenX",((e,t,n)=>(e.call(t,...n),0))),v(window,"screenLeft",((e,t,n)=>(e.call(t,...n),0))),v(window,"outerHeight",((e,t,n)=>e.call(t,...n)||screen.availHeight)),v(e,"originAgentCluster",((e,t,n)=>(e.call(t,...n),!0)),{skipAbsent:!0}),C(console,"debug",(()=>{}),{skipAbsent:!0}),C(console,"clear",(()=>{}),{skipAbsent:!0}),speechSynthesis&&C(speechSynthesis,"getVoices",((e,n,r)=>{let i=e.call(n,...r);if(i?.length)return i;let o=t.languageCode||navigator.language||"",a="";a=o.startsWith("zh")?"Microsoft Huihui - Chinese (Simplified, PRC)":o.startsWith("en")?"Microsoft Mark - English (United States)":"Google "+o;let s={default:!0,lang:o,localService:!0,name:a,voiceURI:a};return Object.setPrototypeOf(s,SpeechSynthesisVoice.prototype),[s]}));const r=e.Object,i=e.Reflect,o=/^([a-z]){3}_.*_(Array|Promise|Symbol|JSON|Object|Proxy|Window)$/;function a(e,t,n){let r=e.call(t,...n);return r&&t?r.filter((e=>!o.test(e))).filter((e=>"ret_nodes"!==e)):r}if(C(r,"keys",a,{skipAbsent:!1}),C(r,"getOwnPropertyNames",a,{skipAbsent:!1}),C(i,"ownKeys",a,{skipAbsent:!1}),t.antiDebugger){function l(e){let t=e.toString(),n=t.indexOf("{"),r=t.lastIndexOf("}");return t.substring(n+1,r)}C(window,"setInterval",((e,t,n)=>{let[r,i,...o]=n,a=typeof r;if("function"!==a&&"string"!==a)return e.call(t,...n);let s=r.toString().trim();return s.includes("debugger")?"string"===a?(s=s.replaceAll(/(?<!\\w)debugger(?!\\w)/g,""),n[0]=s,e.call(t,...n)):(s=l(r).trim().replaceAll(";;",";"),s.length>10&&r(...o),n[0]=()=>{},e.call(t,...n)):e.call(t,...n)}))}return!0}function R(e,t,n){if(!t)return e;let r=t.toLowerCase().trim(),i=P[r];return i&&(n+"").trim()!==i+""?"(false)":e}const P={},W=(e,t)=>P[e]=t;function $(e){return!!Object.keys(P).length&&(C(e.window,"matchMedia",((e,t,n)=>{let r=e.call(t,...n);if(!r)return r;let i=r.media;if("not all"===i)return r;let o=function(e,t){const n=new RegExp(`(^|[^a-zA-Z-])(${["all","braille","embossed","handheld","print","projection","screen","speech","tty","tv"].join("|")})($|[^a-zA-Z-])`,"g");return e.replace(/\(([^)]+):([^)]+)\)/g,R).replace(n,(e=>{let n=t[e];return e in t?n?"<non>":"(false)":e}))}(i,P);if(o===i)return r.matches||Object.defineProperty(r,"matches",{value:!0}),r;if(""===o&&!0===r.matches)return r;let a=e.call(t,o);return Object.defineProperty(a,"media",{value:n[0]}),a})),!0)}const j=JSON.parse('{"js":{"76":["Document.onsecuritypolicyviolation","Promise.allSettled"],"77":["Document.onformdata","Document.onpointerrawupdate"],"78":["Element.elementTiming"],"79":["Document.onanimationend","Document.onanimationiteration","Document.onanimationstart","Document.ontransitionend"],"80":["!Document.registerElement","!Element.createShadowRoot","!Element.getDestinationInsertionPoints"],"81":["Document.onwebkitanimationend","Document.onwebkitanimationiteration","Document.onwebkitanimationstart","Document.onwebkittransitionend","Element.ariaAtomic","Element.ariaAutoComplete","Element.ariaBusy","Element.ariaChecked","Element.ariaColCount","Element.ariaColIndex","Element.ariaColSpan","Element.ariaCurrent","Element.ariaDisabled","Element.ariaExpanded","Element.ariaHasPopup","Element.ariaHidden","Element.ariaKeyShortcuts","Element.ariaLabel","Element.ariaLevel","Element.ariaLive","Element.ariaModal","Element.ariaMultiLine","Element.ariaMultiSelectable","Element.ariaOrientation","Element.ariaPlaceholder","Element.ariaPosInSet","Element.ariaPressed","Element.ariaReadOnly","Element.ariaRelevant","Element.ariaRequired","Element.ariaRoleDescription","Element.ariaRowCount","Element.ariaRowIndex","Element.ariaRowSpan","Element.ariaSelected","Element.ariaSort","Element.ariaValueMax","Element.ariaValueMin","Element.ariaValueNow","Element.ariaValueText","Intl.DisplayNames"],"83":["Element.ariaDescription","Element.onbeforexrselect"],"84":["Document.getAnimations","Document.timeline","Element.ariaSetSize","Element.getAnimations"],"85":["Promise.any","String.replaceAll"],"86":["Document.fragmentDirective","Document.replaceChildren","Element.replaceChildren","!Atomics.wake"],"90":["Document.onbeforexrselect","RegExp.hasIndices","!Element.onbeforexrselect"],"91":["Element.getInnerHTML"],"92":["Array.at","String.at"],"93":["Error.cause","Object.hasOwn"],"94":["!Error.cause","Object.hasOwn"],"102":["Element.ariaInvalid","Document.onbeforematch"],"110":["Array.toReversed","Array.toSorted","Array.toSpliced","Array.with"],"111":["String.isWellFormed","String.toWellFormed","Document.startViewTransition"],"116":["WebAssembly.JSTag"],"121":["Array.fromAsync"],"124":["Document.parseHTMLUnsafe","Element.setHTMLUnsafe"],"127":["Symbol.asyncDispose"],"128":["Promise.try","Document.caretPositionFromPoint","Element.currentCSSZoom","Element.ariaColIndexText","Element.ariaRowIndexText"],"129":["Intl.DurationFormat","Document.onscrollsnapchange","Document.onscrollsnapchanging"],"130":["!Element.getInnerHTML"],"133":["Atomics.pause","Document.moveBefore","Element.moveBefore"],"134":["Error.isError"],"135":["Math.f16round","Document.oncommand","Element.ariaActiveDescendantElement","Element.ariaControlsElements","Element.ariaDescribedByElements","Element.ariaDetailsElements","Element.ariaErrorMessageElements","Element.ariaFlowToElements","Element.ariaLabelledByElements"],"136":["RegExp.escape"],"87-89":["Atomics.waitAsync","Document.ontransitioncancel","Document.ontransitionrun","Document.ontransitionstart","Intl.Segmenter"],"95-96":["WebAssembly.Exception","WebAssembly.Tag"],"97-98":["Array.findLast","Array.findLastIndex","Document.onslotchange"],"99-101":["Intl.supportedValuesOf","Document.oncontextlost","Document.oncontextrestored"],"103-106":["Element.role"],"107-109":["Element.ariaBrailleLabel","Element.ariaBrailleRoleDescription"],"112-113":["RegExp.unicodeSets"],"114-115":["JSON.rawJSON","JSON.isRawJSON"],"117-118":["Object.groupBy","Map.groupBy","Document.hasPrivateToken","Document.hasRedemptionRecord"],"119-120":["Promise.withResolvers","Document.hasStorageAccess","Document.requestStorageAccess","Document.requestStorageAccessFor","!Element.setHTML"],"122-123":["Set.union","Set.intersection","Set.difference","Set.symmetricDifference","Set.isSubsetOf","Set.isSupersetOf","Set.isDisjointFrom"],"125-126":["Symbol.dispose","Document.hasUnpartitionedCookieAccess","Element.getHTML"],"131-132":["Document.browsingTopics"]},"A":{"76":["backdrop-filter"],"81":["color-scheme","image-orientation"],"83":["contain-intrinsic-size"],"84":["appearance","ruby-position"],"87":["ascent-override","border-block","border-block-color","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-style","border-inline-width","descent-override","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","line-gap-override","margin-block","margin-inline","padding-block","padding-inline","text-decoration-thickness","text-underline-offset"],"88":["aspect-ratio"],"89":["border-end-end-radius","border-end-start-radius","border-start-end-radius","border-start-start-radius","forced-color-adjust"],"90":["overflow-clip-margin"],"91":["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"],"92":["size-adjust"],"93":["accent-color"],"94":["scrollbar-gutter"],"104":["object-view-box"],"105":["container-name","container-type","container"],"108":["hyphenate-character","!orientation","!max-zoom","!min-zoom","!user-zoom"],"109":["hyphenate-limit-chars","math-depth","math-shift","math-style"],"110":["initial-letter"],"116":["animation-range-end","animation-range-start","animation-timeline","offset-anchor","offset-position","scroll-timeline-axis","scroll-timeline-name","timeline-scope","view-timeline-axis","view-timeline-inset","view-timeline-name","animation-range","scroll-timeline","view-timeline"],"120":["mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","!background-repeat-x","!background-repeat-y","!-webkit-mask-repeat-x","!-webkit-mask-repeat-y"],"125":["anchor-name","inset-area","position-anchor","position-try-options","position-try-order","position-visibility","view-transition-class","anchorName","insetArea","positionAnchor","positionTry","positionTryOptions","positionTryOrder","positionVisibility","viewTransitionClass","position-try"],"126":["navigation","types"],"127":["font-size-adjust","fontSizeAdjust"],"128":["position-try-fallbacks","ruby-align","positionTryFallbacks","rubyAlign","!position-try-options","!positionTryOptions"],"129":["interpolate-size","position-area","interpolateSize","positionArea"],"135":["interactivity","overflow-block","overflow-inline","scroll-marker-group","overflowBlock","overflowInline","scrollMarkerGroup"],"136":["!-webkit-print-color-adjust","dynamic-range-limit","print-color-adjust","dynamicRangeLimit","printColorAdjust","webkit-print-color-adjust"],"77-80":["overscroll-behavior-block","overscroll-behavior-inline"],"85-86":["content-visibility","counter-set","inherits","initial-value","page-orientation","syntax"],"95-96":["app-region","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-width"],"97-98":["font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-synthesis"],"99-100":["text-emphasis-color","text-emphasis-position","text-emphasis-style","text-emphasis"],"101-103":["font-palette","base-palette","override-colors"],"106-107":["hyphenate-character"],"111-113":["baseline-source","font-variant-alternates","view-transition-name"],"114-115":["text-wrap","white-space-collapse"],"117-119":["font-variant-position","overlay","transition-behavior","!-webkit-highlight"],"121-122":["scrollbar-color","scrollbar-width"],"123-124":["field-sizing","text-spacing-trim","fieldSizing","textSpacingTrim"],"130-132":["box-decoration-break","text-wrap-mode","text-wrap-style","boxDecorationBreak","textWrapMode","textWrapStyle"],"133-134":["scroll-initial-target","text-box-edge","text-box-trim","scrollInitialTarget","textBox","textBoxEdge","textBoxTrim","text-box"]},"x":{"80":["CompressionStream","DecompressionStream","FeaturePolicy","FragmentDirective","PeriodicSyncManager","VideoPlaybackQuality"],"81":["SubmitEvent","XRHitTestResult","XRHitTestSource","XRRay","XRTransientInputHitTestResult","XRTransientInputHitTestSource"],"83":["BarcodeDetector","XRDOMOverlayState","XRSystem"],"84":["AnimationPlaybackEvent","AnimationTimeline","CSSAnimation","CSSTransition","DocumentTimeline","FinalizationRegistry","LayoutShiftAttribution","ResizeObserverSize","WakeLock","WakeLockSentinel","WeakRef","XRLayer"],"85":["AggregateError","CSSPropertyRule","EventCounts","XRAnchor","XRAnchorSet"],"86":["RTCEncodedAudioFrame","RTCEncodedVideoFrame"],"87":["CookieChangeEvent","CookieStore","CookieStoreManager","Scheduling"],"88":["Scheduling","!BarcodeDetector"],"89":["ReadableByteStreamController","ReadableStreamBYOBReader","ReadableStreamBYOBRequest","ReadableStreamDefaultController","XRWebGLBinding"],"90":["AbstractRange","CustomStateSet","NavigatorUAData","XRCPUDepthInformation","XRDepthInformation","XRLightEstimate","XRLightProbe","XRWebGLDepthInformation"],"91":["CSSCounterStyleRule","GravitySensor","NavigatorManagedData"],"92":["CSSCounterStyleRule","!SharedArrayBuffer"],"93":["WritableStreamDefaultController"],"94":["AudioData","AudioDecoder","AudioEncoder","EncodedAudioChunk","EncodedVideoChunk","IdleDetector","ImageDecoder","ImageTrack","ImageTrackList","VideoColorSpace","VideoDecoder","VideoEncoder","VideoFrame","MediaStreamTrackGenerator","MediaStreamTrackProcessor","Profiler","VirtualKeyboard","DelegatedInkTrailPresenter","Ink","Scheduler","TaskController","TaskPriorityChangeEvent","TaskSignal","VirtualKeyboardGeometryChangeEvent"],"99":["CanvasFilter","CSSLayerBlockRule","CSSLayerStatementRule"],"100":["CSSMathClamp"],"109":["MathMLElement"],"110":["AudioSinkInfo"],"116":["documentPictureInPicture","DocumentPictureInPicture","WGSLLanguageFeatures","IdentityProvider","DocumentPictureInPictureEvent","ScrollTimeline","ViewTimeline","VisibilityStateEntry"],"117":["CSSStartingStyleRule","Iterator"],"118":["CSSScopeRule"],"119":["!Sanitizer","!openDatabase"],"120":["MediaStreamTrackVideoStats","sharedStorage","fence","Fence","FencedFrameConfig","HTMLFencedFrameElement","IdentityCredentialError","NavigatorLogin","SharedStorage","SharedStorageWorklet"],"121":["CharacterBoundsUpdateEvent","EditContext","TextFormat","TextFormatUpdateEvent","TextUpdateEvent","SpeechSynthesis","SpeechSynthesisVoice"],"122":["Iterator","StorageBucket","StorageBucketManager"],"123":["onpagereveal","NavigationActivation","PageRevealEvent","PerformanceLongAnimationFrameTiming","PerformanceScriptTiming"],"124":["onpageswap","PageSwapEvent","WebSocketError","WebSocketStream"],"125":["MediaStreamTrackAudioStats","PressureObserver","PressureRecord","ProtectedAudience","CSSPositionTryDescriptors","CSSPositionTryRule","ViewTransitionTypeSet"],"126":["WebGLObject","CSSViewTransitionRule","CloseWatcher","caches"],"127":["ChapterInformation","!MutationEvent","!eslint"],"128":["CaretPosition"],"129":["onscrollsnapchange","onscrollsnapchanging","SnapEvent"],"130":["CSSNestedDeclarations"],"131":["XRHand","XRJointPose","XRJointSpace","CSSMarginRule"],"132":["AICreateMonitor","DevicePosture","RestrictionTarget"],"133":["ReportBody","CSPViolationReportBody","FileSystemObserver"],"134":["CSSFontFeatureValuesRule","SharedStorageAppendMethod","SharedStorageClearMethod","SharedStorageDeleteMethod","SharedStorageModifierMethod","SharedStorageSetMethod"],"95-96":["URLPattern"],"97-98":["WebTransport","WebTransportBidirectionalStream","WebTransportDatagramDuplexStream","WebTransportError"],"101-104":["CSSFontPaletteValuesRule"],"105-106":["CSSContainerRule"],"107-108":["XRCamera"],"111-112":["ViewTransition"],"113-115":["ViewTransition","!CanvasFilter"],"135-136":["SuppressedError","DisposableStack","AsyncDisposableStack","Float16Array","FetchLaterResult","fetchLater","oncommand","CommandEvent","Observable","Subscriber"]}}'),z=JSON.parse('{"js":{"71":["Promise.allSettled"],"74":["!Array.toSource","!Boolean.toSource","!Date.toSource","!Error.toSource","!Function.toSource","!Intl.toSource","!JSON.toSource","!Math.toSource","!Number.toSource","!Object.toSource","!RegExp.toSource","!String.toSource","!WebAssembly.toSource"],"77":["String.replaceAll"],"78":["Atomics.add","Atomics.and","Atomics.compareExchange","Atomics.exchange","Atomics.isLockFree","Atomics.load","Atomics.notify","Atomics.or","Atomics.store","Atomics.sub","Atomics.wait","Atomics.wake","Atomics.xor","Document.replaceChildren","Element.replaceChildren","Intl.ListFormat","RegExp.dotAll"],"85":["!Document.onshow","Promise.any"],"86":["Intl.DisplayNames"],"87":["Document.onbeforeinput"],"92":["Object.hasOwn"],"100":["WebAssembly.Tag","WebAssembly.Exception"],"125":["Intl.Segmenter","Document.onbeforetoggle","Document.oncontextlost","Document.oncontextrestored","Element.ariaBrailleLabel","Element.ariaBrailleRoleDescription"],"126":["Element.currentCSSZoom"],"127":["Set.union","Set.difference","Set.intersection","Set.symmetricDifference","Set.isSubsetOf","Set.isSupersetOf","Set.isDisjointFrom"],"128":["Element.getHTML"],"129":["Math.f16round"],"130":["Document.oncontentvisibilityautostatechange"],"134":["RegExp.escape","Promise.try"],"135":["JSON.isRawJSON","JSON.rawJSON"],"136":["Intl.DurationFormat","Element.ariaActiveDescendantElement","Element.ariaControlsElements","Element.ariaDescribedByElements","Element.ariaDetailsElements","Element.ariaErrorMessageElements","Element.ariaFlowToElements","Element.ariaLabelledByElements","Element.ariaOwnsElements"],"137":["Math.sumPrecise","Atomics.pause"],"138":["Error.isError","Error.captureStackTrace"],"72-73":["Document.onformdata","Element.part"],"75-76":["Document.getAnimations","Document.timeline","Element.getAnimations","Intl.Locale"],"79-84":["Promise.any"],"88-89":["RegExp.hasIndices"],"90-91":["Array.at","String.at"],"93-99":["Intl.supportedValuesOf","Document.onsecuritypolicyviolation","Document.onslotchange"],"101-103":["Document.adoptedStyleSheets"],"104-108":["Array.findLast","Array.findLastIndex"],"109-114":["Document.onscrollend"],"115-117":["RegExp.unicodeSets","Array.fromAsync","Array.toReversed","Array.toSorted","Array.toSpliced","Array.with","Document.oncancel"],"118-120":["Object.groupBy","String.isWellFormed","String.toWellFormed","Element.role","Element.ariaAtomic","Element.ariaAutoComplete","Element.ariaBusy","Element.ariaChecked","Element.ariaColCount","Element.ariaColIndex","Element.ariaColIndexText","Element.ariaColSpan","Element.ariaCurrent","Element.ariaDescription","Element.ariaDisabled","Element.ariaExpanded","Element.ariaHasPopup","Element.ariaHidden","Element.ariaInvalid","Element.ariaKeyShortcuts","Element.ariaLabel","Element.ariaLevel","Element.ariaLive","Element.ariaModal","Element.ariaMultiLine","Element.ariaMultiSelectable","Element.ariaOrientation","Element.ariaPlaceholder","Element.ariaPosInSet","Element.ariaPressed","Element.ariaReadOnly","Element.ariaRelevant","Element.ariaRequired","Element.ariaRoleDescription","Element.ariaRowCount","Element.ariaRowIndex","Element.ariaRowIndexText","Element.ariaRowSpan","Element.ariaSelected","Element.ariaSetSize","Element.ariaSort","Element.ariaValueMax","Element.ariaValueMin","Element.ariaValueNow","Element.ariaValueText"],"121-122":["Promise.withResolvers","Map.groupBy"],"123-124":["Document.parseHTMLUnsafe","Element.setHTMLUnsafe"],"131-133":["WebAssembly.JSTag","Document.fragmentDirective"]},"A":{"71":["-moz-column-span"],"72":["offset","offset-anchor","offset-distance","offset-path","offset-rotate","rotate","scale","translate"],"73":["overscroll-behavior-block","overscroll-behavior-inline"],"91":["tab-size"],"96":["color-scheme"],"97":["print-color-adjust","scrollbar-gutter","d"],"102":["overflow-clip-margin"],"109":["-webkit-clip-path"],"110":["container-type","container-name","page","container"],"111":["font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight"],"112":["font-synthesis-small-caps","!-moz-image-region"],"121":["text-wrap"],"122":["offset-position"],"123":["!MozUserFocus","!-moz-user-focus"],"124":["textWrapMode","text-wrap-mode","textWrapStyle","text-wrap-style","whiteSpaceCollapse","white-space-collapse"],"125":["contentVisibility","content-visibility"],"132":["!MozUserModify","!-moz-user-modify"],"74-79":["!-moz-stack-sizing","text-underline-position"],"80-88":["appearance"],"89-90":["!-moz-outline-radius","!-moz-outline-radius-bottomleft","!-moz-outline-radius-bottomright","!-moz-outline-radius-topleft","!-moz-outline-radius-topright","aspect-ratio"],"92-95":["accent-color"],"98-101":["hyphenate-character"],"103-106":["scroll-snap-stop"],"107-108":["backdrop-filter","font-palette","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-width","contain-intrinsic-size"],"113-114":["forced-color-adjust","-webkit-text-security"],"115-117":["baseline-source","math-depth","math-style","animation-composition"],"118-120":["font-synthesis-position"],"126-128":["zoom","!MozPerspective","!-moz-perspective","!MozPerspectiveOrigin","!-moz-perspective-origin","!MozBackfaceVisibility","!-moz-backface-visibility","!MozTransformStyle","!-moz-transform-style","!MozTransitionDuration","!-moz-transition-duration","!MozTransitionTimingFunction","!-moz-transition-timing-function","!MozTransitionProperty","!-moz-transition-property","!MozTransitionDelay","!-moz-transition-delay","!MozTransition","!-moz-transition"],"129-131":["WebkitFontFeatureSettings","-webkit-font-feature-settings","webkitFontFeatureSettings","transitionBehavior","transition-behavior"],"133-134":["MozPerspective","-moz-perspective","MozPerspectiveOrigin","-moz-perspective-origin","MozBackfaceVisibility","-moz-backface-visibility","MozTransformStyle","-moz-transform-style"],"135-136":["!MozUserInput","!-moz-user-input"],"137-138":["hyphenateLimitChars","hyphenate-limit-chars"]},"x":{"71":["MathMLElement","!SVGZoomAndPan"],"74":["FormDataEvent","!uneval"],"75":["AnimationTimeline","CSSAnimation","CSSTransition","DocumentTimeline","SubmitEvent"],"78":["Atomics"],"82":["MediaMetadata","MediaSession","Sanitizer"],"83":["MediaMetadata","MediaSession","!Sanitizer"],"84":["PerformancePaintTiming"],"87":["onbeforeinput"],"88":["onbeforeinput","!VisualViewport"],"96":["Lock","LockManager"],"97":["CSSLayerBlockRule","CSSLayerStatementRule"],"98":["HTMLDialogElement"],"99":["NavigationPreloadManager"],"110":["CSSContainerRule"],"111":["FileSystemFileHandle","FileSystemDirectoryHandle"],"112":["FileSystemFileHandle","!U2F"],"121":["UserActivation","ToggleEvent","!sizeToContent"],"124":["!DOMRequest"],"125":["ContentVisibilityAutoStateChangeEvent","RTCIceTransport","onbeforetoggle","oncontextlost","oncontextrestored"],"126":["WakeLockSentinel","WakeLock","CustomStateSet"],"127":["ClipboardItem"],"128":["CSSPropertyRule"],"129":["!MediaCapabilitiesInfo","Float16Array","CSSStartingStyleRule","TextEvent","CSSPageDescriptors"],"130":["AudioDecoder","EncodedVideoChunk","AudioData","VideoDecoder","VideoFrame","AudioEncoder","VideoColorSpace","VideoEncoder","EncodedAudioChunk","oncontentvisibilityautostatechange"],"131":["Iterator","FragmentDirective"],"132":["CSSNestedDeclarations"],"136":["SVGDiscardElement"],"137":["CookieChangeEvent","CookieStore","cookieStore"],"138":["NavigatorLogin","originAgentCluster","!CookieChangeEvent","!CookieStore","!cookieStore"],"72-73":["!BatteryManager","FormDataEvent","Geolocation","GeolocationCoordinates","GeolocationPosition","GeolocationPositionError","!mozPaintCount"],"76-77":["AudioParamMap","AudioWorklet","AudioWorkletNode","Worklet"],"79-81":["AggregateError","FinalizationRegistry"],"85-86":["PerformancePaintTiming","!HTMLMenuItemElement","!onshow"],"89-92":["!ondevicelight","!ondeviceproximity","!onuserproximity"],"93-95":["ElementInternals"],"100-104":["WritableStream"],"105-106":["TextDecoderStream","OffscreenCanvasRenderingContext2D","OffscreenCanvas","TextEncoderStream"],"107-109":["CSSFontPaletteValuesRule"],"113-114":["CompressionStream","DecompressionStream","WebTransportSendStream","WebTransportDatagramDuplexStream","WebTransportError","WebTransportReceiveStream","WebTransport","WebTransportBidirectionalStream","!mozRTCPeerConnection","!mozRTCSessionDescription","!mozRTCIceCandidate"],"115-120":["RTCEncodedAudioFrame","RTCSctpTransport","RTCEncodedVideoFrame","RTCRtpScriptTransform","oncancel","!OfflineResourceList","!applicationCache"],"122-123":["LargestContentfulPaint"],"133-135":["ImageDecoder","ImageTrackList","ImageTrack"]}}');function _(e={},t=0){let n=new Set,r=new Set;for(let[i,o]of Object.entries(e)){o=o||[];let e=(i+"").split("-"),a=+e[0],s=+e[e.length-1];a<t&&o.forEach((e=>{let t=e;"!"===e.toString().charAt(0)?(t=e.toString().substring(1),r.add(t)):n.add(t)})),a>=t&&s<=t&&o.forEach((e=>{let t=e;"!"===e.toString().charAt(0)?(t=e.toString().substring(1),r.add(t)):n.add(t)})),s>t&&o.forEach((e=>{let t=e;if("!"===e.toString().charAt(0)){if(t=e.toString().substring(1),r.has(t))return;n.add(t)}else{if(n.has(t))return;r.add(t)}}))}return{removes:[...r],adds:[...n]}}const Z=()=>class{constructor(){let e=new TypeError("Illegal constructor");throw e.stack="Illegal constructor\n    at <anonymous>:1:1",e}},U=new Set;function H(e,t){if(t.safeMode||!t.uaInfo)return!1;let n=t.uaInfo.engine;if(!n.name)return!1;let r=n.version?n.version:"0";const i="BLINK"===n.name.toUpperCase(),s="GECKO"===n.name.toUpperCase();let l=Number(/\d+/.exec(r+"")[0]);if(i&&l<76||s&&l<71)return!1;let c=function(e,t){let{css:n,win:r,js:i}=function(e){const t="BLINK"===e.toUpperCase(),n="GECKO"===e.toUpperCase();return{css:t?j.A:n?z.A:{},win:t?j.x:n?z.x:{},js:t?j.js:n?z.js:{}}}(e),o=_(n,t),a=_(r,t),s=_(i,t);return{adds:{css:o.adds,win:a.adds,js:s.adds},removes:{css:o.removes,win:a.removes,js:s.removes}}}(n.name,l),{adds:{js:u,css:d,win:m},removes:{js:h,css:f,win:p}}=c;return((e,t=[],n=[])=>{t.forEach((t=>{let[n,r]=t.split(".");if(o.getValue(e,t))return;let i=o.getValue(e,n+".prototype");if(!i){if(r.charAt(0)===r.charAt(0).toUpperCase()){let t=Z();return void O(e,r,t)}return void O(e,t,(function(){}))}if(Object.getOwnPropertyDescriptor(i,r))return;Object.defineProperty(i,r,{value:function(){},writable:!0})})),n.forEach((t=>{let[n,r]=t.split(".");if(o.getValue(e,t)){U.add(t);try{o.deleteValue(e,t)}catch(e){self[a.attach].errors.push(e)}return}let i=o.getValue(e,n+".prototype");if(i){U.add(n+".prototype."+r);try{delete i[r]}catch(t){let i=o.getValue(e,n);i[a.remove]||(i[a.remove]=[]),i[a.remove].push(r)}}}))})(e,u,h),((e,t=[],n=[])=>{t.forEach((t=>{if(!o.getValue(self,t))if(t.charAt(0)!==t.charAt(0).toUpperCase())O(e,t,(function(){}));else{let n=Z();O(e,t,n)}})),n.forEach((t=>{U.add(t);try{delete e[t],delete e.self[t]}catch(e){self[a.attach].errors.push(e)}}))})(e,m,p),"Window"===e.constructor.name&&((e,t=[],n=[])=>{let r=(e="")=>{if("string"!=typeof e)return e;let t=e.split("-"),n="";return t.forEach((e=>{if(e)return n?void(n+=e.charAt(0).toUpperCase()+e.substring(1)):n+=e})),n};t=new Set(t.map(r)),n=new Set(n.map(r));let i=e=>e?(e[a.add]=t,e[a.remove]=n,new Proxy(e,{get:(e,t)=>function(e,t){if("__origin__"===t)return e;let i=e[t];if("function"==typeof i)return i.prototype?i:i+"".startsWith("function")?i.bind(e):i;let o=r(t);return n.has(o)?void 0:n.has(r(i))?"":(e.fakeCss=e.fakeCss||{},i||e.fakeCss[o]||"")}(e,t),set:(e,i,o)=>function(e,i,o){if("fakeCss"===i)return!0;if("function"==typeof o||"object"==typeof o)return e[i]=o,!0;let a=r(i);return i in e?(n.has(a)||(e.setProperty(i,o),e[i]=o),!0):!t.has(a)||(e.fakeCss=e.fakeCss||{},e.fakeCss[a]=o,!0)}(e,i,o),has:(e,i)=>function(e,i){let o=r(i);return!!n.has(o)||!!t.has(o)||i in e}(e,i)})):e;const o=e.CSSStyleDeclaration;C(o.prototype,"getPropertyValue",((e,t,n)=>t[n[0]]||""),{skipAbsent:!1}),C(o.prototype,"setProperty",((e,t,n)=>t[n[0]]=n[1]),{skipAbsent:!1}),C(o.prototype,"item",((e,t,n)=>{let r=e.call(t,...n);return r in t?r:""}),{skipAbsent:!1}),C(o.prototype,"item",((e,t,n)=>{let r=e.call(t,...n);return r in t?r:""}),{skipAbsent:!1});const s=e.HTMLElement,l=e.CSSStyleRule;v(s.prototype,"style",((e,t,n)=>{let r=e.call(t,...n);return i(r)}),{skipAbsent:!1}),C(e.window,"getComputedStyle",((e,t,n)=>{let r=e.call(t,...n);return i(r)}),{skipAbsent:!1}),v(l.prototype,"style",((e,t,n)=>{let r=e.call(t,...n);return i(r)}),{skipAbsent:!1})})(e,d,f),!0}const B={7:["Cambria Math","Lucida Console"],8:["Aldhabi","Gadugi","Myanmar Text","Nirmala UI"],8.1:["Leelawadee UI","Javanese Text","Segoe UI Emoji"],10:["HoloLens MDL2 Assets","Segoe MDL2 Assets","Bahnschrift","Ink Free"],11:["Segoe Fluent Icons"]},G={10.9:["Helvetica Neue","Geneva"],"10.10":["Kohinoor Devanagari Medium","Luminari"],10.11:["PingFang HK Light"],10.12:["American Typewriter Semibold","Futura Bold","SignPainter-HouseScript Semibold"],"10.13-10.14":["InaiMathi Bold"],"10.15-11":["Galvji","MuktaMahee Regular"],12:["Noto Sans Gunjala Gondi Regular","Noto Sans Masaram Gondi Regular","Noto Serif Yezidi Regular"],13:["Apple SD Gothic Neo ExtraBold","STIX Two Math Regular","STIX Two Text Regular","Noto Sans Canadian Aboriginal Regular"]},q={"Microsoft Outlook":["MS Outlook"],"Adobe Acrobat":["ZWAdobeF"],LibreOffice:["Amiri","KACSTOffice","Liberation Mono","Source Code Pro"],OpenOffice:["DejaVu Sans","Gentium Book Basic","OpenSymbol"]},J=Object.keys(G).map((e=>G[e])).flat(),Y=Object.keys(B).map((e=>B[e])).flat(),X=Object.keys(q).map((e=>q[e])).flat();new Set([...J,...Y,"Arimo","Chilanka","Cousine","Jomolhari","MONO","Noto Color Emoji","Ubuntu","Dancing Script","Droid Sans Mono","Roboto",...X]);const K={android:["Dancing Script","Droid Sans Mono","Roboto"],linux:["Arimo","Chilanka","Cousine","Jomolhari","MONO","Noto Color Emoji","Ubuntu"],apple:["Helvetica Neue","Geneva","Kohinoor Devanagari Medium","Luminari","PingFang HK Light","American Typewriter Semibold","Futura Bold","SignPainter-HouseScript Semibold","InaiMathi Bold","Galvji","MuktaMahee Regular","Bai Jamjuree","Chakra Petch","Charmonman","Kodchasan"],windows:["Cambria Math","Lucida Console","Aldhabi","Gadugi","Myanmar Text","Nirmala UI","Leelawadee UI","Javanese Text","Segoe UI Emoji","HoloLens MDL2 Assets","Segoe MDL2 Assets","Bahnschrift","Ink Free","Segoe Fluent Icons"]};function Q(e="",t=[]){if(!e)return!1;let n=e.split(" "),r=(n[n.length-1]||"").toUpperCase();return t.find((e=>e.toUpperCase()===r))}const ee=["monospace","sans-serif","serif"];let te=Object.getOwnPropertyDescriptor(HTMLElement.prototype,"offsetWidth").get,ne=Object.getOwnPropertyDescriptor(HTMLElement.prototype,"offsetHeight").get;function re(e,t){const n=t.factors.fonts;if(!n)return!1;const r=e.HTMLElement,i=o.randomInt(n,5,20);let a;if(t.uaInfo)a=((t.uaInfo.os||{}).name||"").toLowerCase();else{let b=e.navigator.userAgent.toLowerCase();b.includes("windows")?a="windows":b.includes("ios")||b.includes("macintosh")?a="apple":b.includes("android")?a="android":b.includes("linux")&&(a="linux")}let s=a;"ios"!==a&&"mac"!==a||(s="apple");let l=K[s]||[];if(!l.length)return!1;let c=Object.keys(K).filter((e=>e!==s)).map((e=>K[e])).flat()||[];const u={};function d(e){if(Q(e,c))return!0;let t=function(e,t){if(!e||!t||!t.length)return!1;const n=t.reduce(((e,t)=>(e[t]=!0,e)),{}),r="Cambria Math"in n||"Nirmala UI"in n||"Leelawadee UI"in n||"HoloLens MDL2 Assets"in n||"Segoe Fluent Icons"in n,i="Helvetica Neue"in n||"Luminari"in n||"PingFang HK Light"in n||"Futura Bold"in n||"InaiMathi Bold"in n||"Galvji"in n||"Chakra Petch"in n,o="Arimo"in n||"MONO"in n||"Ubuntu"in n||"Noto Color Emoji"in n||"Dancing Script"in n||"Droid Sans Mono"in n||"Roboto"in n;let a=e.toLowerCase();return!(!r||"windows"===a)||!(!i||"apple"===a)||!(!o||"linux"===a)}(s,[e]);if(t)return!0;if(Q(e,l))return!1;let r=u[e];return r||(u[e]=o.hashcode(e),r=u[e]),1===o.randomInt(n+r,1,4)}const m={};if(r){function S(e,t,r){let i=e.call(t,...r),a=o.getStyle(t,"font-family"),s=o.getStyle(t,"font-size");if(!a||!s)return i;if((s.match(/\d+/g)?.map(Number)[0]||0)>200)return i;let l=a.split(",").map((e=>e.trim()));for(let e of l){if(ee.includes(e))return m[e]=i,i;if(d(e))continue;if(!Object.values(m).includes(i))return i;let t=Math.abs(o.hashcode(e));return i+o.randomInt(n+t,1,50)/100}return Object.values(m)[0]||i}v(r.prototype,"offsetWidth",S,{skipAbsent:!1})}const h=e.TextMetrics;h&&v(h.prototype,"width",((e,t,r)=>{let a=e.call(t,...r);return a?a%i==0?a:a+(o.randomNum(n,5e3)-2500)/5e3:a}));const f=e.SVGTextContentElement;f&&C(f.prototype,"getComputedTextLength",((e,t,r)=>{let a=e.call(t,...r);return a?a%i==0?a:a+(o.randomNum(n,5e3)-2500)/5e3:a}));const p=e.FontFace;let y=p.prototype.load;const g=Object.getPrototypeOf(e.document.fonts);if(p){function w(e,t){const n=new p(e,`local(${e})`);return y.call(n).then((e=>(Object.defineProperty(e,"family",{value:t}),e))).catch((e=>{throw e}))}C(p.prototype,"load",((t,n,r)=>{const i=n.family;let o=t.call(n,...r);return o instanceof e.Promise?"CTHULHU_JS"===i||ee.includes(i)?o:d(i)?w("CTHULHU_JS",i):o.then((e=>e)).catch((e=>w("Arial",i))):o}))}return g&&C(g,"check",((e,t,n)=>{let r=e.call(t,...n),i=n[0];if(!i)return r;let o=/".+"/.exec(i);if(!o)return r;let a=o[1];return a?ee.includes(a)?r:!d(a):r})),!0}function ie(e,t){const n=t.factors.clientRect;if(!n)return!1;const r=e.Element;return!!r&&(C(r.prototype,"getClientRects",(function(e,t,r){let i=e.call(t,...r);if(t[a.fake]||["rect-known","rect-ghost","rects"].includes(t.className))return i;let s=!0;for(let e in i[0])if("toJSON"!==e&&(0!==i[0][e]||0!==i[0][e])){s=!1;break}if(s)return i;const l=1e-4*o.randomInt(n,-999,999);if("absolute"===t.style.position){let e=(t.style.left.match(/\d+/g)||[]).map(Number)[0]||0;t.style.left=e+l+"px"}else t.style.marginLeft=l+"%";return t[a.fake]=1,e.call(t,...r)}),{skipAbsent:!1}),!0)}function oe(e,t){const n=e.GeolocationCoordinates,r=t.location;return!(!r||!r.lat&&!r.lng||(n&&r&&(v(n.prototype,"longitude",((e,t,n)=>{let i=e.call(t,...n);return r.lng/1||i})),v(n.prototype,"latitude",((e,t,n)=>{let i=e.call(t,...n);return r.lat/1||i}))),0))}function ae(e,t){const n=e.Plugin;if(!t.factors.plugins||!n)return!1;let r=o.randomNum(t.factors.plugins,999999);const i=(e,t,n)=>{let i=e.call(t,...n),o=new String(i);return o.valueOf=function(){return i+" "+r},o.valueOf[a.native]=1,o.toString=function(){return i+" "+r},o.toString[a.native]=1,o};return v(n.prototype,"description",i,{skipAbsent:!0}),v(n.prototype,"name",i,{skipAbsent:!0}),t.hideFlash&&navigator.plugins&&new Proxy(navigator.plugins,{get(e,t,r){const i=Reflect.get(e,t,r);if(!(i instanceof n&&i.name.includes("Shockwave Flash")))return i}}),!0}function se(e,t){const n=t.factors.audio;if(!n)return;t.safeMode;const r=e.AudioBuffer,i=e.AnalyserNode;function a(e){let t=0;const r=[-1,0,1];o.randomPeek(n,e,((n,i)=>{if(!r.includes(n)){if(t>0&&!r.includes(e[t])){let r=e[t];return e[t]=n,t=0,r}t=i}}),1,Math.min(e.length,1024))}return C(r.prototype,"getChannelData",((e,t,n)=>{let r=e.call(t,...n);return r?(a(r),r):r})),C(r.prototype,"copyFromChannel",((e,t,n)=>{let r=e.call(t,...n);return n[0]&&a(n[0]),r})),C(i.prototype,"getFloatFrequencyData",((e,t,r)=>{let i=e.call(t,...r);return r[0]&&o.randomNoise(n,r[0],[0]),i}),{skipAbsent:!0}),C(i.prototype,"getFloatTimeDomainData",((e,t,r)=>{let i=e.call(t,...r);return r[0]&&o.randomNoise(n,r[0],[0]),i}),{skipAbsent:!0}),!0}!function(e){try{const t=document.createElement("span");t.textContent="lLmMwWiI0O&18",t.style.visibility="hidden",t.style.fontFamily=e,document.body.appendChild(t);let n=te.apply(t),r=ne.apply(t);return document.body.removeChild(t),{width:n,height:r}}catch(e){return-1}}(ee.join(","));const le=self.Date;function ce(e,t=0){if(t>3)return!1;let n=0;for(let r of e){if(n>50)break;n++;let e=ue(r,t+1);if(e)return e}return!1}function ue(e,t=0){if(t>3)return!1;if(null==e)return!1;let n=e[a.trace];if(n&&"path"in n&&"handlers"in n&&"source"in n)return n;if("object"!=typeof e)return!1;if(e instanceof Map)return ce(e.values(),t+1);if(e instanceof Array)return ce(e,t+1);if(e instanceof Set)return ce(e,t+1);if(e.constructor!==Object)return!1;let r=Object.getOwnPropertyDescriptors(e);for(let n in r){if("function"==typeof r[n].get)continue;let i=ue(e[n],t+1);if(i)return i}return!1}function de(e,t){if(null==e)return e;let n=function(e){let t=e;return"number"!=typeof e||e instanceof Number||(t=new Number(e)),"boolean"!=typeof e||e instanceof Boolean||(t=new Boolean(e)),"string"!=typeof e||e instanceof String||(t=new String(e)),t}(e);return n[a.trace]=t,n}const me=["sort","splice","slice","includes","join","filter","map","reduce","toReversed","toSorted","toSpliced"];function he(e,t,n){function r(){return n}function i(){return N(t,n)}r[a.native]=2,Object.defineProperty(e,"name",{get:r}),i[a.native]=1,Object.defineProperty(e,"toString",{value:i})}function fe(e,t){return!(null==t||!e.functionTrace)&&!(!e.dev&&((new le).getTime()-e.start)/1e3>30)}function pe(e,t,n,r){let i=ue(n);if(i||(i=ue(r),i&&(n[a.trace]=i)),i){let o=i.handlers[e];return o?o(t,n,r,i):(i.path.push(e),de(t.call(n,...r),i))}return t.call(n,...r)}function ye(e,t){return!!t.functionTrace&&(function(e,t){if(!e)return;let n=Object.getOwnPropertyDescriptors(e.prototype);for(let r of me){let i=n[r];if(i&&i.value&&"function"==typeof i.value){if("valueOf"===r||"toString"===r)continue;C(e.prototype,r,((e,n,i)=>fe(t,n)?pe("Array."+r,e,n,i):e.call(n,...i)))}}}(e.Array,t),!0)}console.log;const ge=255;function be(e,t){const n=t.factors.canvas;if(!n)return!1;t.safeMode;const r=e.HTMLCanvasElement,i=e.CanvasRenderingContext2D;function s(e,r,i){let s=e.call(r,...i);if(!s||!s.data)return s;if("string"==typeof r.fillStyle&&r.fillStyle.startsWith("rgba"))return s;if(i){let[e,t,n,r]=i;if(n<=1&&r<=1)return s}return function(e,t){if(!e)return;if("srgb"!==e.colorSpace)return;let n=e.data,r=4*e.width;e.height,o.randomPeek(t,n,((e,t)=>{if(!e||(t+1)%4||e<ge)return;let i=n[t-3],o=n[t-2],a=n[t-1];if(!i||!o||!a)return;let s=t/r,l=t%r,c=n[r*(s-1)+l],u=n[r*(s+1)+l],d=n[l>4?l+4:-1],m=n[l<r?l-4:-1];return c&&c<ge||u&&u<ge||d&&d<ge||m&&m<ge?void 0:(function(e,t,n){let r=e[t];e[t]=e[n],e[n]=r}(n,t-1,t-2),e)}),3,10)}(s,n),s&&fe(t,s.data)&&(function(e){for(let t of me){let n=e[t];"function"==typeof n&&(e[t]=function(){return pe("Array."+t,n,this,arguments)},he(e[t],"function",t))}}(s.data),s.data[a.trace]={source:"Canvas.image",path:[],handlers:{"Array.includes":(e,t,n,r)=>"Array.join"===r.path[r.path.length-1]||e.call(t,...n)}}),s}function l(e){const{width:t,height:n}=e;if(!(t<=5&&n<=5))return e.getContext("2d")}function c(e,t){let n=t.getImageData(0,0,Math.min(e.width,300),Math.min(e.height,300));t.putImageData(n,0,0)}function u(e,t,n){let r=l(t);if(!r)return e.call(t,...n);if("string"==typeof r.fillStyle&&r.fillStyle.startsWith("rgba"))return e.call(t,...n);c(t,r);let i=e.call(t,...n);return c(t,r),i}C(i.prototype,"getImageData",s),C(r.prototype,"toBlob",u),C(r.prototype,"toDataURL",u),C(r.prototype,"getContext",((e,t,n)=>(n[1]={...n[1]||{}},n[1].willReadFrequently=!0,e.call(t,...n))),{skipAbsent:!1});const d=e.OffscreenCanvas,m=e.OffscreenCanvasRenderingContext2D;return d&&m&&(C(d.prototype,"convertToBlob",((e,t,n)=>{let r=l(t);if(!r)return e.call(t,...n);c(t,r);let i=e.call(t,...n);return i instanceof Promise?i.then((e=>(c(t,r),e))):(c(t,r),i)}),{skipAbsent:!1}),C(m.prototype,"getImageData",s)),C(i.prototype,"drawImage",((e,t,n)=>{let r=e.call(t,...n);return c(t.canvas,t),r})),!0}class Se extends Error{}class we extends Se{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}}class ke extends Se{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}}class ve extends Se{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}}class Ee extends Se{}class Ce extends Se{constructor(e){super(`Invalid unit ${e}`)}}class Te extends Se{}class Oe extends Se{constructor(){super("Zone is an abstract class")}}const De="numeric",Ae="short",Me="long",Ie={year:De,month:De,day:De},xe={year:De,month:Ae,day:De},Ne={year:De,month:Ae,day:De,weekday:Ae},Fe={year:De,month:Me,day:De},Ve={year:De,month:Me,day:De,weekday:Me},Le={hour:De,minute:De},Re={hour:De,minute:De,second:De},Pe={hour:De,minute:De,second:De,timeZoneName:Ae},We={hour:De,minute:De,second:De,timeZoneName:Me},$e={hour:De,minute:De,hourCycle:"h23"},je={hour:De,minute:De,second:De,hourCycle:"h23"},ze={hour:De,minute:De,second:De,hourCycle:"h23",timeZoneName:Ae},_e={hour:De,minute:De,second:De,hourCycle:"h23",timeZoneName:Me},Ze={year:De,month:De,day:De,hour:De,minute:De},Ue={year:De,month:De,day:De,hour:De,minute:De,second:De},He={year:De,month:Ae,day:De,hour:De,minute:De},Be={year:De,month:Ae,day:De,hour:De,minute:De,second:De},Ge={year:De,month:Ae,day:De,weekday:Ae,hour:De,minute:De},qe={year:De,month:Me,day:De,hour:De,minute:De,timeZoneName:Ae},Je={year:De,month:Me,day:De,hour:De,minute:De,second:De,timeZoneName:Ae},Ye={year:De,month:Me,day:De,weekday:Me,hour:De,minute:De,timeZoneName:Me},Xe={year:De,month:Me,day:De,weekday:Me,hour:De,minute:De,second:De,timeZoneName:Me};class Ke{get type(){throw new Oe}get name(){throw new Oe}get ianaName(){return this.name}get isUniversal(){throw new Oe}offsetName(e,t){throw new Oe}formatOffset(e,t){throw new Oe}offset(e){throw new Oe}equals(e){throw new Oe}get isValid(){throw new Oe}}let Qe=null;class et extends Ke{static get instance(){return null===Qe&&(Qe=new et),Qe}get type(){return"system"}get name(){return(new Intl.DateTimeFormat).resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:t,locale:n}){return Sn(e,t,n)}formatOffset(e,t){return En(this.offset(e),t)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return"system"===e.type}get isValid(){return!0}}let tt={};const nt={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};let rt={};class it extends Ke{static create(e){return rt[e]||(rt[e]=new it(e)),rt[e]}static resetCache(){rt={},tt={}}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(e){return!1}}constructor(e){super(),this.zoneName=e,this.valid=it.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:t,locale:n}){return Sn(e,t,n,this.name)}formatOffset(e,t){return En(this.offset(e),t)}offset(e){const t=new Date(e);if(isNaN(t))return NaN;const n=(r=this.name,tt[r]||(tt[r]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:r,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),tt[r]);var r;let[i,o,a,s,l,c,u]=n.formatToParts?function(e,t){const n=e.formatToParts(t),r=[];for(let e=0;e<n.length;e++){const{type:t,value:i}=n[e],o=nt[t];"era"===t?r[o]=i:Xt(o)||(r[o]=parseInt(i,10))}return r}(n,t):function(e,t){const n=e.format(t).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,i,o,a,s,l,c,u]=r;return[a,i,o,s,l,c,u]}(n,t);"BC"===s&&(i=1-Math.abs(i));let d=+t;const m=d%1e3;return d-=m>=0?m:1e3+m,(pn({year:i,month:o,day:a,hour:24===l?0:l,minute:c,second:u,millisecond:0})-d)/6e4}equals(e){return"iana"===e.type&&e.name===this.name}get isValid(){return this.valid}}let ot={},at={};function st(e,t={}){const n=JSON.stringify([e,t]);let r=at[n];return r||(r=new Intl.DateTimeFormat(e,t),at[n]=r),r}let lt={},ct={},ut=null,dt={};function mt(e,t,n,r){const i=e.listingMode();return"error"===i?null:"en"===i?n(t):r(t)}class ht{constructor(e,t,n){this.padTo=n.padTo||0,this.floor=n.floor||!1;const{padTo:r,floor:i,...o}=n;if(!t||Object.keys(o).length>0){const t={useGrouping:!1,...n};n.padTo>0&&(t.minimumIntegerDigits=n.padTo),this.inf=function(e,t={}){const n=JSON.stringify([e,t]);let r=lt[n];return r||(r=new Intl.NumberFormat(e,t),lt[n]=r),r}(e,t)}}format(e){if(this.inf){const t=this.floor?Math.floor(e):e;return this.inf.format(t)}return sn(this.floor?Math.floor(e):dn(e,3),this.padTo)}}class ft{constructor(e,t,n){let r;if(this.opts=n,this.originalZone=void 0,this.opts.timeZone)this.dt=e;else if("fixed"===e.zone.type){const t=e.offset/60*-1,n=t>=0?`Etc/GMT+${t}`:`Etc/GMT${t}`;0!==e.offset&&it.create(n).valid?(r=n,this.dt=e):(r="UTC",this.dt=0===e.offset?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else"system"===e.zone.type?this.dt=e:"iana"===e.zone.type?(this.dt=e,r=e.zone.name):(r="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);const i={...this.opts};i.timeZone=i.timeZone||r,this.dtf=st(t,i)}format(){return this.originalZone?this.formatToParts().map((({value:e})=>e)).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){const e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map((e=>{if("timeZoneName"===e.type){const t=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...e,value:t}}return e})):e}resolvedOptions(){return this.dtf.resolvedOptions()}}class pt{constructor(e,t,n){this.opts={style:"long",...n},!t&&en()&&(this.rtf=function(e,t={}){const{base:n,...r}=t,i=JSON.stringify([e,r]);let o=ct[i];return o||(o=new Intl.RelativeTimeFormat(e,t),ct[i]=o),o}(e,n))}format(e,t){return this.rtf?this.rtf.format(e,t):function(e,t,n="always",r=!1){const i={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},o=-1===["hours","minutes","seconds"].indexOf(e);if("auto"===n&&o){const n="days"===e;switch(t){case 1:return n?"tomorrow":`next ${i[e][0]}`;case-1:return n?"yesterday":`last ${i[e][0]}`;case 0:return n?"today":`this ${i[e][0]}`}}const a=Object.is(t,-0)||t<0,s=Math.abs(t),l=1===s,c=i[e],u=r?l?c[1]:c[2]||c[1]:l?i[e][0]:e;return a?`${s} ${u} ago`:`in ${s} ${u}`}(t,e,this.opts.numeric,"long"!==this.opts.style)}formatToParts(e,t){return this.rtf?this.rtf.formatToParts(e,t):[]}}const yt={firstDay:1,minimalDays:4,weekend:[6,7]};class gt{static fromOpts(e){return gt.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)}static create(e,t,n,r,i=!1){const o=e||Lt.defaultLocale,a=o||(i?"en-US":ut||(ut=(new Intl.DateTimeFormat).resolvedOptions().locale,ut)),s=t||Lt.defaultNumberingSystem,l=n||Lt.defaultOutputCalendar,c=on(r)||Lt.defaultWeekSettings;return new gt(a,s,l,c,o)}static resetCache(){ut=null,at={},lt={},ct={}}static fromObject({locale:e,numberingSystem:t,outputCalendar:n,weekSettings:r}={}){return gt.create(e,t,n,r)}constructor(e,t,n,r,i){const[o,a,s]=function(e){const t=e.indexOf("-x-");-1!==t&&(e=e.substring(0,t));const n=e.indexOf("-u-");if(-1===n)return[e];{let t,r;try{t=st(e).resolvedOptions(),r=e}catch(i){const o=e.substring(0,n);t=st(o).resolvedOptions(),r=o}const{numberingSystem:i,calendar:o}=t;return[r,i,o]}}(e);this.locale=o,this.numberingSystem=t||a||null,this.outputCalendar=n||s||null,this.weekSettings=r,this.intl=function(e,t,n){return n||t?(e.includes("-u-")||(e+="-u"),n&&(e+=`-ca-${n}`),t&&(e+=`-nu-${t}`),e):e}(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=i,this.fastNumbersCached=null}get fastNumbers(){var e;return null==this.fastNumbersCached&&(this.fastNumbersCached=(!(e=this).numberingSystem||"latn"===e.numberingSystem)&&("latn"===e.numberingSystem||!e.locale||e.locale.startsWith("en")||"latn"===new Intl.DateTimeFormat(e.intl).resolvedOptions().numberingSystem)),this.fastNumbersCached}listingMode(){const e=this.isEnglish(),t=!(null!==this.numberingSystem&&"latn"!==this.numberingSystem||null!==this.outputCalendar&&"gregory"!==this.outputCalendar);return e&&t?"en":"intl"}clone(e){return e&&0!==Object.getOwnPropertyNames(e).length?gt.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,on(e.weekSettings)||this.weekSettings,e.defaultToEN||!1):this}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,t=!1){return mt(this,e,An,(()=>{const n=t?{month:e,day:"numeric"}:{month:e},r=t?"format":"standalone";return this.monthsCache[r][e]||(this.monthsCache[r][e]=function(e){const t=[];for(let n=1;n<=12;n++){const r=Ai.utc(2009,n,1);t.push(e(r))}return t}((e=>this.extract(e,n,"month")))),this.monthsCache[r][e]}))}weekdays(e,t=!1){return mt(this,e,Nn,(()=>{const n=t?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},r=t?"format":"standalone";return this.weekdaysCache[r][e]||(this.weekdaysCache[r][e]=function(e){const t=[];for(let n=1;n<=7;n++){const r=Ai.utc(2016,11,13+n);t.push(e(r))}return t}((e=>this.extract(e,n,"weekday")))),this.weekdaysCache[r][e]}))}meridiems(){return mt(this,void 0,(()=>Fn),(()=>{if(!this.meridiemCache){const e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[Ai.utc(2016,11,13,9),Ai.utc(2016,11,13,19)].map((t=>this.extract(t,e,"dayperiod")))}return this.meridiemCache}))}eras(e){return mt(this,e,Pn,(()=>{const t={era:e};return this.eraCache[e]||(this.eraCache[e]=[Ai.utc(-40,1,1),Ai.utc(2017,1,1)].map((e=>this.extract(e,t,"era")))),this.eraCache[e]}))}extract(e,t,n){const r=this.dtFormatter(e,t).formatToParts().find((e=>e.type.toLowerCase()===n));return r?r.value:null}numberFormatter(e={}){return new ht(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,t={}){return new ft(e,this.intl,t)}relFormatter(e={}){return new pt(this.intl,this.isEnglish(),e)}listFormatter(e={}){return function(e,t={}){const n=JSON.stringify([e,t]);let r=ot[n];return r||(r=new Intl.ListFormat(e,t),ot[n]=r),r}(this.intl,e)}isEnglish(){return"en"===this.locale||"en-us"===this.locale.toLowerCase()||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:tn()?function(e){let t=dt[e];if(!t){const n=new Intl.Locale(e);t="getWeekInfo"in n?n.getWeekInfo():n.weekInfo,dt[e]=t}return t}(this.locale):yt}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}toString(){return`Locale(${this.locale}, ${this.numberingSystem}, ${this.outputCalendar})`}}let bt=null;class St extends Ke{static get utcInstance(){return null===bt&&(bt=new St(0)),bt}static instance(e){return 0===e?St.utcInstance:new St(e)}static parseSpecifier(e){if(e){const t=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(t)return new St(wn(t[1],t[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return 0===this.fixed?"UTC":`UTC${En(this.fixed,"narrow")}`}get ianaName(){return 0===this.fixed?"Etc/UTC":`Etc/GMT${En(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,t){return En(this.fixed,t)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return"fixed"===e.type&&e.fixed===this.fixed}get isValid(){return!0}}class wt extends Ke{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}}function kt(e,t){if(Xt(e)||null===e)return t;if(e instanceof Ke)return e;if("string"==typeof e){const n=e.toLowerCase();return"default"===n?t:"local"===n||"system"===n?et.instance:"utc"===n||"gmt"===n?St.utcInstance:St.parseSpecifier(n)||it.create(e)}return Kt(e)?St.instance(e):"object"==typeof e&&"offset"in e&&"function"==typeof e.offset?e:new wt(e)}const vt={arab:"[٠-٩]",arabext:"[۰-۹]",bali:"[᭐-᭙]",beng:"[০-৯]",deva:"[०-९]",fullwide:"[０-９]",gujr:"[૦-૯]",hanidec:"[〇|一|二|三|四|五|六|七|八|九]",khmr:"[០-៩]",knda:"[೦-೯]",laoo:"[໐-໙]",limb:"[᥆-᥏]",mlym:"[൦-൯]",mong:"[᠐-᠙]",mymr:"[၀-၉]",orya:"[୦-୯]",tamldec:"[௦-௯]",telu:"[౦-౯]",thai:"[๐-๙]",tibt:"[༠-༩]",latn:"\\d"},Et={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},Ct=vt.hanidec.replace(/[\[|\]]/g,"").split("");let Tt={};function Ot({numberingSystem:e},t=""){const n=e||"latn";return Tt[n]||(Tt[n]={}),Tt[n][t]||(Tt[n][t]=new RegExp(`${vt[n]}${t}`)),Tt[n][t]}let Dt,At=()=>Date.now(),Mt="system",It=null,xt=null,Nt=null,Ft=60,Vt=null;class Lt{static get now(){return At}static set now(e){At=e}static set defaultZone(e){Mt=e}static get defaultZone(){return kt(Mt,et.instance)}static get defaultLocale(){return It}static set defaultLocale(e){It=e}static get defaultNumberingSystem(){return xt}static set defaultNumberingSystem(e){xt=e}static get defaultOutputCalendar(){return Nt}static set defaultOutputCalendar(e){Nt=e}static get defaultWeekSettings(){return Vt}static set defaultWeekSettings(e){Vt=on(e)}static get twoDigitCutoffYear(){return Ft}static set twoDigitCutoffYear(e){Ft=e%100}static get throwOnInvalid(){return Dt}static set throwOnInvalid(e){Dt=e}static resetCaches(){gt.resetCache(),it.resetCache(),Ai.resetCache(),Tt={}}}class Rt{constructor(e,t){this.reason=e,this.explanation=t}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}}const Pt=[0,31,59,90,120,151,181,212,243,273,304,334],Wt=[0,31,60,91,121,152,182,213,244,274,305,335];function $t(e,t){return new Rt("unit out of range",`you specified ${t} (of type ${typeof t}) as a ${e}, which is invalid`)}function jt(e,t,n){const r=new Date(Date.UTC(e,t-1,n));e<100&&e>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);const i=r.getUTCDay();return 0===i?7:i}function zt(e,t,n){return n+(mn(e)?Wt:Pt)[t-1]}function _t(e,t){const n=mn(e)?Wt:Pt,r=n.findIndex((e=>e<t));return{month:r+1,day:t-n[r]}}function Zt(e,t){return(e-t+7)%7+1}function Ut(e,t=4,n=1){const{year:r,month:i,day:o}=e,a=zt(r,i,o),s=Zt(jt(r,i,o),n);let l,c=Math.floor((a-s+14-t)/7);return c<1?(l=r-1,c=gn(l,t,n)):c>gn(r,t,n)?(l=r+1,c=1):l=r,{weekYear:l,weekNumber:c,weekday:s,...Cn(e)}}function Ht(e,t=4,n=1){const{weekYear:r,weekNumber:i,weekday:o}=e,a=Zt(jt(r,1,t),n),s=hn(r);let l,c=7*i+o-a-7+t;c<1?(l=r-1,c+=hn(l)):c>s?(l=r+1,c-=hn(r)):l=r;const{month:u,day:d}=_t(l,c);return{year:l,month:u,day:d,...Cn(e)}}function Bt(e){const{year:t,month:n,day:r}=e;return{year:t,ordinal:zt(t,n,r),...Cn(e)}}function Gt(e){const{year:t,ordinal:n}=e,{month:r,day:i}=_t(t,n);return{year:t,month:r,day:i,...Cn(e)}}function qt(e,t){if(!Xt(e.localWeekday)||!Xt(e.localWeekNumber)||!Xt(e.localWeekYear)){if(!Xt(e.weekday)||!Xt(e.weekNumber)||!Xt(e.weekYear))throw new Ee("Cannot mix locale-based week fields with ISO-based week fields");return Xt(e.localWeekday)||(e.weekday=e.localWeekday),Xt(e.localWeekNumber)||(e.weekNumber=e.localWeekNumber),Xt(e.localWeekYear)||(e.weekYear=e.localWeekYear),delete e.localWeekday,delete e.localWeekNumber,delete e.localWeekYear,{minDaysInFirstWeek:t.getMinDaysInFirstWeek(),startOfWeek:t.getStartOfWeek()}}return{minDaysInFirstWeek:4,startOfWeek:1}}function Jt(e){const t=Qt(e.year),n=an(e.month,1,12),r=an(e.day,1,fn(e.year,e.month));return t?n?!r&&$t("day",e.day):$t("month",e.month):$t("year",e.year)}function Yt(e){const{hour:t,minute:n,second:r,millisecond:i}=e,o=an(t,0,23)||24===t&&0===n&&0===r&&0===i,a=an(n,0,59),s=an(r,0,59),l=an(i,0,999);return o?a?s?!l&&$t("millisecond",i):$t("second",r):$t("minute",n):$t("hour",t)}function Xt(e){return void 0===e}function Kt(e){return"number"==typeof e}function Qt(e){return"number"==typeof e&&e%1==0}function en(){try{return"undefined"!=typeof Intl&&!!Intl.RelativeTimeFormat}catch(e){return!1}}function tn(){try{return"undefined"!=typeof Intl&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch(e){return!1}}function nn(e,t,n){if(0!==e.length)return e.reduce(((e,r)=>{const i=[t(r),r];return e&&n(e[0],i[0])===e[0]?e:i}),null)[1]}function rn(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function on(e){if(null==e)return null;if("object"!=typeof e)throw new Te("Week settings must be an object");if(!an(e.firstDay,1,7)||!an(e.minimalDays,1,7)||!Array.isArray(e.weekend)||e.weekend.some((e=>!an(e,1,7))))throw new Te("Invalid week settings");return{firstDay:e.firstDay,minimalDays:e.minimalDays,weekend:Array.from(e.weekend)}}function an(e,t,n){return Qt(e)&&e>=t&&e<=n}function sn(e,t=2){let n;return n=e<0?"-"+(""+-e).padStart(t,"0"):(""+e).padStart(t,"0"),n}function ln(e){return Xt(e)||null===e||""===e?void 0:parseInt(e,10)}function cn(e){return Xt(e)||null===e||""===e?void 0:parseFloat(e)}function un(e){if(!Xt(e)&&null!==e&&""!==e){const t=1e3*parseFloat("0."+e);return Math.floor(t)}}function dn(e,t,n=!1){const r=10**t;return(n?Math.trunc:Math.round)(e*r)/r}function mn(e){return e%4==0&&(e%100!=0||e%400==0)}function hn(e){return mn(e)?366:365}function fn(e,t){const n=(r=t-1)-12*Math.floor(r/12)+1;var r;return 2===n?mn(e+(t-n)/12)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function pn(e){let t=Date.UTC(e.year,e.month-1,e.day,e.hour,e.minute,e.second,e.millisecond);return e.year<100&&e.year>=0&&(t=new Date(t),t.setUTCFullYear(e.year,e.month-1,e.day)),+t}function yn(e,t,n){return-Zt(jt(e,1,t),n)+t-1}function gn(e,t=4,n=1){const r=yn(e,t,n),i=yn(e+1,t,n);return(hn(e)-r+i)/7}function bn(e){return e>99?e:e>Lt.twoDigitCutoffYear?1900+e:2e3+e}function Sn(e,t,n,r=null){const i=new Date(e),o={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(o.timeZone=r);const a={timeZoneName:t,...o},s=new Intl.DateTimeFormat(n,a).formatToParts(i).find((e=>"timezonename"===e.type.toLowerCase()));return s?s.value:null}function wn(e,t){let n=parseInt(e,10);Number.isNaN(n)&&(n=0);const r=parseInt(t,10)||0;return 60*n+(n<0||Object.is(n,-0)?-r:r)}function kn(e){const t=Number(e);if("boolean"==typeof e||""===e||Number.isNaN(t))throw new Te(`Invalid unit value ${e}`);return t}function vn(e,t){const n={};for(const r in e)if(rn(e,r)){const i=e[r];if(null==i)continue;n[t(r)]=kn(i)}return n}function En(e,t){const n=Math.trunc(Math.abs(e/60)),r=Math.trunc(Math.abs(e%60)),i=e>=0?"+":"-";switch(t){case"short":return`${i}${sn(n,2)}:${sn(r,2)}`;case"narrow":return`${i}${n}${r>0?`:${r}`:""}`;case"techie":return`${i}${sn(n,2)}${sn(r,2)}`;default:throw new RangeError(`Value format ${t} is out of range for property format`)}}function Cn(e){return function(e){return["hour","minute","second","millisecond"].reduce(((t,n)=>(t[n]=e[n],t)),{})}(e)}const Tn=["January","February","March","April","May","June","July","August","September","October","November","December"],On=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],Dn=["J","F","M","A","M","J","J","A","S","O","N","D"];function An(e){switch(e){case"narrow":return[...Dn];case"short":return[...On];case"long":return[...Tn];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}const Mn=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],In=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],xn=["M","T","W","T","F","S","S"];function Nn(e){switch(e){case"narrow":return[...xn];case"short":return[...In];case"long":return[...Mn];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}const Fn=["AM","PM"],Vn=["Before Christ","Anno Domini"],Ln=["BC","AD"],Rn=["B","A"];function Pn(e){switch(e){case"narrow":return[...Rn];case"short":return[...Ln];case"long":return[...Vn];default:return null}}function Wn(e,t){let n="";for(const r of e)r.literal?n+=r.val:n+=t(r.val);return n}const $n={D:Ie,DD:xe,DDD:Fe,DDDD:Ve,t:Le,tt:Re,ttt:Pe,tttt:We,T:$e,TT:je,TTT:ze,TTTT:_e,f:Ze,ff:He,fff:qe,ffff:Ye,F:Ue,FF:Be,FFF:Je,FFFF:Xe};class jn{static create(e,t={}){return new jn(e,t)}static parseFormat(e){let t=null,n="",r=!1;const i=[];for(let o=0;o<e.length;o++){const a=e.charAt(o);"'"===a?(n.length>0&&i.push({literal:r||/^\s+$/.test(n),val:n}),t=null,n="",r=!r):r||a===t?n+=a:(n.length>0&&i.push({literal:/^\s+$/.test(n),val:n}),n=a,t=a)}return n.length>0&&i.push({literal:r||/^\s+$/.test(n),val:n}),i}static macroTokenToFormatOpts(e){return $n[e]}constructor(e,t){this.opts=t,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,t){return null===this.systemLoc&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...t}).format()}dtFormatter(e,t={}){return this.loc.dtFormatter(e,{...this.opts,...t})}formatDateTime(e,t){return this.dtFormatter(e,t).format()}formatDateTimeParts(e,t){return this.dtFormatter(e,t).formatToParts()}formatInterval(e,t){return this.dtFormatter(e.start,t).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,t){return this.dtFormatter(e,t).resolvedOptions()}num(e,t=0){if(this.opts.forceSimple)return sn(e,t);const n={...this.opts};return t>0&&(n.padTo=t),this.loc.numberFormatter(n).format(e)}formatDateTimeFromString(e,t){const n="en"===this.loc.listingMode(),r=this.loc.outputCalendar&&"gregory"!==this.loc.outputCalendar,i=(t,n)=>this.loc.extract(e,t,n),o=t=>e.isOffsetFixed&&0===e.offset&&t.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,t.format):"",a=(t,r)=>n?function(e,t){return An(t)[e.month-1]}(e,t):i(r?{month:t}:{month:t,day:"numeric"},"month"),s=(t,r)=>n?function(e,t){return Nn(t)[e.weekday-1]}(e,t):i(r?{weekday:t}:{weekday:t,month:"long",day:"numeric"},"weekday"),l=t=>{const n=jn.macroTokenToFormatOpts(t);return n?this.formatWithSystemDefault(e,n):t},c=t=>n?function(e,t){return Pn(t)[e.year<0?0:1]}(e,t):i({era:t},"era");return Wn(jn.parseFormat(t),(t=>{switch(t){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12==0?12:e.hour%12);case"hh":return this.num(e.hour%12==0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return o({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return o({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return o({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return n?function(e){return Fn[e.hour<12?0:1]}(e):i({hour:"numeric",hourCycle:"h12"},"dayperiod");case"d":return r?i({day:"numeric"},"day"):this.num(e.day);case"dd":return r?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":case"E":return this.num(e.weekday);case"ccc":return s("short",!0);case"cccc":return s("long",!0);case"ccccc":return s("narrow",!0);case"EEE":return s("short",!1);case"EEEE":return s("long",!1);case"EEEEE":return s("narrow",!1);case"L":return r?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return r?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return a("short",!0);case"LLLL":return a("long",!0);case"LLLLL":return a("narrow",!0);case"M":return r?i({month:"numeric"},"month"):this.num(e.month);case"MM":return r?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return a("short",!1);case"MMMM":return a("long",!1);case"MMMMM":return a("narrow",!1);case"y":return r?i({year:"numeric"},"year"):this.num(e.year);case"yy":return r?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return r?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return r?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return c("short");case"GG":return c("long");case"GGGGG":return c("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"n":return this.num(e.localWeekNumber);case"nn":return this.num(e.localWeekNumber,2);case"ii":return this.num(e.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(e.localWeekYear,4);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return l(t)}}))}formatDurationFromString(e,t){const n=e=>{switch(e[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},r=jn.parseFormat(t),i=r.reduce(((e,{literal:t,val:n})=>t?e:e.concat(n)),[]);return Wn(r,(e=>t=>{const r=n(t);return r?this.num(e.get(r),t.length):t})(e.shiftTo(...i.map(n).filter((e=>e)))))}}const zn=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function _n(...e){const t=e.reduce(((e,t)=>e+t.source),"");return RegExp(`^${t}$`)}function Zn(...e){return t=>e.reduce((([e,n,r],i)=>{const[o,a,s]=i(t,r);return[{...e,...o},a||n,s]}),[{},null,1]).slice(0,2)}function Un(e,...t){if(null==e)return[null,null];for(const[n,r]of t){const t=n.exec(e);if(t)return r(t)}return[null,null]}function Hn(...e){return(t,n)=>{const r={};let i;for(i=0;i<e.length;i++)r[e[i]]=ln(t[n+i]);return[r,null,n+i]}}const Bn=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,Gn=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,qn=RegExp(`${Gn.source}(?:${Bn.source}?(?:\\[(${zn.source})\\])?)?`),Jn=RegExp(`(?:T${qn.source})?`),Yn=Hn("weekYear","weekNumber","weekDay"),Xn=Hn("year","ordinal"),Kn=RegExp(`${Gn.source} ?(?:${Bn.source}|(${zn.source}))?`),Qn=RegExp(`(?: ${Kn.source})?`);function er(e,t,n){const r=e[t];return Xt(r)?n:ln(r)}function tr(e,t){return[{hours:er(e,t,0),minutes:er(e,t+1,0),seconds:er(e,t+2,0),milliseconds:un(e[t+3])},null,t+4]}function nr(e,t){const n=!e[t]&&!e[t+1],r=wn(e[t+1],e[t+2]);return[{},n?null:St.instance(r),t+3]}function rr(e,t){return[{},e[t]?it.create(e[t]):null,t+1]}const ir=RegExp(`^T?${Gn.source}$`),or=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function ar(e){const[t,n,r,i,o,a,s,l,c]=e,u="-"===t[0],d=l&&"-"===l[0],m=(e,t=!1)=>void 0!==e&&(t||e&&u)?-e:e;return[{years:m(cn(n)),months:m(cn(r)),weeks:m(cn(i)),days:m(cn(o)),hours:m(cn(a)),minutes:m(cn(s)),seconds:m(cn(l),"-0"===l),milliseconds:m(un(c),d)}]}const sr={GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function lr(e,t,n,r,i,o,a){const s={year:2===t.length?bn(ln(t)):ln(t),month:On.indexOf(n)+1,day:ln(r),hour:ln(i),minute:ln(o)};return a&&(s.second=ln(a)),e&&(s.weekday=e.length>3?Mn.indexOf(e)+1:In.indexOf(e)+1),s}const cr=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function ur(e){const[,t,n,r,i,o,a,s,l,c,u,d]=e,m=lr(t,i,r,n,o,a,s);let h;return h=l?sr[l]:c?0:wn(u,d),[m,new St(h)]}const dr=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,mr=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,hr=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function fr(e){const[,t,n,r,i,o,a,s]=e;return[lr(t,i,r,n,o,a,s),St.utcInstance]}function pr(e){const[,t,n,r,i,o,a,s]=e;return[lr(t,s,n,r,i,o,a),St.utcInstance]}const yr=_n(/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,Jn),gr=_n(/(\d{4})-?W(\d\d)(?:-?(\d))?/,Jn),br=_n(/(\d{4})-?(\d{3})/,Jn),Sr=_n(qn),wr=Zn((function(e,t){return[{year:er(e,t),month:er(e,t+1,1),day:er(e,t+2,1)},null,t+3]}),tr,nr,rr),kr=Zn(Yn,tr,nr,rr),vr=Zn(Xn,tr,nr,rr),Er=Zn(tr,nr,rr),Cr=Zn(tr),Tr=_n(/(\d{4})-(\d\d)-(\d\d)/,Qn),Or=_n(Kn),Dr=Zn(tr,nr,rr),Ar="Invalid Duration",Mr={weeks:{days:7,hours:168,minutes:10080,seconds:604800,milliseconds:6048e5},days:{hours:24,minutes:1440,seconds:86400,milliseconds:864e5},hours:{minutes:60,seconds:3600,milliseconds:36e5},minutes:{seconds:60,milliseconds:6e4},seconds:{milliseconds:1e3}},Ir={years:{quarters:4,months:12,weeks:52,days:365,hours:8760,minutes:525600,seconds:31536e3,milliseconds:31536e6},quarters:{months:3,weeks:13,days:91,hours:2184,minutes:131040,seconds:7862400,milliseconds:78624e5},months:{weeks:4,days:30,hours:720,minutes:43200,seconds:2592e3,milliseconds:2592e6},...Mr},xr={years:{quarters:4,months:12,weeks:52.1775,days:365.2425,hours:8765.82,minutes:525949.2,seconds:525949.2*60,milliseconds:525949.2*60*1e3},quarters:{months:3,weeks:13.044375,days:91.310625,hours:2191.455,minutes:131487.3,seconds:525949.2*60/4,milliseconds:7889237999.999999},months:{weeks:4.3481250000000005,days:30.436875,hours:730.485,minutes:43829.1,seconds:2629746,milliseconds:2629746e3},...Mr},Nr=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],Fr=Nr.slice(0).reverse();function Vr(e,t,n=!1){const r={values:n?t.values:{...e.values,...t.values||{}},loc:e.loc.clone(t.loc),conversionAccuracy:t.conversionAccuracy||e.conversionAccuracy,matrix:t.matrix||e.matrix};return new Pr(r)}function Lr(e,t){let n=t.milliseconds??0;for(const r of Fr.slice(1))t[r]&&(n+=t[r]*e[r].milliseconds);return n}function Rr(e,t){const n=Lr(e,t)<0?-1:1;Nr.reduceRight(((r,i)=>{if(Xt(t[i]))return r;if(r){const o=t[r]*n,a=e[i][r],s=Math.floor(o/a);t[i]+=s*n,t[r]-=s*a*n}return i}),null),Nr.reduce(((n,r)=>{if(Xt(t[r]))return n;if(n){const i=t[n]%1;t[n]-=i,t[r]+=i*e[n][r]}return r}),null)}class Pr{constructor(e){const t="longterm"===e.conversionAccuracy||!1;let n=t?xr:Ir;e.matrix&&(n=e.matrix),this.values=e.values,this.loc=e.loc||gt.create(),this.conversionAccuracy=t?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=n,this.isLuxonDuration=!0}static fromMillis(e,t){return Pr.fromObject({milliseconds:e},t)}static fromObject(e,t={}){if(null==e||"object"!=typeof e)throw new Te("Duration.fromObject: argument expected to be an object, got "+(null===e?"null":typeof e));return new Pr({values:vn(e,Pr.normalizeUnit),loc:gt.fromObject(t),conversionAccuracy:t.conversionAccuracy,matrix:t.matrix})}static fromDurationLike(e){if(Kt(e))return Pr.fromMillis(e);if(Pr.isDuration(e))return e;if("object"==typeof e)return Pr.fromObject(e);throw new Te(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,t){const[n]=function(e){return Un(e,[or,ar])}(e);return n?Pr.fromObject(n,t):Pr.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,t){const[n]=function(e){return Un(e,[ir,Cr])}(e);return n?Pr.fromObject(n,t):Pr.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,t=null){if(!e)throw new Te("need to specify a reason the Duration is invalid");const n=e instanceof Rt?e:new Rt(e,t);if(Lt.throwOnInvalid)throw new ve(n);return new Pr({invalid:n})}static normalizeUnit(e){const t={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e?e.toLowerCase():e];if(!t)throw new Ce(e);return t}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,t={}){const n={...t,floor:!1!==t.round&&!1!==t.floor};return this.isValid?jn.create(this.loc,n).formatDurationFromString(this,e):Ar}toHuman(e={}){if(!this.isValid)return Ar;const t=Nr.map((t=>{const n=this.values[t];return Xt(n)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:t.slice(0,-1)}).format(n)})).filter((e=>e));return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(t)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return 0!==this.years&&(e+=this.years+"Y"),0===this.months&&0===this.quarters||(e+=this.months+3*this.quarters+"M"),0!==this.weeks&&(e+=this.weeks+"W"),0!==this.days&&(e+=this.days+"D"),0===this.hours&&0===this.minutes&&0===this.seconds&&0===this.milliseconds||(e+="T"),0!==this.hours&&(e+=this.hours+"H"),0!==this.minutes&&(e+=this.minutes+"M"),0===this.seconds&&0===this.milliseconds||(e+=dn(this.seconds+this.milliseconds/1e3,3)+"S"),"P"===e&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;const t=this.toMillis();return t<0||t>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},Ai.fromMillis(t,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?Lr(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;const t=Pr.fromDurationLike(e),n={};for(const e of Nr)(rn(t.values,e)||rn(this.values,e))&&(n[e]=t.get(e)+this.get(e));return Vr(this,{values:n},!0)}minus(e){if(!this.isValid)return this;const t=Pr.fromDurationLike(e);return this.plus(t.negate())}mapUnits(e){if(!this.isValid)return this;const t={};for(const n of Object.keys(this.values))t[n]=kn(e(this.values[n],n));return Vr(this,{values:t},!0)}get(e){return this[Pr.normalizeUnit(e)]}set(e){return this.isValid?Vr(this,{values:{...this.values,...vn(e,Pr.normalizeUnit)}}):this}reconfigure({locale:e,numberingSystem:t,conversionAccuracy:n,matrix:r}={}){return Vr(this,{loc:this.loc.clone({locale:e,numberingSystem:t}),matrix:r,conversionAccuracy:n})}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;const e=this.toObject();return Rr(this.matrix,e),Vr(this,{values:e},!0)}rescale(){return this.isValid?Vr(this,{values:function(e){const t={};for(const[n,r]of Object.entries(e))0!==r&&(t[n]=r);return t}(this.normalize().shiftToAll().toObject())},!0):this}shiftTo(...e){if(!this.isValid)return this;if(0===e.length)return this;e=e.map((e=>Pr.normalizeUnit(e)));const t={},n={},r=this.toObject();let i;for(const o of Nr)if(e.indexOf(o)>=0){i=o;let e=0;for(const t in n)e+=this.matrix[t][o]*n[t],n[t]=0;Kt(r[o])&&(e+=r[o]);const a=Math.trunc(e);t[o]=a,n[o]=(1e3*e-1e3*a)/1e3}else Kt(r[o])&&(n[o]=r[o]);for(const e in n)0!==n[e]&&(t[i]+=e===i?n[e]:n[e]/this.matrix[i][e]);return Rr(this.matrix,t),Vr(this,{values:t},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;const e={};for(const t of Object.keys(this.values))e[t]=0===this.values[t]?0:-this.values[t];return Vr(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid)return!1;if(!this.loc.equals(e.loc))return!1;for(const r of Nr)if(t=this.values[r],n=e.values[r],!(void 0===t||0===t?void 0===n||0===n:t===n))return!1;var t,n;return!0}}const Wr="Invalid Interval";class $r{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,t=null){if(!e)throw new Te("need to specify a reason the Interval is invalid");const n=e instanceof Rt?e:new Rt(e,t);if(Lt.throwOnInvalid)throw new ke(n);return new $r({invalid:n})}static fromDateTimes(e,t){const n=Mi(e),r=Mi(t),i=function(e,t){return e&&e.isValid?t&&t.isValid?t<e?$r.invalid("end before start",`The end of an interval must be after its start, but you had start=${e.toISO()} and end=${t.toISO()}`):null:$r.invalid("missing or invalid end"):$r.invalid("missing or invalid start")}(n,r);return null==i?new $r({start:n,end:r}):i}static after(e,t){const n=Pr.fromDurationLike(t),r=Mi(e);return $r.fromDateTimes(r,r.plus(n))}static before(e,t){const n=Pr.fromDurationLike(t),r=Mi(e);return $r.fromDateTimes(r.minus(n),r)}static fromISO(e,t){const[n,r]=(e||"").split("/",2);if(n&&r){let e,i,o,a;try{e=Ai.fromISO(n,t),i=e.isValid}catch(r){i=!1}try{o=Ai.fromISO(r,t),a=o.isValid}catch(r){a=!1}if(i&&a)return $r.fromDateTimes(e,o);if(i){const n=Pr.fromISO(r,t);if(n.isValid)return $r.after(e,n)}else if(a){const e=Pr.fromISO(n,t);if(e.isValid)return $r.before(o,e)}}return $r.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return null===this.invalidReason}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds",t){if(!this.isValid)return NaN;const n=this.start.startOf(e,t);let r;return r=t?.useLocaleWeeks?this.end.reconfigure({locale:n.locale}):this.end,r=r.startOf(e,t),Math.floor(r.diff(n,e).get(e))+(r.valueOf()!==this.end.valueOf())}hasSame(e){return!!this.isValid&&(this.isEmpty()||this.e.minus(1).hasSame(this.s,e))}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return!!this.isValid&&this.s>e}isBefore(e){return!!this.isValid&&this.e<=e}contains(e){return!!this.isValid&&this.s<=e&&this.e>e}set({start:e,end:t}={}){return this.isValid?$r.fromDateTimes(e||this.s,t||this.e):this}splitAt(...e){if(!this.isValid)return[];const t=e.map(Mi).filter((e=>this.contains(e))).sort(((e,t)=>e.toMillis()-t.toMillis())),n=[];let{s:r}=this,i=0;for(;r<this.e;){const e=t[i]||this.e,o=+e>+this.e?this.e:e;n.push($r.fromDateTimes(r,o)),r=o,i+=1}return n}splitBy(e){const t=Pr.fromDurationLike(e);if(!this.isValid||!t.isValid||0===t.as("milliseconds"))return[];let n,{s:r}=this,i=1;const o=[];for(;r<this.e;){const e=this.start.plus(t.mapUnits((e=>e*i)));n=+e>+this.e?this.e:e,o.push($r.fromDateTimes(r,n)),r=n,i+=1}return o}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return!!this.isValid&&+this.e==+e.s}abutsEnd(e){return!!this.isValid&&+e.e==+this.s}engulfs(e){return!!this.isValid&&this.s<=e.s&&this.e>=e.e}equals(e){return!(!this.isValid||!e.isValid)&&this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;const t=this.s>e.s?this.s:e.s,n=this.e<e.e?this.e:e.e;return t>=n?null:$r.fromDateTimes(t,n)}union(e){if(!this.isValid)return this;const t=this.s<e.s?this.s:e.s,n=this.e>e.e?this.e:e.e;return $r.fromDateTimes(t,n)}static merge(e){const[t,n]=e.sort(((e,t)=>e.s-t.s)).reduce((([e,t],n)=>t?t.overlaps(n)||t.abutsStart(n)?[e,t.union(n)]:[e.concat([t]),n]:[e,n]),[[],null]);return n&&t.push(n),t}static xor(e){let t=null,n=0;const r=[],i=e.map((e=>[{time:e.s,type:"s"},{time:e.e,type:"e"}])),o=Array.prototype.concat(...i).sort(((e,t)=>e.time-t.time));for(const e of o)n+="s"===e.type?1:-1,1===n?t=e.time:(t&&+t!=+e.time&&r.push($r.fromDateTimes(t,e.time)),t=null);return $r.merge(r)}difference(...e){return $r.xor([this].concat(e)).map((e=>this.intersection(e))).filter((e=>e&&!e.isEmpty()))}toString(){return this.isValid?`[${this.s.toISO()} – ${this.e.toISO()})`:Wr}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(e=Ie,t={}){return this.isValid?jn.create(this.s.loc.clone(t),e).formatInterval(this):Wr}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:Wr}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:Wr}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:Wr}toFormat(e,{separator:t=" – "}={}){return this.isValid?`${this.s.toFormat(e)}${t}${this.e.toFormat(e)}`:Wr}toDuration(e,t){return this.isValid?this.e.diff(this.s,e,t):Pr.invalid(this.invalidReason)}mapEndpoints(e){return $r.fromDateTimes(e(this.s),e(this.e))}}class jr{static hasDST(e=Lt.defaultZone){const t=Ai.now().setZone(e).set({month:12});return!e.isUniversal&&t.offset!==t.set({month:6}).offset}static isValidIANAZone(e){return it.isValidZone(e)}static normalizeZone(e){return kt(e,Lt.defaultZone)}static getStartOfWeek({locale:e=null,locObj:t=null}={}){return(t||gt.create(e)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:e=null,locObj:t=null}={}){return(t||gt.create(e)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:e=null,locObj:t=null}={}){return(t||gt.create(e)).getWeekendDays().slice()}static months(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null,outputCalendar:i="gregory"}={}){return(r||gt.create(t,n,i)).months(e)}static monthsFormat(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null,outputCalendar:i="gregory"}={}){return(r||gt.create(t,n,i)).months(e,!0)}static weekdays(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null}={}){return(r||gt.create(t,n,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:t=null,numberingSystem:n=null,locObj:r=null}={}){return(r||gt.create(t,n,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return gt.create(e).meridiems()}static eras(e="short",{locale:t=null}={}){return gt.create(t,null,"gregory").eras(e)}static features(){return{relative:en(),localeWeek:tn()}}}function zr(e,t){const n=e=>e.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(t)-n(e);return Math.floor(Pr.fromMillis(r).as("days"))}function _r(e,t=e=>e){return{regex:e,deser:([e])=>t(function(e){let t=parseInt(e,10);if(isNaN(t)){t="";for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);if(-1!==e[n].search(vt.hanidec))t+=Ct.indexOf(e[n]);else for(const e in Et){const[n,i]=Et[e];r>=n&&r<=i&&(t+=r-n)}}return parseInt(t,10)}return t}(e))}}const Zr=`[ ${String.fromCharCode(160)}]`,Ur=new RegExp(Zr,"g");function Hr(e){return e.replace(/\./g,"\\.?").replace(Ur,Zr)}function Br(e){return e.replace(/\./g,"").replace(Ur," ").toLowerCase()}function Gr(e,t){return null===e?null:{regex:RegExp(e.map(Hr).join("|")),deser:([n])=>e.findIndex((e=>Br(n)===Br(e)))+t}}function qr(e,t){return{regex:e,deser:([,e,t])=>wn(e,t),groups:t}}function Jr(e){return{regex:e,deser:([e])=>e}}const Yr={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};let Xr=null;function Kr(e,t){return Array.prototype.concat(...e.map((e=>function(e,t){if(e.literal)return e;const n=ti(jn.macroTokenToFormatOpts(e.val),t);return null==n||n.includes(void 0)?e:n}(e,t))))}class Qr{constructor(e,t){if(this.locale=e,this.format=t,this.tokens=Kr(jn.parseFormat(t),e),this.units=this.tokens.map((t=>function(e,t){const n=Ot(t),r=Ot(t,"{2}"),i=Ot(t,"{3}"),o=Ot(t,"{4}"),a=Ot(t,"{6}"),s=Ot(t,"{1,2}"),l=Ot(t,"{1,3}"),c=Ot(t,"{1,6}"),u=Ot(t,"{1,9}"),d=Ot(t,"{2,4}"),m=Ot(t,"{4,6}"),h=e=>{return{regex:RegExp((t=e.val,t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"))),deser:([e])=>e,literal:!0};var t},f=(f=>{if(e.literal)return h(f);switch(f.val){case"G":return Gr(t.eras("short"),0);case"GG":return Gr(t.eras("long"),0);case"y":return _r(c);case"yy":case"kk":return _r(d,bn);case"yyyy":case"kkkk":return _r(o);case"yyyyy":return _r(m);case"yyyyyy":return _r(a);case"M":case"L":case"d":case"H":case"h":case"m":case"q":case"s":case"W":return _r(s);case"MM":case"LL":case"dd":case"HH":case"hh":case"mm":case"qq":case"ss":case"WW":return _r(r);case"MMM":return Gr(t.months("short",!0),1);case"MMMM":return Gr(t.months("long",!0),1);case"LLL":return Gr(t.months("short",!1),1);case"LLLL":return Gr(t.months("long",!1),1);case"o":case"S":return _r(l);case"ooo":case"SSS":return _r(i);case"u":return Jr(u);case"uu":return Jr(s);case"uuu":case"E":case"c":return _r(n);case"a":return Gr(t.meridiems(),0);case"EEE":return Gr(t.weekdays("short",!1),1);case"EEEE":return Gr(t.weekdays("long",!1),1);case"ccc":return Gr(t.weekdays("short",!0),1);case"cccc":return Gr(t.weekdays("long",!0),1);case"Z":case"ZZ":return qr(new RegExp(`([+-]${s.source})(?::(${r.source}))?`),2);case"ZZZ":return qr(new RegExp(`([+-]${s.source})(${r.source})?`),2);case"z":return Jr(/[a-z_+-/]{1,256}?/i);case" ":return Jr(/[^\S\n\r]/);default:return h(f)}})(e)||{invalidReason:"missing Intl.DateTimeFormat.formatToParts support"};return f.token=e,f}(t,e))),this.disqualifyingUnit=this.units.find((e=>e.invalidReason)),!this.disqualifyingUnit){const[e,t]=[`^${(n=this.units).map((e=>e.regex)).reduce(((e,t)=>`${e}(${t.source})`),"")}$`,n];this.regex=RegExp(e,"i"),this.handlers=t}var n}explainFromTokens(e){if(this.isValid){const[t,n]=function(e,t,n){const r=e.match(t);if(r){const e={};let t=1;for(const i in n)if(rn(n,i)){const o=n[i],a=o.groups?o.groups+1:1;!o.literal&&o.token&&(e[o.token.val[0]]=o.deser(r.slice(t,t+a))),t+=a}return[r,e]}return[r,{}]}(e,this.regex,this.handlers),[r,i,o]=n?function(e){let t,n=null;return Xt(e.z)||(n=it.create(e.z)),Xt(e.Z)||(n||(n=new St(e.Z)),t=e.Z),Xt(e.q)||(e.M=3*(e.q-1)+1),Xt(e.h)||(e.h<12&&1===e.a?e.h+=12:12===e.h&&0===e.a&&(e.h=0)),0===e.G&&e.y&&(e.y=-e.y),Xt(e.u)||(e.S=un(e.u)),[Object.keys(e).reduce(((t,n)=>{const r=(e=>{switch(e){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}})(n);return r&&(t[r]=e[n]),t}),{}),n,t]}(n):[null,null,void 0];if(rn(n,"a")&&rn(n,"H"))throw new Ee("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:this.tokens,regex:this.regex,rawMatches:t,matches:n,result:r,zone:i,specificOffset:o}}return{input:e,tokens:this.tokens,invalidReason:this.invalidReason}}get isValid(){return!this.disqualifyingUnit}get invalidReason(){return this.disqualifyingUnit?this.disqualifyingUnit.invalidReason:null}}function ei(e,t,n){return new Qr(e,n).explainFromTokens(t)}function ti(e,t){if(!e)return null;const n=jn.create(t,e).dtFormatter((Xr||(Xr=Ai.fromMillis(1555555555555)),Xr)),r=n.formatToParts(),i=n.resolvedOptions();return r.map((t=>function(e,t,n){const{type:r,value:i}=e;if("literal"===r){const e=/^\s+$/.test(i);return{literal:!e,val:e?" ":i}}const o=t[r];let a=r;"hour"===r&&(a=null!=t.hour12?t.hour12?"hour12":"hour24":null!=t.hourCycle?"h11"===t.hourCycle||"h12"===t.hourCycle?"hour12":"hour24":n.hour12?"hour12":"hour24");let s=Yr[a];if("object"==typeof s&&(s=s[o]),s)return{literal:!1,val:s}}(t,e,i)))}const ni="Invalid DateTime",ri=864e13;function ii(e){return new Rt("unsupported zone",`the zone "${e.name}" is not supported`)}function oi(e){return null===e.weekData&&(e.weekData=Ut(e.c)),e.weekData}function ai(e){return null===e.localWeekData&&(e.localWeekData=Ut(e.c,e.loc.getMinDaysInFirstWeek(),e.loc.getStartOfWeek())),e.localWeekData}function si(e,t){const n={ts:e.ts,zone:e.zone,c:e.c,o:e.o,loc:e.loc,invalid:e.invalid};return new Ai({...n,...t,old:n})}function li(e,t,n){let r=e-60*t*1e3;const i=n.offset(r);if(t===i)return[r,t];r-=60*(i-t)*1e3;const o=n.offset(r);return i===o?[r,i]:[e-60*Math.min(i,o)*1e3,Math.max(i,o)]}function ci(e,t){const n=new Date(e+=60*t*1e3);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function ui(e,t,n){return li(pn(e),t,n)}function di(e,t){const n=e.o,r=e.c.year+Math.trunc(t.years),i=e.c.month+Math.trunc(t.months)+3*Math.trunc(t.quarters),o={...e.c,year:r,month:i,day:Math.min(e.c.day,fn(r,i))+Math.trunc(t.days)+7*Math.trunc(t.weeks)},a=Pr.fromObject({years:t.years-Math.trunc(t.years),quarters:t.quarters-Math.trunc(t.quarters),months:t.months-Math.trunc(t.months),weeks:t.weeks-Math.trunc(t.weeks),days:t.days-Math.trunc(t.days),hours:t.hours,minutes:t.minutes,seconds:t.seconds,milliseconds:t.milliseconds}).as("milliseconds"),s=pn(o);let[l,c]=li(s,n,e.zone);return 0!==a&&(l+=a,c=e.zone.offset(l)),{ts:l,o:c}}function mi(e,t,n,r,i,o){const{setZone:a,zone:s}=n;if(e&&0!==Object.keys(e).length||t){const r=t||s,i=Ai.fromObject(e,{...n,zone:r,specificOffset:o});return a?i:i.setZone(s)}return Ai.invalid(new Rt("unparsable",`the input "${i}" can't be parsed as ${r}`))}function hi(e,t,n=!0){return e.isValid?jn.create(gt.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(e,t):null}function fi(e,t){const n=e.c.year>9999||e.c.year<0;let r="";return n&&e.c.year>=0&&(r+="+"),r+=sn(e.c.year,n?6:4),t?(r+="-",r+=sn(e.c.month),r+="-",r+=sn(e.c.day)):(r+=sn(e.c.month),r+=sn(e.c.day)),r}function pi(e,t,n,r,i,o){let a=sn(e.c.hour);return t?(a+=":",a+=sn(e.c.minute),0===e.c.millisecond&&0===e.c.second&&n||(a+=":")):a+=sn(e.c.minute),0===e.c.millisecond&&0===e.c.second&&n||(a+=sn(e.c.second),0===e.c.millisecond&&r||(a+=".",a+=sn(e.c.millisecond,3))),i&&(e.isOffsetFixed&&0===e.offset&&!o?a+="Z":e.o<0?(a+="-",a+=sn(Math.trunc(-e.o/60)),a+=":",a+=sn(Math.trunc(-e.o%60))):(a+="+",a+=sn(Math.trunc(e.o/60)),a+=":",a+=sn(Math.trunc(e.o%60)))),o&&(a+="["+e.zone.ianaName+"]"),a}const yi={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},gi={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},bi={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Si=["year","month","day","hour","minute","second","millisecond"],wi=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],ki=["year","ordinal","hour","minute","second","millisecond"];function vi(e){switch(e.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return function(e){const t={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[e.toLowerCase()];if(!t)throw new Ce(e);return t}(e)}}function Ei(e,t){const n=kt(t.zone,Lt.defaultZone);if(!n.isValid)return Ai.invalid(ii(n));const r=gt.fromObject(t);let i,o;if(Xt(e.year))i=Lt.now();else{for(const t of Si)Xt(e[t])&&(e[t]=yi[t]);const t=Jt(e)||Yt(e);if(t)return Ai.invalid(t);const r=function(e){return Di[e]||(void 0===Oi&&(Oi=Lt.now()),Di[e]=e.offset(Oi)),Di[e]}(n);[i,o]=ui(e,r,n)}return new Ai({ts:i,zone:n,loc:r,o})}function Ci(e,t,n){const r=!!Xt(n.round)||n.round,i=(e,i)=>(e=dn(e,r||n.calendary?0:2,!0),t.loc.clone(n).relFormatter(n).format(e,i)),o=r=>n.calendary?t.hasSame(e,r)?0:t.startOf(r).diff(e.startOf(r),r).get(r):t.diff(e,r).get(r);if(n.unit)return i(o(n.unit),n.unit);for(const e of n.units){const t=o(e);if(Math.abs(t)>=1)return i(t,e)}return i(e>t?-0:0,n.units[n.units.length-1])}function Ti(e){let t,n={};return e.length>0&&"object"==typeof e[e.length-1]?(n=e[e.length-1],t=Array.from(e).slice(0,e.length-1)):t=Array.from(e),[n,t]}let Oi,Di={};class Ai{constructor(e){const t=e.zone||Lt.defaultZone;let n=e.invalid||(Number.isNaN(e.ts)?new Rt("invalid input"):null)||(t.isValid?null:ii(t));this.ts=Xt(e.ts)?Lt.now():e.ts;let r=null,i=null;if(!n)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(t))[r,i]=[e.old.c,e.old.o];else{const o=Kt(e.o)&&!e.old?e.o:t.offset(this.ts);r=ci(this.ts,o),n=Number.isNaN(r.year)?new Rt("invalid input"):null,r=n?null:r,i=n?null:o}this._zone=t,this.loc=e.loc||gt.create(),this.invalid=n,this.weekData=null,this.localWeekData=null,this.c=r,this.o=i,this.isLuxonDateTime=!0}static now(){return new Ai({})}static local(){const[e,t]=Ti(arguments),[n,r,i,o,a,s,l]=t;return Ei({year:n,month:r,day:i,hour:o,minute:a,second:s,millisecond:l},e)}static utc(){const[e,t]=Ti(arguments),[n,r,i,o,a,s,l]=t;return e.zone=St.utcInstance,Ei({year:n,month:r,day:i,hour:o,minute:a,second:s,millisecond:l},e)}static fromJSDate(e,t={}){const n=(r=e,"[object Date]"===Object.prototype.toString.call(r)?e.valueOf():NaN);var r;if(Number.isNaN(n))return Ai.invalid("invalid input");const i=kt(t.zone,Lt.defaultZone);return i.isValid?new Ai({ts:n,zone:i,loc:gt.fromObject(t)}):Ai.invalid(ii(i))}static fromMillis(e,t={}){if(Kt(e))return e<-ri||e>ri?Ai.invalid("Timestamp out of range"):new Ai({ts:e,zone:kt(t.zone,Lt.defaultZone),loc:gt.fromObject(t)});throw new Te(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,t={}){if(Kt(e))return new Ai({ts:1e3*e,zone:kt(t.zone,Lt.defaultZone),loc:gt.fromObject(t)});throw new Te("fromSeconds requires a numerical input")}static fromObject(e,t={}){e=e||{};const n=kt(t.zone,Lt.defaultZone);if(!n.isValid)return Ai.invalid(ii(n));const r=gt.fromObject(t),i=vn(e,vi),{minDaysInFirstWeek:o,startOfWeek:a}=qt(i,r),s=Lt.now(),l=Xt(t.specificOffset)?n.offset(s):t.specificOffset,c=!Xt(i.ordinal),u=!Xt(i.year),d=!Xt(i.month)||!Xt(i.day),m=u||d,h=i.weekYear||i.weekNumber;if((m||c)&&h)throw new Ee("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(d&&c)throw new Ee("Can't mix ordinal dates with month/day");const f=h||i.weekday&&!m;let p,y,g=ci(s,l);f?(p=wi,y=gi,g=Ut(g,o,a)):c?(p=ki,y=bi,g=Bt(g)):(p=Si,y=yi);let b=!1;for(const e of p)Xt(i[e])?i[e]=b?y[e]:g[e]:b=!0;const S=f?function(e,t=4,n=1){const r=Qt(e.weekYear),i=an(e.weekNumber,1,gn(e.weekYear,t,n)),o=an(e.weekday,1,7);return r?i?!o&&$t("weekday",e.weekday):$t("week",e.weekNumber):$t("weekYear",e.weekYear)}(i,o,a):c?function(e){const t=Qt(e.year),n=an(e.ordinal,1,hn(e.year));return t?!n&&$t("ordinal",e.ordinal):$t("year",e.year)}(i):Jt(i),w=S||Yt(i);if(w)return Ai.invalid(w);const k=f?Ht(i,o,a):c?Gt(i):i,[v,E]=ui(k,l,n),C=new Ai({ts:v,zone:n,o:E,loc:r});return i.weekday&&m&&e.weekday!==C.weekday?Ai.invalid("mismatched weekday",`you can't specify both a weekday of ${i.weekday} and a date of ${C.toISO()}`):C.isValid?C:Ai.invalid(C.invalid)}static fromISO(e,t={}){const[n,r]=function(e){return Un(e,[yr,wr],[gr,kr],[br,vr],[Sr,Er])}(e);return mi(n,r,t,"ISO 8601",e)}static fromRFC2822(e,t={}){const[n,r]=function(e){return Un(function(e){return e.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}(e),[cr,ur])}(e);return mi(n,r,t,"RFC 2822",e)}static fromHTTP(e,t={}){const[n,r]=function(e){return Un(e,[dr,fr],[mr,fr],[hr,pr])}(e);return mi(n,r,t,"HTTP",t)}static fromFormat(e,t,n={}){if(Xt(e)||Xt(t))throw new Te("fromFormat requires an input string and a format");const{locale:r=null,numberingSystem:i=null}=n,o=gt.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0}),[a,s,l,c]=function(e,t,n){const{result:r,zone:i,specificOffset:o,invalidReason:a}=ei(e,t,n);return[r,i,o,a]}(o,e,t);return c?Ai.invalid(c):mi(a,s,n,`format ${t}`,e,l)}static fromString(e,t,n={}){return Ai.fromFormat(e,t,n)}static fromSQL(e,t={}){const[n,r]=function(e){return Un(e,[Tr,wr],[Or,Dr])}(e);return mi(n,r,t,"SQL",e)}static invalid(e,t=null){if(!e)throw new Te("need to specify a reason the DateTime is invalid");const n=e instanceof Rt?e:new Rt(e,t);if(Lt.throwOnInvalid)throw new we(n);return new Ai({invalid:n})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,t={}){const n=ti(e,gt.fromObject(t));return n?n.map((e=>e?e.val:null)).join(""):null}static expandFormat(e,t={}){return Kr(jn.parseFormat(e),gt.fromObject(t)).map((e=>e.val)).join("")}static resetCache(){Oi=void 0,Di={}}get(e){return this[e]}get isValid(){return null===this.invalid}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?oi(this).weekYear:NaN}get weekNumber(){return this.isValid?oi(this).weekNumber:NaN}get weekday(){return this.isValid?oi(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?ai(this).weekday:NaN}get localWeekNumber(){return this.isValid?ai(this).weekNumber:NaN}get localWeekYear(){return this.isValid?ai(this).weekYear:NaN}get ordinal(){return this.isValid?Bt(this.c).ordinal:NaN}get monthShort(){return this.isValid?jr.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?jr.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?jr.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?jr.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return!this.isOffsetFixed&&(this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset)}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];const e=864e5,t=6e4,n=pn(this.c),r=this.zone.offset(n-e),i=this.zone.offset(n+e),o=this.zone.offset(n-r*t),a=this.zone.offset(n-i*t);if(o===a)return[this];const s=n-o*t,l=n-a*t,c=ci(s,o),u=ci(l,a);return c.hour===u.hour&&c.minute===u.minute&&c.second===u.second&&c.millisecond===u.millisecond?[si(this,{ts:s}),si(this,{ts:l})]:[this]}get isInLeapYear(){return mn(this.year)}get daysInMonth(){return fn(this.year,this.month)}get daysInYear(){return this.isValid?hn(this.year):NaN}get weeksInWeekYear(){return this.isValid?gn(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?gn(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(e={}){const{locale:t,numberingSystem:n,calendar:r}=jn.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:t,numberingSystem:n,outputCalendar:r}}toUTC(e=0,t={}){return this.setZone(St.instance(e),t)}toLocal(){return this.setZone(Lt.defaultZone)}setZone(e,{keepLocalTime:t=!1,keepCalendarTime:n=!1}={}){if((e=kt(e,Lt.defaultZone)).equals(this.zone))return this;if(e.isValid){let r=this.ts;if(t||n){const t=e.offset(this.ts),n=this.toObject();[r]=ui(n,t,e)}return si(this,{ts:r,zone:e})}return Ai.invalid(ii(e))}reconfigure({locale:e,numberingSystem:t,outputCalendar:n}={}){return si(this,{loc:this.loc.clone({locale:e,numberingSystem:t,outputCalendar:n})})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;const t=vn(e,vi),{minDaysInFirstWeek:n,startOfWeek:r}=qt(t,this.loc),i=!Xt(t.weekYear)||!Xt(t.weekNumber)||!Xt(t.weekday),o=!Xt(t.ordinal),a=!Xt(t.year),s=!Xt(t.month)||!Xt(t.day),l=a||s,c=t.weekYear||t.weekNumber;if((l||o)&&c)throw new Ee("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(s&&o)throw new Ee("Can't mix ordinal dates with month/day");let u;i?u=Ht({...Ut(this.c,n,r),...t},n,r):Xt(t.ordinal)?(u={...this.toObject(),...t},Xt(t.day)&&(u.day=Math.min(fn(u.year,u.month),u.day))):u=Gt({...Bt(this.c),...t});const[d,m]=ui(u,this.o,this.zone);return si(this,{ts:d,o:m})}plus(e){return this.isValid?si(this,di(this,Pr.fromDurationLike(e))):this}minus(e){return this.isValid?si(this,di(this,Pr.fromDurationLike(e).negate())):this}startOf(e,{useLocaleWeeks:t=!1}={}){if(!this.isValid)return this;const n={},r=Pr.normalizeUnit(e);switch(r){case"years":n.month=1;case"quarters":case"months":n.day=1;case"weeks":case"days":n.hour=0;case"hours":n.minute=0;case"minutes":n.second=0;case"seconds":n.millisecond=0}if("weeks"===r)if(t){const e=this.loc.getStartOfWeek(),{weekday:t}=this;t<e&&(n.weekNumber=this.weekNumber-1),n.weekday=e}else n.weekday=1;if("quarters"===r){const e=Math.ceil(this.month/3);n.month=3*(e-1)+1}return this.set(n)}endOf(e,t){return this.isValid?this.plus({[e]:1}).startOf(e,t).minus(1):this}toFormat(e,t={}){return this.isValid?jn.create(this.loc.redefaultToEN(t)).formatDateTimeFromString(this,e):ni}toLocaleString(e=Ie,t={}){return this.isValid?jn.create(this.loc.clone(t),e).formatDateTime(this):ni}toLocaleParts(e={}){return this.isValid?jn.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:t=!1,suppressMilliseconds:n=!1,includeOffset:r=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;const o="extended"===e;let a=fi(this,o);return a+="T",a+=pi(this,o,t,n,r,i),a}toISODate({format:e="extended"}={}){return this.isValid?fi(this,"extended"===e):null}toISOWeekDate(){return hi(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:t=!1,includeOffset:n=!0,includePrefix:r=!1,extendedZone:i=!1,format:o="extended"}={}){return this.isValid?(r?"T":"")+pi(this,"extended"===o,t,e,n,i):null}toRFC2822(){return hi(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return hi(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?fi(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:t=!1,includeOffsetSpace:n=!0}={}){let r="HH:mm:ss.SSS";return(t||e)&&(n&&(r+=" "),t?r+="z":e&&(r+="ZZ")),hi(this,r,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():ni}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};const t={...this.c};return e.includeConfig&&(t.outputCalendar=this.outputCalendar,t.numberingSystem=this.loc.numberingSystem,t.locale=this.loc.locale),t}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,t="milliseconds",n={}){if(!this.isValid||!e.isValid)return Pr.invalid("created by diffing an invalid DateTime");const r={locale:this.locale,numberingSystem:this.numberingSystem,...n},i=(s=t,Array.isArray(s)?s:[s]).map(Pr.normalizeUnit),o=e.valueOf()>this.valueOf(),a=function(e,t,n,r){let[i,o,a,s]=function(e,t,n){const r=[["years",(e,t)=>t.year-e.year],["quarters",(e,t)=>t.quarter-e.quarter+4*(t.year-e.year)],["months",(e,t)=>t.month-e.month+12*(t.year-e.year)],["weeks",(e,t)=>{const n=zr(e,t);return(n-n%7)/7}],["days",zr]],i={},o=e;let a,s;for(const[l,c]of r)n.indexOf(l)>=0&&(a=l,i[l]=c(e,t),s=o.plus(i),s>t?(i[l]--,(e=o.plus(i))>t&&(s=e,i[l]--,e=o.plus(i))):e=s);return[e,i,s,a]}(e,t,n);const l=t-i,c=n.filter((e=>["hours","minutes","seconds","milliseconds"].indexOf(e)>=0));0===c.length&&(a<t&&(a=i.plus({[s]:1})),a!==i&&(o[s]=(o[s]||0)+l/(a-i)));const u=Pr.fromObject(o,r);return c.length>0?Pr.fromMillis(l,r).shiftTo(...c).plus(u):u}(o?this:e,o?e:this,i,r);var s;return o?a.negate():a}diffNow(e="milliseconds",t={}){return this.diff(Ai.now(),e,t)}until(e){return this.isValid?$r.fromDateTimes(this,e):this}hasSame(e,t,n){if(!this.isValid)return!1;const r=e.valueOf(),i=this.setZone(e.zone,{keepLocalTime:!0});return i.startOf(t,n)<=r&&r<=i.endOf(t,n)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;const t=e.base||Ai.fromObject({},{zone:this.zone}),n=e.padding?this<t?-e.padding:e.padding:0;let r=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(r=e.unit,i=void 0),Ci(t,this.plus(n),{...e,numeric:"always",units:r,unit:i})}toRelativeCalendar(e={}){return this.isValid?Ci(e.base||Ai.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(Ai.isDateTime))throw new Te("min requires all arguments be DateTimes");return nn(e,(e=>e.valueOf()),Math.min)}static max(...e){if(!e.every(Ai.isDateTime))throw new Te("max requires all arguments be DateTimes");return nn(e,(e=>e.valueOf()),Math.max)}static fromFormatExplain(e,t,n={}){const{locale:r=null,numberingSystem:i=null}=n;return ei(gt.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0}),e,t)}static fromStringExplain(e,t,n={}){return Ai.fromFormatExplain(e,t,n)}static buildFormatParser(e,t={}){const{locale:n=null,numberingSystem:r=null}=t,i=gt.fromOpts({locale:n,numberingSystem:r,defaultToEN:!0});return new Qr(i,e)}static fromFormatParser(e,t,n={}){if(Xt(e)||Xt(t))throw new Te("fromFormatParser requires an input string and a format parser");const{locale:r=null,numberingSystem:i=null}=n,o=gt.fromOpts({locale:r,numberingSystem:i,defaultToEN:!0});if(!o.equals(t.locale))throw new Te(`fromFormatParser called with a locale of ${o}, but the format parser was created for ${t.locale}`);const{result:a,zone:s,specificOffset:l,invalidReason:c}=t.explainFromTokens(e);return c?Ai.invalid(c):mi(a,s,n,`format ${t.format}`,e,l)}static get DATE_SHORT(){return Ie}static get DATE_MED(){return xe}static get DATE_MED_WITH_WEEKDAY(){return Ne}static get DATE_FULL(){return Fe}static get DATE_HUGE(){return Ve}static get TIME_SIMPLE(){return Le}static get TIME_WITH_SECONDS(){return Re}static get TIME_WITH_SHORT_OFFSET(){return Pe}static get TIME_WITH_LONG_OFFSET(){return We}static get TIME_24_SIMPLE(){return $e}static get TIME_24_WITH_SECONDS(){return je}static get TIME_24_WITH_SHORT_OFFSET(){return ze}static get TIME_24_WITH_LONG_OFFSET(){return _e}static get DATETIME_SHORT(){return Ze}static get DATETIME_SHORT_WITH_SECONDS(){return Ue}static get DATETIME_MED(){return He}static get DATETIME_MED_WITH_SECONDS(){return Be}static get DATETIME_MED_WITH_WEEKDAY(){return Ge}static get DATETIME_FULL(){return qe}static get DATETIME_FULL_WITH_SECONDS(){return Je}static get DATETIME_HUGE(){return Ye}static get DATETIME_HUGE_WITH_SECONDS(){return Xe}}function Mi(e){if(Ai.isDateTime(e))return e;if(e&&e.valueOf&&Kt(e.valueOf()))return Ai.fromJSDate(e);if(e&&"object"==typeof e)return Ai.fromObject(e);throw new Te(`Unknown datetime argument: ${e}, of type ${typeof e}`)}function Ii(e,t){let n=t.timezone;if(!n&&!t.language)return!1;const r=e.Intl.DateTimeFormat;r&&C(r.prototype,"resolvedOptions",((e,i,o)=>{let a=e.call(i,...o);if(n&&(a.timeZone=n),t.language){let e=t.language.split(",")[0],n=r.supportedLocalesOf(e);a.locale=n[0]||""}return a}));const i=e.Date;if(i){let t=new i,r=new i(t.toLocaleString("en",{timeZone:n})).getTime()-t.getTime();C(i.prototype,"toString",((e,t,r)=>{try{return Ai.fromJSDate(t).setZone(n).toFormat("ccc LLL dd yyyy TT 'GMT'ZZZ (ZZZZZ)")}catch(n){return e.call(t,...r)}}),{skipAbsent:!1});for(let e of["toUTCString","toGMTString","toISOString"])C(i.prototype,e,((e,t,n)=>(t=new i(t.getTime()+r),e.call(t,...n))),{skipAbsent:!1});T(e,"Date",((e,t,n)=>{let i=Reflect.construct(e,t,n);return 1!==t?.length||"number"!=typeof t[0]?i:new e(i.getTime()-r)}),{skipAbsent:!1})}return!0}const xi=/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})/g,Ni=/^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/;function Fi(e,t){let n=e.split(" ");return n.forEach(((e,r)=>{if(!(e.length<7)&&"0.0.0.0"!==e&&"127.0.0.1"!==e)return xi.test(e)||Ni.test(e)?n[r]=t:void 0})),n.join(" ")}function Vi(e,t){if(!t.webrtc)return!1;const n=e.RTCSessionDescription,r=e.RTCIceCandidate;o.ipv4ToIpv6(t.webrtc),n&&v(n.prototype,"sdp",(function(e,n,r){let i=e.call(n,...r);return i?"disable"===t.webrtc?i.replace(xi,""):Fi(i,t.webrtc):i})),r&&(C(r.prototype,"toJSON",(function(e,t,n){let r=e.call(t,...n);return r.candidate=t.candidate,r})),v(r.prototype,"address",(function(e,n,r){let i=e.call(n,...r);return"disable"===t.webrtc?"":t.webrtc?t.webrtc:i})),v(r.prototype,"candidate",(function(e,n,r){let i=e.call(n,...r);return i?"disable"===t.webrtc?i.replace(xi,""):Fi(i,t.webrtc):i})));const i=t.iceServers;return i&&["RTCPeerConnection","mozRTCPeerConnection","webkitRTCPeerConnection"].forEach((t=>{T(e,t,((e,t,n)=>{let r=t[0];return r&&(r.iceServers=i),Reflect.construct(e,t,n)}),{skipAbsent:!0})})),!0}const Li={37445:"UNMASKED_VENDOR_WEBGL",37446:"UNMASKED_RENDERER_WEBGL"};function Ri(e){Object.keys(e).forEach((t=>{try{const n=e[t];"number"==typeof n&&(Li[n]=t)}catch(e){}}))}function Pi(e,t,n,r){let i=t.factors.webgl;const o=t.webglInfo||{},s=Li[n];if(s){const e=o[s];if(void 0!==e)return e}if(i&&"number"==typeof r){let e=new Number(r);return e.toString=function(){return Number.prototype.toString.call(e+i,...arguments)},e.toString[a.native]=1,e}return r}Ri(WebGLRenderingContext.prototype),Ri(WebGL2RenderingContext.prototype);const Wi={"Array.reduce":(e,t,n,r)=>0,"Array.map":(e,t,n,r)=>{let i=e.call(t,...n);return i[0]&&(i[0]=de(i[0],r)),i},"Array.sort":(e,t,n,r)=>""},$i=/gl_FragColor\s*=\s*(.+);/;function ji(e,t){const n=t.factors.webgl;if(!n&&!t.gpu?.vendor&&!t.gpu?.renderer)return!1;t.safeMode;o.randomInt(n,-1e4,1e4);let r="vec4("+o.randomInt(n+1,-50,50)/20+","+o.randomInt(n+2,-50,50)/20+","+o.randomInt(n+3,-50,50)/20+",0)";const i=e=>e&&"string"==typeof e?function(e){const t=e.match($i);if(t){if(t[1].split(/[,+\-*/()]/).map((e=>e.trim())).filter((e=>!isNaN(parseFloat(e)))).length>=3)return e;{const n=`gl_FragColor = ${r} + ${t[1]};`;return e.replace($i,n)}}return e}(e):e,a=e.WebGLRenderingContext;a&&(C(a.prototype,"getParameter",((e,r,i)=>{let o=e.call(r,...i);return o=Pi(0,t,i[0],o)||o,fe(t,o)&&o instanceof Number&&n?de(o,{source:"WebGL.param",path:[],handlers:Wi}):o})),n&&C(a.prototype,"shaderSource",((e,t,n)=>((n=[...n])[1]&&(n[1]=i(n[1])),e.call(t,...n)))));const s=e.WebGL2RenderingContext;return s&&(C(s.prototype,"getParameter",((e,r,i)=>{let o=e.call(r,...i);return o=Pi(0,t,i[0],o)||o,fe(t,o)&&o instanceof Number&&n?de(o,{source:"WebGL2.param",path:[],handlers:Wi}):o})),n&&C(s.prototype,"shaderSource",((e,t,n)=>((n=[...n])[1]&&(n[1]=i(n[1])),e.call(t,...n))))),!0}function zi(e,t){const n=t.factors.webgpu,r=(t.webglInfo||{}).UNMASKED_VENDOR_WEBGL;if(!r&&!n)return!1;const i=e.GPUAdapterInfo,a=e.GPUSupportedLimits;if(i&&r&&v(i.prototype,"vendor",((e,t,n)=>{let i=e.call(t,...n);if(r){let e=(/\(.+\)/.exec(r)||[])[0];return e?e.substring(1,e.length-1).toLowerCase():""}return i})),a&&n){let e=Object.getOwnPropertyDescriptors(a.prototype);Object.keys(e).forEach((t=>{"function"==typeof e[t].get&&v(a.prototype,t,((e,t,r)=>{let i=e.call(t,...r);if(i<=64)return i;let a=2*o.randomInt(n,0,10);return i%2==0?i-(a/2|0):i-a-1}),{skipAbsent:!0})}))}return!0}function _i(e,t){const n=e.Screen;if(!n)return!1;if(!t.screen)return!1;if(!(t.screen.noise||t.screen.width||t.screen.height||t.screen.dpr||t.screen.scheme||t.screen.colorDepth||t.screen.pixelDepth))return!1;t.screen?.dpr&&(O(e.window,"devicePixelRatio",t.screen.dpr),W("resolution",t.screen.dpr+"dppx")),t.screen?.scheme&&W("prefers-color-scheme",t.screen?.scheme),t.screen?.pixelDepth&&v(n.prototype,"pixelDepth",((e,n,r)=>{let i=e.call(n,...r);return t.screen.pixelDepth||i})),t.screen?.colorDepth&&v(n.prototype,"colorDepth",((e,n,r)=>{let i=e.call(n,...r);return t.screen.colorDepth||i}));const r=t.screen.noise;let{width:i,height:a}=e.screen,s=i,l=a;if(r){const e=o.randomInt(r,-50,50);s+=e,l+=e}else{let{width:e,height:n}=t.screen;s=e,l=n}let c=s/i,u=l/a;function d(e,t,n){let r=e.call(t,...n);return c*r|0}function m(e,t,n){let r=e.call(t,...n);return u*r|0}v(n.prototype,"width",((e,t,n)=>e.call(t,...n)*c|0)),v(n.prototype,"height",((e,t,n)=>e.call(t,...n)*u|0)),W("device-width",s+"px"),W("device-height",l+"px"),v(n.prototype,"availWidth",d),v(n.prototype,"availHeight",m),v(e,"outerWidth",d),v(e,"outerHeight",m),v(e,"innerWidth",d),v(e,"innerHeight",m);const h=e.VisualViewport;h&&(v(h.prototype,"width",d),v(h.prototype,"height",m),v(h.prototype,"pageTop",((e,t,n)=>(e.call(t,...n),0))));const f=e.ScreenOrientation;return f&&v(f.prototype,"type",((e,t,n)=>(e.call(t,...n),"landscape-primary"))),!0}function Zi(e,t){const n=t.factors.voice;if(!n)return!1;const r=e.SpeechSynthesis;return!!r&&(C(r.prototype,"getVoices",((e,t,r)=>{let i=e.call(t,...r);if(i instanceof Array){let e,t=-1;o.randomPeek(n,i,((n,r)=>{if(t>=0)return i[t]=n,t=-1,e;t=r,e=i[r]}),1,i.length)}return i}),{skipAbsent:!0}),!0)}const Ui=console.log;function Hi(e,t){(t.customVars||[]).forEach((t=>{let{path:n,value:r}=t,i=n.split("."),s=i.pop();n=i.join(".");let l=o.getValue(e,n);l?(O(l,s,r),console.info(`${e.constructor.name.toUpperCase()} Path:${o.formatString(n,50)} ---MODIFIED`)):e[a.attach].warns.push(`${n} is undefined`)})),(t.customProtos||[]).forEach((t=>{let n=t.name;t.properties.forEach((t=>{let r=t.key,i=t.value;switch(t.type){case"string":i+="";break;case"number":i=+i;break;case"boolean":i=!!i;break;case"json":i=JSON.parse(i);break;case"undefined":i=void 0}let a=o.getValue(e,n);a&&v(a.prototype,r,((e,t,n)=>(e.call(t,...n),i)))}))}))}let Bi=0;function Gi(e,t,n){try{if(e[a.window])return;e[a.SCOPE_BROWSER]=t}catch(e){return Ui(n+" cross domain")}let r=n+"_"+Bi++;const i={iframeCheater:D,nativeCheater:V,workerCheater:x,featureCheater:H,navigatorCheater:M,locationCheater:oe,pluginCheater:ae,screenCheater:_i,mediaCheater:$,clientRectCheater:ie,dateCheater:Ii,audioCheater:se,canvasCheater:be,webglCheater:ji,fontsCheater:re,webgpuCheater:zi,webrtcCheater:Vi,voiceCheater:Zi,elseCheater:Hi,traceCheater:ye};try{let n=e[a.attach]||b(e);const r=t.enables||{};for(let[o,a]of Object.entries(i)){let i=o.replace("Cheater","");i in r&&!r[i]||a(e,t)&&n.cheaters.push(i)}n.isFirst=!1}catch(e){Ui("Injected failed: ",e,n)}Ui(`%c${e.constructor.name.toUpperCase()} HAS CHEATED!!!!!!!!!!!!!!!!!!! FROM ${r||"unknown"}`,"color:green"),e[a.window]=r}function qi(e,t,n){try{if(e.__workerCheat__)return}catch(t){return e[a.attach].warns.push(n+" cross domain")}const r={iframeCheater:D,nativeCheater:V,navigatorCheater:M,locationCheater:oe,pluginCheater:ae,driverCheater:L,featureCheater:H,dateCheater:Ii,webrtcCheater:Vi,webglCheater:ji,webgpuCheater:zi,elseCheater:Hi};try{let n=e[a.attach]||b(e);const i=t.enables||{};for(let[o,a]of Object.entries(r)){let r=o.replace("Cheater","");r in i&&!i[r]||n.cheaters.includes(r)||a(e,t)&&n.isFirst&&n.cheaters.push(r)}n.isFirst=!1}catch(e){Ui("Injected failed: ",e)}Ui(`${e.constructor.name.toUpperCase()} HAS CHEATED!!!!!!!!!!!!!!!!!!! FROM ${n||"unknown"}`),e.__workerCheat__=n}const Ji=self.Date,Yi=console.log;function Xi(){let e=self[a.SCOPE_BROWSER];if(e)return self.top!==self&&self.top||Yi("browser from background"),e;let t=sessionStorage.getItem(a.SCOPE_BROWSER);if(t)try{return e=JSON.parse(t),self[a.SCOPE_BROWSER]=e,self.top!==self&&self.top||Yi("browser from session"),e}catch(e){return}}(()=>{let t;try{t=globalThis}catch(e){}try{t=e.g}catch(e){}const n="Window"===t.constructor.name?Gi:qi;console.clear=()=>{},t[a.SCOPE_CHEATER]=n,n.run=function(){let e=Xi();if(e){if(!e.start){!function(e){if(e.device?.dpr){let t=e.device.dpr.toFixed(2);window.devicePixelRatio.toFixed(2)===t?e.device.dpr=void 0:e.device.dpr=t/1}e.userAgent?e.userAgent.startsWith(navigator.userAgent)?e.uaInfo=void 0:e.appVersion=e.userAgent.replace("Mozilla/",""):e.uaInfo=void 0;const t=e.enables||{};e.functionTrace=e.functionTrace||void 0===t.functionTrace||t.functionTrace;let n=new Ji;if(e.start=n.getTime(),e.language&&(e.languages=(e.language+"").split(",").map((e=>e.split(";")[0])),e.languageCode=e.languages.join(",")),e.timezone){let t=new Ji(n.toLocaleString("en",{timeZone:e.timezone}));e.timezoneOffset=Math.round(t-n)}navigator.userAgent.toLowerCase().includes("firefox")&&(e.memoryCapacity=navigator.deviceMemory),e.getPlatform=t=>{let n=e.uaInfo;if(!n)return t;switch(n.os?.name.toString().toLowerCase()||""){case"ios":return"mobile"===n.device.type?"iPhone":"iPad";case"windows":if(t.toLowerCase().includes("win"))return t}return n.os?.name||t};let r=e.uaInfo;if(r){let t=e.uaInfo?.engine?.name;if(!t)if(/iphone|ipad/i.test(e.userAgent||""))t="Webkit";else{let n=e.uaInfo?.product?.name.toLowerCase()||"";t="firefox"===n?"Gecko":"safari"===n?"Webkit":"Blink"}if(e.uaInfo.engine.name=t,r.engine){let t=r.engine.name;/blink.*/i.test(t)?e.browserVendor="Google Inc.":/webkit.*/i.test(t)?e.browserVendor="Apple Computer, Inc.":/gecko.*/i.test(t)&&(e.browserVendor="")}let n=r.product||r.browser;if(n){if(navigator.userAgentData){let t=[...navigator.userAgentData.brands],r=n.name||"";for(let e in s)if(r.toUpperCase().includes(e.toUpperCase())){let i=[{brand:s[e]+" "+r,version:n.major},{brand:"Not=A?Brand",version:"8"}];t[2]&&i.push({brand:t[2].brand,version:n.major}),t=i;break}e.brands=t}e.uaFullVersion=n.version}let i=()=>{let t=e.brands||[];if(t.length)return t[0].brand?t=t.filter((e=>!/Not/.test(e.brand))).map((e=>`${e.brand} ${e.version}`)):r.product&&(t=t.map((e=>e.toString().startsWith("Not;")?e+" 24":e+" "+r.product.major))),t.length>1&&(t=t.filter((e=>!/Chromium/.test(e)))),t};if(e.brandsVersion=i(),r.device&&(e.mobile="mobile"===r.device.type),e.brands&&(e.fullVersionList=e.brands.map((e=>({brand:e.brand,version:e.version+".0.0.0"})))),(r.cpu||r.os)&&((r.os?.name.toLowerCase()||"").includes("win")?e.arch=o.randomItem(e.seed||0,["x86","x64"]):e.arch=(r.cpu||{}).architecture),e.bitness=(e.arch||"").toString().includes("32")?"32":"64",r.os){let t=r.os.version.split(".");t.length>3?t=t.slice(0,3):t.length<3&&(1===t.length?t.push("0.0"):2===t.length&&t.push("0")),e.platformVersion=t.join(".")}}}(e),console.log("%cInjected:"+e.name,"color: green");try{if(n(t,e,"direct"),!document)return;document.getElementsByTagName("iframe");for(let r=0;r<t.length;r++)n(t[r],e,`self array [${r}]`);e.status="ok"}catch(t){e.status="err",console.error(t)}}}else console.log("There is no injectable information on the current page...")},n.run()})()})();