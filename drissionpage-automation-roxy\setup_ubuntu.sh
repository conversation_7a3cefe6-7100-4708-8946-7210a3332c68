#!/bin/bash

# Ubuntu 环境设置脚本
# 自动安装 Chrome、依赖包和配置环境

set -e  # 遇到错误立即退出

echo "🐧 Ubuntu 环境设置脚本"
echo "======================================"

# 检查是否为 root 用户
if [[ $EUID -eq 0 ]]; then
   echo "⚠️ 请不要以 root 用户运行此脚本"
   echo "💡 使用: bash setup_ubuntu.sh"
   exit 1
fi

# 检查操作系统
if [[ ! -f /etc/os-release ]]; then
    echo "❌ 无法检测操作系统"
    exit 1
fi

source /etc/os-release
echo "🔍 检测到系统: $PRETTY_NAME"

# 更新包列表
echo "📦 更新包列表..."
sudo apt update

# 安装基础依赖
echo "🔧 安装基础依赖..."
sudo apt install -y \
    wget \
    curl \
    gnupg \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    python3 \
    python3-pip \
    python3-venv

# 安装 X11 和虚拟显示器依赖
echo "🖥️ 安装显示器相关依赖..."
sudo apt install -y \
    xvfb \
    xauth \
    x11-utils \
    libxss1 \
    libgconf-2-4 \
    libxrandr2 \
    libasound2 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo-gobject2 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxi6 \
    libxtst6 \
    libnss3 \
    libcups2 \
    libxrandr2 \
    libasound2 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libatspi2.0-0

# 检查 Chrome 是否已安装
if command -v google-chrome &> /dev/null; then
    echo "✅ Google Chrome 已安装"
    google-chrome --version
else
    echo "📥 安装 Google Chrome..."
    
    # 添加 Google Chrome 仓库
    wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
    
    # 更新包列表并安装 Chrome
    sudo apt update
    sudo apt install -y google-chrome-stable
    
    echo "✅ Google Chrome 安装完成"
    google-chrome --version
fi

# 创建 Python 虚拟环境
echo "🐍 设置 Python 环境..."
if [[ ! -d "venv" ]]; then
    python3 -m venv venv
    echo "✅ Python 虚拟环境创建完成"
else
    echo "✅ Python 虚拟环境已存在"
fi

# 激活虚拟环境并安装依赖
echo "📦 安装 Python 依赖..."
source venv/bin/activate
pip install --upgrade pip

# 安装项目依赖
if [[ -f "requirements.txt" ]]; then
    pip install -r requirements.txt
    echo "✅ Python 依赖安装完成"
else
    echo "⚠️ 未找到 requirements.txt，手动安装依赖..."
    pip install DrissionPage>=4.0.0 requests>=2.31.0 python-dotenv>=1.0.0 pyperclip>=1.8.2 PyVirtualDisplay>=3.0
fi

# 配置用户组
echo "👥 配置用户组..."
sudo usermod -a -G video $USER
echo "✅ 用户已添加到 video 组"

# 测试虚拟显示器
echo "🧪 测试虚拟显示器..."
if command -v Xvfb &> /dev/null; then
    echo "✅ Xvfb 可用"
    
    # 测试启动虚拟显示器
    Xvfb :99 -screen 0 1024x768x24 &
    XVFB_PID=$!
    sleep 2
    
    if kill -0 $XVFB_PID 2>/dev/null; then
        echo "✅ 虚拟显示器测试成功"
        kill $XVFB_PID
    else
        echo "⚠️ 虚拟显示器测试失败"
    fi
else
    echo "❌ Xvfb 未安装"
fi

# 运行兼容性测试
echo "🧪 运行兼容性测试..."
if [[ -f "test_ubuntu_compatibility.py" ]]; then
    python test_ubuntu_compatibility.py
else
    echo "⚠️ 未找到测试脚本"
fi

# 创建启动脚本
echo "📝 创建启动脚本..."
cat > run_automation.sh << 'EOF'
#!/bin/bash
# DrissionPage 自动化启动脚本

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export PYTHONPATH=$PWD:$PYTHONPATH

# 运行自动化脚本
python drissionpage_automation.py "$@"
EOF

chmod +x run_automation.sh
echo "✅ 启动脚本创建完成: ./run_automation.sh"

# 显示完成信息
echo ""
echo "🎉 Ubuntu 环境设置完成！"
echo "======================================"
echo ""
echo "📋 安装的组件:"
echo "  ✅ Google Chrome"
echo "  ✅ Python 虚拟环境"
echo "  ✅ 项目依赖包"
echo "  ✅ 虚拟显示器 (Xvfb)"
echo "  ✅ X11 相关依赖"
echo ""
echo "🚀 使用方法:"
echo "  1. 重新登录以应用用户组更改:"
echo "     logout && login"
echo "  2. 或者使用 newgrp 命令:"
echo "     newgrp video"
echo "  3. 运行自动化脚本:"
echo "     ./run_automation.sh"
echo "  4. 或者手动激活环境:"
echo "     source venv/bin/activate"
echo "     python drissionpage_automation.py"
echo ""
echo "🧪 测试命令:"
echo "  python test_enhanced_config.py"
echo "  python test_ubuntu_compatibility.py"
echo ""
echo "📚 文档:"
echo "  UBUNTU_COMPATIBILITY.md - Ubuntu 兼容性指南"
echo "  VIRTUAL_DISPLAY_GUIDE.md - 虚拟显示器使用指南"
echo ""
echo "💡 如果遇到问题，请查看文档或运行测试脚本进行诊断。"
